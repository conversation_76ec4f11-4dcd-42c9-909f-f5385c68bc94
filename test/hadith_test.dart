import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_home_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_detail_screen.dart';

void main() {
  group('Hadith Feature Tests', () {
    testWidgets('HadithDetailScreen displays correctly', (WidgetTester tester) async {
      // Build the HadithDetailScreen widget
      await tester.pumpWidget(
        MaterialApp(
          home: HadithDetailScreen(
            hadithText: 'Test Arabic hadith text',
            narrator: 'Narrated by <PERSON><PERSON>: The Prophet (peace be upon him) said, "The most beloved deed to Allah is the most regular and constant even though it were little."',
            translation: 'The Prophet (peace be upon him) said, "The most beloved deed to <PERSON> is the most regular and constant even though it were little."',
            explanation: 'This hadith emphasizes the importance of consistency in performing good deeds, even if they seem small. Regularity in worship and righteous actions is more pleasing to <PERSON> than sporadic acts of great magnitude.',
            reference: '<PERSON><PERSON><PERSON>, Book of Faith, Hadith 43',
            collection: '<PERSON><PERSON><PERSON>',
          ),
        ),
      );

      // Verify that the screen displays the expected content
      expect(find.text('Hadith'), findsOneWidget);
      expect(find.text('Save'), findsOneWidget);
      expect(find.text('Share'), findsOneWidget);
      expect(find.text('Text'), findsOneWidget);
      expect(find.text('Sahih Bukhari'), findsOneWidget);
      expect(find.text('Translation'), findsOneWidget);
      expect(find.text('Explanation'), findsOneWidget);
      expect(find.text('Reference'), findsOneWidget);
    });

    testWidgets('HadithDetailScreen action buttons work', (WidgetTester tester) async {
      bool savePressed = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: HadithDetailScreen(
            hadithText: 'Test Arabic hadith text',
            narrator: 'Test narrator',
            translation: 'Test translation',
            explanation: 'Test explanation',
            reference: 'Test reference',
            collection: 'Test collection',
          ),
        ),
      );

      // Test save button tap
      await tester.tap(find.text('Save'));
      await tester.pump();

      // Verify the UI updated (bookmark icon should change)
      expect(find.byIcon(Icons.bookmark), findsOneWidget);
    });

    testWidgets('HadithDetailScreen back button works', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => HadithDetailScreen(
                        hadithText: 'Test Arabic hadith text',
                        narrator: 'Test narrator',
                        translation: 'Test translation',
                        explanation: 'Test explanation',
                        reference: 'Test reference',
                        collection: 'Test collection',
                      ),
                    ),
                  );
                },
                child: Text('Open Hadith'),
              ),
            ),
          ),
        ),
      );

      // Navigate to hadith detail screen
      await tester.tap(find.text('Open Hadith'));
      await tester.pumpAndSettle();

      // Verify we're on the hadith detail screen
      expect(find.text('Hadith'), findsOneWidget);

      // Tap back button
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Verify we're back to the original screen
      expect(find.text('Open Hadith'), findsOneWidget);
    });
  });
}
