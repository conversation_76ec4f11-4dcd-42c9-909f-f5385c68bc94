import 'package:flutter/foundation.dart';

class ArabicWord {
  final String id;
  final String word;
  final String transliteration;
  final String meaning;
  final String example;
  final String exampleTranslation;
  final String audioUrl;
  final List<String> relatedWords;
  final DateTime date;
  final bool isFavorite;

  ArabicWord({
    required this.id,
    required this.word,
    required this.transliteration,
    required this.meaning,
    required this.example,
    required this.exampleTranslation,
    this.audioUrl = '',
    this.relatedWords = const [],
    required this.date,
    this.isFavorite = false,
  });

  factory ArabicWord.fromJson(Map<String, dynamic> json) {
    return ArabicWord(
      id: json['id'] as String,
      word: json['word'] as String,
      transliteration: json['transliteration'] as String,
      meaning: json['meaning'] as String,
      example: json['example'] as String,
      exampleTranslation: json['exampleTranslation'] as String,
      audioUrl: json['audioUrl'] as String? ?? '',
      relatedWords: (json['relatedWords'] as List<dynamic>?)?.map((e) => e as String).toList() ?? [],
      date: DateTime.parse(json['date'] as String),
      isFavorite: json['isFavorite'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'word': word,
      'transliteration': transliteration,
      'meaning': meaning,
      'example': example,
      'exampleTranslation': exampleTranslation,
      'audioUrl': audioUrl,
      'relatedWords': relatedWords,
      'date': date.toIso8601String(),
      'isFavorite': isFavorite,
    };
  }

  ArabicWord copyWith({
    String? id,
    String? word,
    String? transliteration,
    String? meaning,
    String? example,
    String? exampleTranslation,
    String? audioUrl,
    List<String>? relatedWords,
    DateTime? date,
    bool? isFavorite,
  }) {
    return ArabicWord(
      id: id ?? this.id,
      word: word ?? this.word,
      transliteration: transliteration ?? this.transliteration,
      meaning: meaning ?? this.meaning,
      example: example ?? this.example,
      exampleTranslation: exampleTranslation ?? this.exampleTranslation,
      audioUrl: audioUrl ?? this.audioUrl,
      relatedWords: relatedWords ?? this.relatedWords,
      date: date ?? this.date,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ArabicWord && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
} 