import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/arabic_word/models/arabic_word.dart';
import 'package:new_islamic_app_uk/features/arabic_word/services/arabic_word_service.dart';
import 'package:share_plus/share_plus.dart';

class WordDetailScreen extends StatefulWidget {
  final ArabicWord word;

  const WordDetailScreen({
    Key? key,
    required this.word,
  }) : super(key: key);

  @override
  State<WordDetailScreen> createState() => _WordDetailScreenState();
}

class _WordDetailScreenState extends State<WordDetailScreen> {
  final ArabicWordService _wordService = ArabicWordService();
  late ArabicWord _word;
  bool _isTextCopied = false;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();
    _word = widget.word;
  }

  Future<void> _toggleFavorite() async {
    final updatedWord = await _wordService.toggleFavorite(_word);
    setState(() {
      _word = updatedWord;
    });
  }

  void _copyWordToClipboard() {
    final String textToCopy = 
        '${_word.word} (${_word.transliteration})\n'
        'Meaning: ${_word.meaning}\n\n'
        'Example: ${_word.example}\n'
        'Translation: ${_word.exampleTranslation}';
    
    Clipboard.setData(ClipboardData(text: textToCopy));
    
    setState(() {
      _isTextCopied = true;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Word copied to clipboard'),
        backgroundColor: Color(URLs().colorScheme),
        duration: Duration(seconds: 2),
      ),
    );
    
    Future.delayed(Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isTextCopied = false;
        });
      }
    });
  }

  void _shareWord() {
    final String textToShare = 
        '${_word.word} (${_word.transliteration})\n'
        'Meaning: ${_word.meaning}\n\n'
        'Example: ${_word.example}\n'
        'Translation: ${_word.exampleTranslation}\n\n'
        'Shared from Masjid Sunnah App';
    
    Share.share(textToShare);
  }

  void _playPronunciation() {
    // In a real app, you would play the audio from _word.audioUrl
    // For this demo, we'll just show a snackbar
    setState(() {
      _isPlaying = true;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Playing pronunciation...'),
        backgroundColor: Color(URLs().colorScheme),
        duration: Duration(seconds: 2),
      ),
    );
    
    Future.delayed(Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isPlaying = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'Arabic Word',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Color(URLs().colorScheme),
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Color(URLs().colorScheme)),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _word.isFavorite ? Icons.favorite : Icons.favorite_border,
              color: _word.isFavorite ? Colors.red : Color(URLs().colorScheme),
            ),
            onPressed: _toggleFavorite,
            tooltip: _word.isFavorite ? 'Remove from favorites' : 'Add to favorites',
          ),
          IconButton(
            icon: Icon(
              _isTextCopied ? Icons.check : Icons.content_copy,
              color: Color(URLs().colorScheme),
            ),
            onPressed: _copyWordToClipboard,
            tooltip: 'Copy word',
          ),
          IconButton(
            icon: Icon(
              Icons.share,
              color: Color(URLs().colorScheme),
            ),
            onPressed: _shareWord,
            tooltip: 'Share word',
          ),
        ],
      ),
      body: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWordCard(),
            SizedBox(height: 24),
            _buildExampleCard(),
            SizedBox(height: 24),
            _buildRelatedWordsCard(),
            SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildWordCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(URLs().colorScheme),
            Color(URLs().colorScheme).withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Color(URLs().colorScheme).withOpacity(0.3),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            _word.word,
            style: GoogleFonts.scheherazadeNew(
              fontSize: 60,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 16),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(30),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Text(
              _word.transliteration,
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ),
          SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                _word.meaning,
                style: GoogleFonts.poppins(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              SizedBox(width: 12),
              IconButton(
                onPressed: _playPronunciation,
                icon: Icon(
                  _isPlaying ? Icons.volume_up : Icons.volume_up_outlined,
                  color: Colors.white,
                  size: 24,
                ),
                tooltip: 'Listen to pronunciation',
                style: IconButton.styleFrom(
                  backgroundColor: Colors.white.withOpacity(0.2),
                  padding: EdgeInsets.all(8),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExampleCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.quoteRight,
                size: 16,
                color: Color(URLs().colorScheme),
              ),
              SizedBox(width: 8),
              Text(
                'Example',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Color(URLs().colorScheme).withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Color(URLs().colorScheme).withOpacity(0.1),
                width: 1,
              ),
            ),
            child: Text(
              _word.example,
              textAlign: TextAlign.center,
              style: GoogleFonts.scheherazadeNew(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
                height: 1.6,
              ),
            ),
          ),
          SizedBox(height: 16),
          Text(
            _word.exampleTranslation,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontStyle: FontStyle.italic,
              color: Colors.grey[700],
              height: 1.6,
            ),
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Icon(
                FontAwesomeIcons.circleInfo,
                size: 14,
                color: Colors.grey[600],
              ),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Try using this word in your daily conversations to improve your Arabic vocabulary.',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRelatedWordsCard() {
    if (_word.relatedWords.isEmpty) {
      return SizedBox();
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.diagramProject,
                size: 16,
                color: Color(URLs().colorScheme),
              ),
              SizedBox(width: 8),
              Text(
                'Related Words',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: _word.relatedWords.map((relatedWord) {
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.grey[300]!,
                    width: 1,
                  ),
                ),
                child: Text(
                  relatedWord,
                  style: GoogleFonts.scheherazadeNew(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[800],
                  ),
                ),
              );
            }).toList(),
          ),
          SizedBox(height: 16),
          Text(
            'Date: ${_word.date.day}/${_word.date.month}/${_word.date.year}',
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
} 