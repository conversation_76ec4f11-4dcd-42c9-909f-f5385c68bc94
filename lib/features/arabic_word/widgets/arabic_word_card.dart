import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/arabic_word/models/arabic_word.dart';
import 'package:new_islamic_app_uk/features/arabic_word/screens/word_detail_screen.dart';
import 'package:new_islamic_app_uk/features/arabic_word/services/arabic_word_service.dart';

class ArabicWordCard extends StatefulWidget {
  const ArabicWordCard({Key? key}) : super(key: key);

  @override
  State<ArabicWordCard> createState() => _ArabicWordCardState();
}

class _ArabicWordCardState extends State<ArabicWordCard> {
  final ArabicWordService _wordService = ArabicWordService();
  bool _isLoading = true;
  ArabicWord? _dailyWord;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();
    _loadDailyWord();
  }

  Future<void> _loadDailyWord() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final word = await _wordService.getDailyWord();
      setState(() {
        _dailyWord = word;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleFavorite() async {
    if (_dailyWord == null) return;

    final updatedWord = await _wordService.toggleFavorite(_dailyWord!);
    setState(() {
      _dailyWord = updatedWord;
    });
  }

  void _playPronunciation() {
    // In a real app, you would play the audio from _dailyWord!.audioUrl
    // For this demo, we'll just show a state change
    setState(() {
      _isPlaying = true;
    });
    
    Future.delayed(Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isPlaying = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            if (_dailyWord != null) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => WordDetailScreen(word: _dailyWord!),
                ),
              );
            }
          },
          child: Padding(
            padding: EdgeInsets.all(16),
            child: _isLoading
                ? _buildLoadingState()
                : _dailyWord == null
                    ? _buildErrorState()
                    : _buildWordContent(),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(
            color: Color(URLs().colorScheme),
          ),
          SizedBox(height: 16),
          Text(
            'Loading word of the day...',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline,
            size: 40,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            'Unable to load word',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
          SizedBox(height: 8),
          TextButton(
            onPressed: _loadDailyWord,
            child: Text('Try Again'),
            style: TextButton.styleFrom(
              foregroundColor: Color(URLs().colorScheme),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWordContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Color(URLs().colorScheme).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.translate,
                color: Color(URLs().colorScheme),
                size: 20,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                'Arabic Word of the Day',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ),
            IconButton(
              icon: Icon(
                _dailyWord!.isFavorite
                    ? Icons.favorite
                    : Icons.favorite_border,
                color: _dailyWord!.isFavorite
                    ? Colors.red
                    : Colors.grey[400],
                size: 22,
              ),
              onPressed: _toggleFavorite,
              tooltip: 'Add to favorites',
            ),
          ],
        ),
        SizedBox(height: 20),
        Center(
          child: Text(
            _dailyWord!.word,
            style: GoogleFonts.scheherazadeNew(
              fontSize: 48,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
        ),
        SizedBox(height: 8),
        Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _dailyWord!.transliteration,
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontStyle: FontStyle.italic,
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(width: 8),
              IconButton(
                onPressed: _playPronunciation,
                icon: Icon(
                  _isPlaying ? Icons.volume_up : Icons.volume_up_outlined,
                  color: Color(URLs().colorScheme),
                  size: 18,
                ),
                tooltip: 'Listen to pronunciation',
                constraints: BoxConstraints(
                  minWidth: 32,
                  minHeight: 32,
                ),
                padding: EdgeInsets.zero,
                iconSize: 18,
              ),
            ],
          ),
        ),
        SizedBox(height: 16),
        Divider(),
        SizedBox(height: 16),
        Row(
          children: [
            Text(
              'Meaning:',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                _dailyWord!.meaning,
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(URLs().colorScheme),
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 16),
        Text(
          'Example:',
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Color(URLs().colorScheme).withOpacity(0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Color(URLs().colorScheme).withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Text(
            _dailyWord!.example,
            textAlign: TextAlign.center,
            style: GoogleFonts.scheherazadeNew(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[800],
            ),
          ),
        ),
        SizedBox(height: 8),
        Text(
          _dailyWord!.exampleTranslation,
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontStyle: FontStyle.italic,
            color: Colors.grey[600],
          ),
        ),
        SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              'Tap to view details',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
            SizedBox(width: 4),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[400],
              size: 12,
            ),
          ],
        ),
      ],
    );
  }
} 