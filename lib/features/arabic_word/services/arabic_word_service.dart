import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:new_islamic_app_uk/features/arabic_word/models/arabic_word.dart';

class ArabicWordService {
  static final ArabicWordService _instance = ArabicWordService._internal();
  
  factory ArabicWordService() {
    return _instance;
  }
  
  ArabicWordService._internal();
  
  final Dio _dio = Dio();
  final String _prefsKey = 'arabic_word_of_day';
  final String _favoritesKey = 'favorite_arabic_words';
  
  // Sample Arabic words for offline use
  final List<Map<String, dynamic>> _sampleWords = [
    {
      'id': '1',
      'word': 'سَلام',
      'transliteration': 'Salām',
      'meaning': 'Peace',
      'example': 'السَّلَامُ عَلَيْكُمْ',
      'exampleTranslation': 'Peace be upon you',
      'audioUrl': '',
      'relatedWords': ['سَلِيم', 'مُسْلِم', 'إسْلام'],
      'date': DateTime.now().toIso8601String(),
      'isFavorite': false,
    },
    {
      'id': '2',
      'word': 'رَحْمَة',
      'transliteration': 'Raḥmah',
      'meaning': 'Mercy',
      'example': 'بِسْمِ اللهِ الرَّحْمٰنِ الرَّحِيمِ',
      'exampleTranslation': 'In the name of Allah, the Most Gracious, the Most Merciful',
      'audioUrl': '',
      'relatedWords': ['رَحِيم', 'رَحْمٰن', 'مَرْحَمَة'],
      'date': DateTime.now().subtract(Duration(days: 1)).toIso8601String(),
      'isFavorite': false,
    },
    {
      'id': '3',
      'word': 'عِلْم',
      'transliteration': 'Ilm',
      'meaning': 'Knowledge',
      'example': 'طَلَبُ الْعِلْمِ فَرِيضَةٌ عَلَى كُلِّ مُسْلِمٍ',
      'exampleTranslation': 'Seeking knowledge is an obligation upon every Muslim',
      'audioUrl': '',
      'relatedWords': ['عَالِم', 'مَعْلُوم', 'تَعْلِيم'],
      'date': DateTime.now().subtract(Duration(days: 2)).toIso8601String(),
      'isFavorite': false,
    },
    {
      'id': '4',
      'word': 'صَبْر',
      'transliteration': 'Ṣabr',
      'meaning': 'Patience',
      'example': 'إِنَّمَا يُوَفَّى الصَّابِرُونَ أَجْرَهُم بِغَيْرِ حِسَابٍ',
      'exampleTranslation': 'Indeed, the patient will be given their reward without account',
      'audioUrl': '',
      'relatedWords': ['صَابِر', 'صَبُور', 'اِصْطِبَار'],
      'date': DateTime.now().subtract(Duration(days: 3)).toIso8601String(),
      'isFavorite': false,
    },
    {
      'id': '5',
      'word': 'شُكْر',
      'transliteration': 'Shukr',
      'meaning': 'Gratitude',
      'example': 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
      'exampleTranslation': 'All praise is due to Allah, Lord of the worlds',
      'audioUrl': '',
      'relatedWords': ['شَاكِر', 'مَشْكُور', 'شَكُور'],
      'date': DateTime.now().subtract(Duration(days: 4)).toIso8601String(),
      'isFavorite': false,
    },
    {
      'id': '6',
      'word': 'تَقْوَى',
      'transliteration': 'Taqwā',
      'meaning': 'God-consciousness, piety',
      'example': 'إِنَّ أَكْرَمَكُمْ عِندَ اللَّهِ أَتْقَاكُمْ',
      'exampleTranslation': 'Indeed, the most noble of you in the sight of Allah is the most righteous of you',
      'audioUrl': '',
      'relatedWords': ['مُتَّقِي', 'تَقِيّ', 'اِتِّقَاء'],
      'date': DateTime.now().subtract(Duration(days: 5)).toIso8601String(),
      'isFavorite': false,
    },
    {
      'id': '7',
      'word': 'إِحْسَان',
      'transliteration': 'Iḥsān',
      'meaning': 'Excellence, perfection',
      'example': 'إِنَّ اللَّهَ يُحِبُّ الْمُحْسِنِينَ',
      'exampleTranslation': 'Indeed, Allah loves those who do good',
      'audioUrl': '',
      'relatedWords': ['مُحْسِن', 'حَسَن', 'تَحْسِين'],
      'date': DateTime.now().subtract(Duration(days: 6)).toIso8601String(),
      'isFavorite': false,
    },
  ];
  
  // Get today's Arabic word
  Future<ArabicWord> getDailyWord() async {
    try {
      // First try to fetch from API (in a real app)
      // final response = await _dio.get('https://api.example.com/daily-arabic-word');
      // if (response.statusCode == 200) {
      //   final wordData = response.data;
      //   final word = ArabicWord.fromJson(wordData);
      //   _saveDailyWord(word);
      //   return word;
      // }
      
      // If API fails or in this demo, use cached or sample data
      final prefs = await SharedPreferences.getInstance();
      final String? cachedData = prefs.getString(_prefsKey);
      
      if (cachedData != null) {
        final Map<String, dynamic> wordData = json.decode(cachedData);
        final DateTime savedDate = DateTime.parse(wordData['date']);
        final DateTime today = DateTime.now();
        
        // If the cached word is from today, use it
        if (savedDate.year == today.year && 
            savedDate.month == today.month && 
            savedDate.day == today.day) {
          return ArabicWord.fromJson(wordData);
        }
      }
      
      // Otherwise, get a new word from sample data
      final int dayOfYear = DateTime.now().difference(DateTime(DateTime.now().year, 1, 1)).inDays;
      final int wordIndex = dayOfYear % _sampleWords.length;
      
      final word = ArabicWord.fromJson(_sampleWords[wordIndex]);
      
      // Update the date to today
      final updatedWord = word.copyWith(date: DateTime.now());
      
      // Save to cache
      _saveDailyWord(updatedWord);
      
      return updatedWord;
    } catch (e) {
      debugPrint('Error getting daily Arabic word: $e');
      
      // Fallback to first sample word if everything fails
      return ArabicWord.fromJson(_sampleWords[0]);
    }
  }
  
  // Save word to local storage
  Future<void> _saveDailyWord(ArabicWord word) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_prefsKey, json.encode(word.toJson()));
    } catch (e) {
      debugPrint('Error saving daily Arabic word: $e');
    }
  }
  
  // Get favorite words
  Future<List<ArabicWord>> getFavoriteWords() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? favoritesJson = prefs.getString(_favoritesKey);
      
      if (favoritesJson == null) {
        return [];
      }
      
      final List<dynamic> favoritesData = json.decode(favoritesJson);
      return favoritesData.map((data) => ArabicWord.fromJson(data)).toList();
    } catch (e) {
      debugPrint('Error getting favorite Arabic words: $e');
      return [];
    }
  }
  
  // Toggle favorite status of a word
  Future<ArabicWord> toggleFavorite(ArabicWord word) async {
    try {
      final List<ArabicWord> favorites = await getFavoriteWords();
      final updatedWord = word.copyWith(isFavorite: !word.isFavorite);
      
      if (updatedWord.isFavorite) {
        // Add to favorites if not already there
        if (!favorites.contains(word)) {
          favorites.add(updatedWord);
        }
      } else {
        // Remove from favorites
        favorites.removeWhere((w) => w.id == word.id);
      }
      
      // Save updated favorites list
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_favoritesKey, json.encode(favorites.map((w) => w.toJson()).toList()));
      
      // If this is today's word, update the cached daily word
      final String? cachedData = prefs.getString(_prefsKey);
      if (cachedData != null) {
        final Map<String, dynamic> cachedWord = json.decode(cachedData);
        if (cachedWord['id'] == word.id) {
          await _saveDailyWord(updatedWord);
        }
      }
      
      return updatedWord;
    } catch (e) {
      debugPrint('Error toggling favorite status: $e');
      return word;
    }
  }
  
  // Get word history (last 7 days)
  Future<List<ArabicWord>> getRecentWords({int limit = 7}) async {
    try {
      // In a real app, you would fetch from an API
      // For this demo, we'll use sample data
      final List<ArabicWord> recentWords = [];
      
      for (int i = 0; i < limit && i < _sampleWords.length; i++) {
        final word = ArabicWord.fromJson(_sampleWords[i]);
        final updatedWord = word.copyWith(
          date: DateTime.now().subtract(Duration(days: i)),
        );
        recentWords.add(updatedWord);
      }
      
      return recentWords;
    } catch (e) {
      debugPrint('Error getting recent Arabic words: $e');
      return [];
    }
  }
  
  // Search words
  Future<List<ArabicWord>> searchWords(String query) async {
    try {
      // In a real app, you would search via an API
      // For this demo, we'll search through sample data
      if (query.isEmpty) {
        return [];
      }
      
      final List<ArabicWord> results = [];
      final String lowercaseQuery = query.toLowerCase();
      
      for (final wordData in _sampleWords) {
        final word = ArabicWord.fromJson(wordData);
        
        if (word.word.toLowerCase().contains(lowercaseQuery) ||
            word.transliteration.toLowerCase().contains(lowercaseQuery) ||
            word.meaning.toLowerCase().contains(lowercaseQuery)) {
          results.add(word);
        }
      }
      
      return results;
    } catch (e) {
      debugPrint('Error searching Arabic words: $e');
      return [];
    }
  }
} 