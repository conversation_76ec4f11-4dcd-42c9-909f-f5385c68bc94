import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';

class PrayerTime {
  final String date;
  final String hijriDate;
  final DateTime? fajr;
  final DateTime? sunrise;
  final DateTime? dhuhr;
  final DateTime? asr;
  final DateTime? maghrib;
  final DateTime? isha;
  final bool isToday;

  PrayerTime({
    required this.date,
    required this.hijriDate,
    this.fajr,
    this.sunrise,
    this.dhuhr,
    this.asr,
    this.maghrib,
    this.isha,
    this.isToday = true,
  });

  // Create an empty prayer time object
  factory PrayerTime.empty({bool isToday = true}) {
    return PrayerTime(
      date: DateTime.now().toString().split(' ')[0],
      hijriDate: '',
      isToday: isToday,
    );
  }

  // Factory constructor to create a PrayerTime from API data
  factory PrayerTime.fromApi(Map<String, dynamic> data) {
    debugPrint('Creating PrayerTime from API data: $data');
    
    // Extract date and Hijri date
    final date = data['date']?.toString() ?? '';
    
    // Try different hijri date formats in the API response
    String hijriDate = '';
    if (data.containsKey('hijri_date_convert') && data['hijri_date_convert'] != null) {
      hijriDate = data['hijri_date_convert'].toString();
    } else if (data.containsKey('hijri_date') && data['hijri_date'] != null) {
      hijriDate = data['hijri_date'].toString();
    } else if (data.containsKey('date_hijri') && data['date_hijri'] != null) {
      hijriDate = data['date_hijri'].toString();
    }
    
    debugPrint('Date: $date, Hijri date: $hijriDate');
    
    // Create a map to store the effective prayer times
    // This will prioritize jamaat changes if they exist
    Map<String, String?> effectiveTimes = {};
    
    // Check if we have jamaat_changes and use those times if available
    if (data.containsKey('jamah_changes') && data['jamah_changes'] is Map) {
      debugPrint('Found jamah_changes in API response');
      final changes = data['jamah_changes'] as Map<String, dynamic>;
      
      // Update with jamaat changes if they exist
      if (changes.containsKey('fajr')) effectiveTimes['fajr'] = changes['fajr']?.toString();
      if (changes.containsKey('dhuhr')) effectiveTimes['dhuhr'] = changes['dhuhr']?.toString();
      if (changes.containsKey('zuhr')) effectiveTimes['dhuhr'] = changes['zuhr']?.toString(); // Alternative spelling
      if (changes.containsKey('asr')) effectiveTimes['asr'] = changes['asr']?.toString();
      if (changes.containsKey('maghrib')) effectiveTimes['maghrib'] = changes['maghrib']?.toString();
      if (changes.containsKey('isha')) effectiveTimes['isha'] = changes['isha']?.toString();
    }
    
    // If no jamaat changes or incomplete, use the main times
    // Only override if we don't already have a value from jamaat_changes
    if (!effectiveTimes.containsKey('fajr') || effectiveTimes['fajr'] == null) 
      effectiveTimes['fajr'] = _parseTimeWithFallback(data, ['fajr', 'fajr_jamah', 'fajr_jamat']);
    
    if (!effectiveTimes.containsKey('dhuhr') || effectiveTimes['dhuhr'] == null) 
      effectiveTimes['dhuhr'] = _parseTimeWithFallback(data, ['dhuhr', 'zuhr', 'dhuhr_jamah', 'zuhr_jamah', 'dhuhr_jamat', 'zuhr_jamat']);
    
    if (!effectiveTimes.containsKey('asr') || effectiveTimes['asr'] == null) 
      effectiveTimes['asr'] = _parseTimeWithFallback(data, ['asr', 'asr_jamah', 'asr_jamat']);
    
    if (!effectiveTimes.containsKey('maghrib') || effectiveTimes['maghrib'] == null) 
      effectiveTimes['maghrib'] = _parseTimeWithFallback(data, ['maghrib', 'maghrib_jamah', 'maghrib_jamat']);
    
    if (!effectiveTimes.containsKey('isha') || effectiveTimes['isha'] == null) 
      effectiveTimes['isha'] = _parseTimeWithFallback(data, ['isha', 'isha_jamah', 'isha_jamat']);
    
    // Parse sunrise separately since it's not typically a Jamaat time
    final sunriseStr = _parseTimeWithFallback(data, ['sunrise', 'shurooq']);
    
    // Debug log the parsed times
    debugPrint('Parsed prayer times:');
    debugPrint(' - Fajr: ${effectiveTimes['fajr']}');
    debugPrint(' - Sunrise: $sunriseStr');
    debugPrint(' - Dhuhr: ${effectiveTimes['dhuhr']}');
    debugPrint(' - Asr: ${effectiveTimes['asr']}');
    debugPrint(' - Maghrib: ${effectiveTimes['maghrib']}');
    debugPrint(' - Isha: ${effectiveTimes['isha']}');
    
    // Create DateTime objects for each prayer time
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    // Parse each prayer time
    DateTime? fajrTime = _parseTimeString(effectiveTimes['fajr'], today);
    DateTime? sunriseTime = _parseTimeString(sunriseStr, today);
    DateTime? dhuhrTime = _parseTimeString(effectiveTimes['dhuhr'], today);
    DateTime? asrTime = _parseTimeString(effectiveTimes['asr'], today);
    DateTime? maghribTime = _parseTimeString(effectiveTimes['maghrib'], today);
    DateTime? ishaTime = _parseTimeString(effectiveTimes['isha'], today);
    
    return PrayerTime(
      date: date,
      hijriDate: hijriDate,
      fajr: fajrTime,
      sunrise: sunriseTime,
      dhuhr: dhuhrTime,
      asr: asrTime,
      maghrib: maghribTime,
      isha: ishaTime,
    );
  }
  
  // Helper method to parse prayer times using different possible keys
  static String? _parseTimeWithFallback(Map<String, dynamic> data, List<String> possibleKeys) {
    for (String key in possibleKeys) {
      if (data.containsKey(key) && data[key] != null && data[key].toString().isNotEmpty) {
        return data[key].toString();
      }
    }
    return null;
  }
  
  // Parse a time string into a DateTime object
  static DateTime? _parseTimeString(String? timeStr, DateTime date) {
    if (timeStr == null || timeStr.isEmpty) {
      return null;
    }
    
    try {
      // Try to handle various time formats
      
      // Format: HH:MM (24-hour)
      final regex24Hour = RegExp(r'^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$');
      final match24Hour = regex24Hour.firstMatch(timeStr);
      if (match24Hour != null) {
        final hour = int.parse(match24Hour.group(1)!);
        final minute = int.parse(match24Hour.group(2)!);
        return DateTime(date.year, date.month, date.day, hour, minute);
      }
      
      // Format: HH:MM AM/PM (12-hour)
      final regex12Hour = RegExp(r'^([0-1]?[0-9]):([0-5][0-9])\s*(am|pm)$', caseSensitive: false);
      final match12Hour = regex12Hour.firstMatch(timeStr);
      if (match12Hour != null) {
        int hour = int.parse(match12Hour.group(1)!);
        final minute = int.parse(match12Hour.group(2)!);
        final amPm = match12Hour.group(3)!.toLowerCase();
        
        // Convert to 24-hour format
        if (amPm == 'pm' && hour < 12) {
          hour += 12;
        } else if (amPm == 'am' && hour == 12) {
          hour = 0;
        }
        
        return DateTime(date.year, date.month, date.day, hour, minute);
      }
      
      // Format: HH:MM:SS (with seconds)
      final regexWithSeconds = RegExp(r'^([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$');
      final matchWithSeconds = regexWithSeconds.firstMatch(timeStr);
      if (matchWithSeconds != null) {
        final hour = int.parse(matchWithSeconds.group(1)!);
        final minute = int.parse(matchWithSeconds.group(2)!);
        return DateTime(date.year, date.month, date.day, hour, minute);
      }
      
      // If none of the regex patterns matched, log and return null
      debugPrint('Could not parse time string: $timeStr');
      return null;
    } catch (e) {
      debugPrint('Error parsing time string "$timeStr": $e');
      return null;
    }
  }
  
  /// Check if this prayer time has any valid prayer times (ignoring sunrise)
  bool hasAnyPrayerTimes() {
    return fajr != null || dhuhr != null || asr != null || maghrib != null || isha != null;
  }
  
  /// Check if we have valid prayer times for most prayers (at least 3 out of 5)
  bool hasValidPrayerTimes() {
    int validCount = 0;
    if (fajr != null) validCount++;
    if (dhuhr != null) validCount++;
    if (asr != null) validCount++;
    if (maghrib != null) validCount++;
    if (isha != null) validCount++;
    
    return validCount >= 3; // At least 3 valid prayers required
  }
  
  /// Gets the formatted time for a prayer time
  String getFormattedTime(DateTime? time) {
    if (time == null) return 'N/A';
    
    try {
      return DateFormat('hh:mm a').format(time);
    } catch (e) {
      // If DateFormat fails, manual formatting as fallback
      final hour = time.hour > 12 ? time.hour - 12 : (time.hour == 0 ? 12 : time.hour);
      final minute = time.minute.toString().padLeft(2, '0');
      final period = time.hour >= 12 ? 'PM' : 'AM';
      return '$hour:$minute $period';
    }
  }
  
  /// Get the next prayer time based on the current time
  DateTime? getNextPrayer(DateTime now) {
    List<DateTime> prayers = [];
    
    // Create a list of available prayers with their times
    if (fajr != null) prayers.add(fajr!);
    // Skip Sunrise as it's not a prayer (don't add it)
    if (dhuhr != null) prayers.add(dhuhr!);
    if (asr != null) prayers.add(asr!);
    if (maghrib != null) prayers.add(maghrib!);
    if (isha != null) prayers.add(isha!);
    
    // Sort prayers by time
    prayers.sort((a, b) => a.compareTo(b));
    
    // Debug the available prayers with names for better logging
    final prayerNames = prayers.map((p) {
      if (p == fajr) return 'Fajr: ${getFormattedTime(p)}';
      if (p == dhuhr) return 'Dhuhr: ${getFormattedTime(p)}';
      if (p == asr) return 'Asr: ${getFormattedTime(p)}';
      if (p == maghrib) return 'Maghrib: ${getFormattedTime(p)}';
      if (p == isha) return 'Isha: ${getFormattedTime(p)}';
      return 'Unknown: ${getFormattedTime(p)}';
    }).join(', ');
    
    debugPrint('Available prayers for today: $prayerNames');
    
    // Find the next prayer after the current time
    for (final prayer in prayers) {
      if (prayer.isAfter(now)) {
        String prayerName = 'Unknown';
        if (prayer == fajr) prayerName = 'Fajr';
        else if (prayer == dhuhr) prayerName = 'Dhuhr';
        else if (prayer == asr) prayerName = 'Asr';
        else if (prayer == maghrib) prayerName = 'Maghrib';
        else if (prayer == isha) prayerName = 'Isha';
        
        debugPrint('Next prayer found: $prayerName at ${getFormattedTime(prayer)}');
        return prayer;
      }
    }
    
    // No next prayer found for today
    debugPrint('No next prayer found for today');
    return null;
  }
  
  /// Get formatted prayer times as a map
  Map<String, String> getFormattedPrayerTimes() {
    return {
      'Fajr': getFormattedTime(fajr),
      'Sunrise': getFormattedTime(sunrise),
      'Dhuhr': getFormattedTime(dhuhr),
      'Asr': getFormattedTime(asr),
      'Maghrib': getFormattedTime(maghrib),
      'Isha': getFormattedTime(isha),
    };
  }
  
  // Get prayer time by name
  DateTime? getPrayerByName(String name) {
    switch (name.toLowerCase()) {
      case 'fajr':
        return fajr;
      case 'sunrise':
        return sunrise;
      case 'zuhr':
      case 'dhuhr':
        return dhuhr;
      case 'asr':
        return asr;
      case 'maghrib':
        return maghrib;
      case 'isha':
        return isha;
      default:
        return null;
    }
  }
  
  @override
  String toString() {
    return 'PrayerTime(date: $date, hijriDate: $hijriDate, fajr: ${getFormattedTime(fajr)}, sunrise: ${getFormattedTime(sunrise)}, dhuhr: ${getFormattedTime(dhuhr)}, asr: ${getFormattedTime(asr)}, maghrib: ${getFormattedTime(maghrib)}, isha: ${getFormattedTime(isha)})';
  }
} 