import 'dart:convert';

enum AdhanOption {
  default<PERSON><PERSON><PERSON>('<PERSON><PERSON><PERSON> <PERSON><PERSON>'),
  f<PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON><PERSON><PERSON>'),
  me<PERSON><PERSON><PERSON><PERSON>('Mecca Adhan'),
  <PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON><PERSON>'),
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON><PERSON><PERSON><PERSON><PERSON>'),
  shortNotification('Short Notification'),
  vibrationOnly('Vibration Only'),
  silentNotification('Silent');

  final String name;
  const AdhanOption(this.name);

  factory AdhanOption.fromString(String value) {
    return AdhanOption.values.firstWhere(
      (e) => e.name.toLowerCase() == value.toLowerCase(),
      orElse: () => AdhanOption.defaultAdhan,
    );
  }
}

class PrayerSettings {
  final bool masterEnabled;
  final Map<String, bool> prayerEnabled;
  final Map<String, int> reminderMinutes;
  final Map<String, AdhanOption> adhanOptions;
  final bool showOnLockScreen;
  final bool showInNotificationCenter;
  final bool vibrate;
  final bool useHighPriorityNotifications;

  PrayerSettings({
    this.masterEnabled = true,
    Map<String, bool>? prayerEnabled,
    Map<String, int>? reminderMinutes,
    Map<String, AdhanOption>? adhanOptions,
    this.showOnLockScreen = true,
    this.showInNotificationCenter = true,
    this.vibrate = true,
    this.useHighPriorityNotifications = true,
  }) : 
    prayerEnabled = prayerEnabled ?? {
      'Fajr': true,
      'Sunrise': false,
      'Dhuhr': true,
      'Asr': true,
      'Maghrib': true,
      'Isha': true,
    },
    reminderMinutes = reminderMinutes ?? {
      'Fajr': 30,
      'Sunrise': 10,
      'Dhuhr': 15,
      'Asr': 15,
      'Maghrib': 10,
      'Isha': 15,
    },
    adhanOptions = adhanOptions ?? {
      'Fajr': AdhanOption.fajrAdhan,
      'Sunrise': AdhanOption.shortNotification,
      'Dhuhr': AdhanOption.defaultAdhan,
      'Asr': AdhanOption.defaultAdhan,
      'Maghrib': AdhanOption.defaultAdhan,
      'Isha': AdhanOption.defaultAdhan,
    };

  // Create a copy with updated values
  PrayerSettings copyWith({
    bool? masterEnabled,
    Map<String, bool>? prayerEnabled,
    Map<String, int>? reminderMinutes,
    Map<String, AdhanOption>? adhanOptions,
    bool? showOnLockScreen,
    bool? showInNotificationCenter,
    bool? vibrate,
    bool? useHighPriorityNotifications,
  }) {
    return PrayerSettings(
      masterEnabled: masterEnabled ?? this.masterEnabled,
      prayerEnabled: prayerEnabled ?? this.prayerEnabled,
      reminderMinutes: reminderMinutes ?? this.reminderMinutes,
      adhanOptions: adhanOptions ?? this.adhanOptions,
      showOnLockScreen: showOnLockScreen ?? this.showOnLockScreen,
      showInNotificationCenter: showInNotificationCenter ?? this.showInNotificationCenter,
      vibrate: vibrate ?? this.vibrate,
      useHighPriorityNotifications: useHighPriorityNotifications ?? this.useHighPriorityNotifications,
    );
  }

  // Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'masterEnabled': masterEnabled,
      'prayerEnabled': prayerEnabled,
      'reminderMinutes': reminderMinutes,
      'adhanOptions': adhanOptions.map((key, value) => MapEntry(key, value.name)),
      'showOnLockScreen': showOnLockScreen,
      'showInNotificationCenter': showInNotificationCenter,
      'vibrate': vibrate,
      'useHighPriorityNotifications': useHighPriorityNotifications,
    };
  }

  // Create from JSON
  factory PrayerSettings.fromJson(Map<String, dynamic> json) {
    return PrayerSettings(
      masterEnabled: json['masterEnabled'] ?? true,
      prayerEnabled: Map<String, bool>.from(json['prayerEnabled'] ?? {}),
      reminderMinutes: Map<String, int>.from(json['reminderMinutes'] ?? {}),
      adhanOptions: (json['adhanOptions'] as Map<String, dynamic>?)?.map(
        (key, value) => MapEntry(key, AdhanOption.fromString(value)),
      ) ?? {},
      showOnLockScreen: json['showOnLockScreen'] ?? true,
      showInNotificationCenter: json['showInNotificationCenter'] ?? true,
      vibrate: json['vibrate'] ?? true,
      useHighPriorityNotifications: json['useHighPriorityNotifications'] ?? true,
    );
  }

  // Create from shared preferences string
  factory PrayerSettings.fromPrefs(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return PrayerSettings.fromJson(json);
    } catch (e) {
      print('Error parsing prayer settings: $e');
      return PrayerSettings();
    }
  }

  // Default settings
  factory PrayerSettings.defaultSettings() {
    return PrayerSettings();
  }
} 