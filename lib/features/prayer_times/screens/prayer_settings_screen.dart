import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/prayer_times/models/prayer_settings.dart';
import 'package:new_islamic_app_uk/features/prayer_times/screens/notification_history_screen.dart';
import 'package:new_islamic_app_uk/features/prayer_times/services/prayer_notification_service.dart';
import 'package:new_islamic_app_uk/features/prayer_times/widgets/adhan_selection_dialog.dart';
import 'package:new_islamic_app_uk/features/prayer_times/widgets/reminder_time_dialog.dart';

class PrayerSettingsScreen extends StatefulWidget {
  const PrayerSettingsScreen({Key? key}) : super(key: key);

  @override
  State<PrayerSettingsScreen> createState() => _PrayerSettingsScreenState();
}

class _PrayerSettingsScreenState extends State<PrayerSettingsScreen> {
  final PrayerNotificationService _notificationService = PrayerNotificationService();
  late PrayerSettings _settings;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _settings = await _notificationService.getSettings();
    } catch (e) {
      _settings = PrayerSettings.defaultSettings();
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _notificationService.saveSettings(_settings);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to save settings: $e')),
      );
    }

    setState(() {
      _isLoading = false;
    });
  }

  void _toggleMasterEnabled(bool value) {
    setState(() {
      _settings = _settings.copyWith(masterEnabled: value);
    });
    _saveSettings();
  }

  void _togglePrayerEnabled(String prayerName, bool value) {
    final newPrayerEnabled = Map<String, bool>.from(_settings.prayerEnabled);
    newPrayerEnabled[prayerName] = value;
    
    setState(() {
      _settings = _settings.copyWith(prayerEnabled: newPrayerEnabled);
    });
    _saveSettings();
  }

  Future<void> _selectReminderTime(String prayerName) async {
    final currentMinutes = _settings.reminderMinutes[prayerName] ?? 15;
    
    final selectedMinutes = await showDialog<int>(
      context: context,
      builder: (context) => ReminderTimeDialog(
        prayerName: prayerName,
        currentMinutes: currentMinutes,
      ),
    );
    
    if (selectedMinutes != null) {
      final newReminderMinutes = Map<String, int>.from(_settings.reminderMinutes);
      newReminderMinutes[prayerName] = selectedMinutes;
      
      setState(() {
        _settings = _settings.copyWith(reminderMinutes: newReminderMinutes);
      });
      _saveSettings();
    }
  }

  Future<void> _selectAdhanOption(String prayerName) async {
    final currentOption = _settings.adhanOptions[prayerName] ?? AdhanOption.defaultAdhan;
    
    final selectedOption = await showDialog<AdhanOption>(
      context: context,
      builder: (context) => AdhanSelectionDialog(
        prayerName: prayerName,
        currentOption: currentOption,
      ),
    );
    
    if (selectedOption != null) {
      final newAdhanOptions = Map<String, AdhanOption>.from(_settings.adhanOptions);
      newAdhanOptions[prayerName] = selectedOption;
      
      setState(() {
        _settings = _settings.copyWith(adhanOptions: newAdhanOptions);
      });
      _saveSettings();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'Prayer Notifications',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Color(URLs().colorScheme),
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Color(URLs().colorScheme)),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.history, color: Color(URLs().colorScheme)),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => NotificationHistoryScreen(),
                ),
              );
            },
            tooltip: 'Notification History',
          ),
          IconButton(
            icon: Icon(Icons.refresh, color: Color(URLs().colorScheme)),
            onPressed: () {
              _loadSettings();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Refreshing settings...'),
                  backgroundColor: Color(URLs().colorScheme),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          _isLoading
              ? Center(child: CircularProgressIndicator(color: Color(URLs().colorScheme)))
              : SingleChildScrollView(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildMasterSwitch(),
                      SizedBox(height: 24),
                      Text(
                        'Prayer Notifications',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[800],
                        ),
                      ),
                      SizedBox(height: 16),
                      _buildPrayerCard('Fajr'),
                      _buildPrayerCard('Sunrise'),
                      _buildPrayerCard('Dhuhr'),
                      _buildPrayerCard('Asr'),
                      _buildPrayerCard('Maghrib'),
                      _buildPrayerCard('Isha'),
                      SizedBox(height: 24),
                      _buildAdvancedSettings(),
                      SizedBox(height: 24),
                      _buildAboutSection(),
                    ],
                  ),
                ),
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final action = await showDialog<String>(
            context: context,
            builder: (context) => AlertDialog(
              title: Text('Test Notifications'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Choose an action to test:'),
                  SizedBox(height: 16),
                  ListTile(
                    leading: Icon(Icons.notifications_active),
                    title: Text('Send Test Notification'),
                    onTap: () => Navigator.pop(context, 'test'),
                  ),
                  ListTile(
                    leading: Icon(Icons.schedule),
                    title: Text('Schedule All Notifications'),
                    onTap: () => Navigator.pop(context, 'schedule'),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text('Cancel'),
                ),
              ],
            ),
          );
          
          if (action == 'test') {
            await _notificationService.createTestNotification();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Test notification sent'),
                backgroundColor: Color(URLs().colorScheme),
              ),
            );
          } else if (action == 'schedule') {
            await _notificationService.scheduleAllNotifications();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('All notifications scheduled'),
                backgroundColor: Color(URLs().colorScheme),
              ),
            );
          }
        },
        backgroundColor: Color(URLs().colorScheme),
        child: Icon(Icons.notifications_active),
        tooltip: 'Test Notifications',
      ),
    );
  }

  Widget _buildMasterSwitch() {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(URLs().colorScheme),
            Color(URLs().colorScheme).withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Color(URLs().colorScheme).withOpacity(0.3),
            blurRadius: 15,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Icon(
                  FontAwesomeIcons.bell,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Prayer Reminders',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      _settings.masterEnabled 
                          ? 'Notifications are enabled' 
                          : 'Notifications are disabled',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
              Switch(
                value: _settings.masterEnabled,
                onChanged: _toggleMasterEnabled,
                activeColor: Colors.white,
                activeTrackColor: Colors.white.withOpacity(0.3),
                inactiveThumbColor: Colors.white.withOpacity(0.8),
                inactiveTrackColor: Colors.white.withOpacity(0.2),
              ),
            ],
          ),
          SizedBox(height: 16),
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  FontAwesomeIcons.circleInfo,
                  color: Colors.white,
                  size: 16,
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Enable or disable all prayer notifications with a single toggle',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 400.ms).slideY(begin: 0.1, end: 0);
  }

  Widget _buildPrayerCard(String prayerName) {
    final isEnabled = _settings.prayerEnabled[prayerName] ?? false;
    final reminderMinutes = _settings.reminderMinutes[prayerName] ?? 15;
    final adhanOption = _settings.adhanOptions[prayerName] ?? AdhanOption.defaultAdhan;
    
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: isEnabled && _settings.masterEnabled
                      ? Color(URLs().colorScheme).withOpacity(0.1)
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  _getIconForPrayer(prayerName),
                  size: 20,
                  color: isEnabled && _settings.masterEnabled
                      ? Color(URLs().colorScheme)
                      : Colors.grey[400],
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  '$prayerName Prayer',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[800],
                  ),
                ),
              ),
              Switch(
                value: isEnabled && _settings.masterEnabled,
                onChanged: _settings.masterEnabled
                    ? (value) => _togglePrayerEnabled(prayerName, value)
                    : null,
                activeColor: Color(URLs().colorScheme),
              ),
            ],
          ),
          
          if (isEnabled && _settings.masterEnabled) ...[
            SizedBox(height: 16),
            
            // Reminder time
            InkWell(
              onTap: () => _selectReminderTime(prayerName),
              borderRadius: BorderRadius.circular(8),
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 20,
                      color: Color(URLs().colorScheme),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Reminder Time',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[800],
                            ),
                          ),
                          Text(
                            '$reminderMinutes minutes before prayer',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Colors.grey[400],
                    ),
                  ],
                ),
              ),
            ),
            
            Divider(),
            
            // Adhan option
            InkWell(
              onTap: () => _selectAdhanOption(prayerName),
              borderRadius: BorderRadius.circular(8),
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    Icon(
                      Icons.music_note,
                      size: 20,
                      color: Color(URLs().colorScheme),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Adhan Sound',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[800],
                            ),
                          ),
                          Text(
                            adhanOption.name,
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Colors.grey[400],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    ).animate().fadeIn(duration: 400.ms, delay: 100.ms);
  }

  Widget _buildAdvancedSettings() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Advanced Settings',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 16),
          
          // Show on lock screen
          SwitchListTile(
            title: Text(
              'Show on Lock Screen',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[800],
              ),
            ),
            subtitle: Text(
              'Display notifications on the lock screen',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            value: _settings.showOnLockScreen,
            onChanged: (value) {
              setState(() {
                _settings = _settings.copyWith(showOnLockScreen: value);
              });
              _saveSettings();
            },
            activeColor: Color(URLs().colorScheme),
          ),
          
          Divider(),
          
          // Show in notification center
          SwitchListTile(
            title: Text(
              'Show in Notification Center',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[800],
              ),
            ),
            subtitle: Text(
              'Display notifications in the notification center',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            value: _settings.showInNotificationCenter,
            onChanged: (value) {
              setState(() {
                _settings = _settings.copyWith(showInNotificationCenter: value);
              });
              _saveSettings();
            },
            activeColor: Color(URLs().colorScheme),
          ),
          
          Divider(),
          
          // Vibrate
          SwitchListTile(
            title: Text(
              'Vibrate',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[800],
              ),
            ),
            subtitle: Text(
              'Vibrate when notifications are received',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            value: _settings.vibrate,
            onChanged: (value) {
              setState(() {
                _settings = _settings.copyWith(vibrate: value);
              });
              _saveSettings();
            },
            activeColor: Color(URLs().colorScheme),
          ),
          
          Divider(),
          
          // High priority notifications
          SwitchListTile(
            title: Text(
              'High Priority Notifications',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[800],
              ),
            ),
            subtitle: Text(
              'Wake screen and show full-screen notifications',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            value: _settings.useHighPriorityNotifications,
            onChanged: (value) {
              setState(() {
                _settings = _settings.copyWith(useHighPriorityNotifications: value);
              });
              _saveSettings();
            },
            activeColor: Color(URLs().colorScheme),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 400.ms, delay: 200.ms);
  }

  Widget _buildAboutSection() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'About Prayer Reminders',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Prayer reminders will notify you before each prayer time. You can customize when to be notified and what adhan sound to play for each prayer.',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 16),
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.orange[200]!,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.orange[700],
                  size: 20,
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'For reliable notifications, please ensure that battery optimization is disabled for this app in your device settings.',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.orange[700],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 400.ms, delay: 300.ms);
  }
  
  // Helper method to get the appropriate icon for a prayer
  IconData _getIconForPrayer(String prayerName) {
    switch (prayerName) {
      case 'Fajr':
        return FontAwesomeIcons.sun;
      case 'Sunrise':
        return FontAwesomeIcons.solidSun;
      case 'Dhuhr':
        return FontAwesomeIcons.sun;
      case 'Asr':
        return FontAwesomeIcons.cloudSun;
      case 'Maghrib':
        return FontAwesomeIcons.solidMoon;
      case 'Isha':
        return FontAwesomeIcons.moon;
      default:
        return Icons.access_time_rounded;
    }
  }
} 