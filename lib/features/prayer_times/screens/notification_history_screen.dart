import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/prayer_times/services/prayer_notification_service.dart';
import 'package:flutter_animate/flutter_animate.dart';

class NotificationHistoryScreen extends StatefulWidget {
  const NotificationHistoryScreen({Key? key}) : super(key: key);

  @override
  State<NotificationHistoryScreen> createState() => _NotificationHistoryScreenState();
}

class _NotificationHistoryScreenState extends State<NotificationHistoryScreen> {
  final PrayerNotificationService _notificationService = PrayerNotificationService();
  List<Map<String, dynamic>> _notificationHistory = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadNotificationHistory();
  }

  Future<void> _loadNotificationHistory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final history = await _notificationService.getNotificationHistory();
      
      // Sort by timestamp (newest first)
      history.sort((a, b) => (b['timestamp'] as int).compareTo(a['timestamp'] as int));
      
      setState(() {
        _notificationHistory = history;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading notification history: $e');
      setState(() {
        _notificationHistory = [];
        _isLoading = false;
      });
    }
  }

  Future<void> _clearHistory() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Clear History'),
        content: Text('Are you sure you want to clear all notification history?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text('Clear'),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      await _notificationService.clearNotificationHistory();
      _loadNotificationHistory();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Notification history cleared'),
          backgroundColor: Color(URLs().colorScheme),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'Notification History',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Color(URLs().colorScheme),
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Color(URLs().colorScheme)),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh, color: Color(URLs().colorScheme)),
            onPressed: _loadNotificationHistory,
            tooltip: 'Refresh',
          ),
          IconButton(
            icon: Icon(Icons.delete_outline, color: Color(URLs().colorScheme)),
            onPressed: _clearHistory,
            tooltip: 'Clear History',
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator(color: Color(URLs().colorScheme)))
          : _notificationHistory.isEmpty
              ? _buildEmptyState()
              : _buildNotificationList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_off_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            'No Notification History',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Your notification history will appear here',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadNotificationHistory,
            icon: Icon(Icons.refresh),
            label: Text('Refresh'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(URLs().colorScheme),
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationList() {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: _notificationHistory.length,
      itemBuilder: (context, index) {
        final notification = _notificationHistory[index];
        final timestamp = notification['timestamp'] as int;
        final scheduledTime = notification['scheduledTime'] as int?;
        final title = notification['title'] as String? ?? 'Unknown';
        final body = notification['body'] as String? ?? '';
        final action = notification['action'] as String? ?? '';
        
        final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
        final formattedDate = DateFormat('MMM d, yyyy').format(date);
        final formattedTime = DateFormat('h:mm a').format(date);
        
        String? scheduledTimeFormatted;
        if (scheduledTime != null) {
          final scheduledDateTime = DateTime.fromMillisecondsSinceEpoch(scheduledTime);
          scheduledTimeFormatted = DateFormat('MMM d, h:mm a').format(scheduledDateTime);
        }
        
        return Container(
          margin: EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: Offset(0, 3),
              ),
            ],
          ),
          child: ListTile(
            contentPadding: EdgeInsets.all(16),
            leading: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Color(URLs().colorScheme).withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Icon(
                  _getIconForNotification(title, action),
                  color: Color(URLs().colorScheme),
                  size: 24,
                ),
              ),
            ),
            title: Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 4),
                Text(
                  body,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                ),
                SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 14,
                      color: Colors.grey[500],
                    ),
                    SizedBox(width: 4),
                    Text(
                      '$formattedDate at $formattedTime',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
                if (scheduledTimeFormatted != null) ...[
                  SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        size: 14,
                        color: Colors.grey[500],
                      ),
                      SizedBox(width: 4),
                      Text(
                        'Scheduled for $scheduledTimeFormatted',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ],
                if (action.isNotEmpty) ...[
                  SizedBox(height: 4),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: _getActionColor(action).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getActionText(action),
                      style: GoogleFonts.poppins(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: _getActionColor(action),
                      ),
                    ),
                  ),
                ],
              ],
            ),
            isThreeLine: true,
          ),
        ).animate().fadeIn(duration: 300.ms, delay: (index * 50).ms).slideY(begin: 0.1, end: 0);
      },
    );
  }
  
  IconData _getIconForNotification(String title, String action) {
    if (action == 'MARK_AS_PRAYED') {
      return Icons.check_circle;
    } else if (action == 'SNOOZE') {
      return Icons.snooze;
    } else if (title.contains('Test')) {
      return Icons.notifications_active;
    } else if (title.contains('Fajr')) {
      return Icons.wb_sunny;
    } else if (title.contains('Dhuhr')) {
      return Icons.wb_sunny;
    } else if (title.contains('Asr')) {
      return Icons.wb_cloudy;
    } else if (title.contains('Maghrib')) {
      return Icons.nights_stay;
    } else if (title.contains('Isha')) {
      return Icons.nightlight_round;
    } else {
      return Icons.notifications;
    }
  }
  
  String _getActionText(String action) {
    switch (action) {
      case 'MARK_AS_PRAYED':
        return 'Marked as Prayed';
      case 'SNOOZE':
        return 'Snoozed';
      default:
        return action;
    }
  }
  
  Color _getActionColor(String action) {
    switch (action) {
      case 'MARK_AS_PRAYED':
        return Colors.green;
      case 'SNOOZE':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
} 