import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/prayer_times/models/prayer_settings.dart';

class AdhanSelectionDialog extends StatefulWidget {
  final String prayerName;
  final AdhanOption currentOption;

  const AdhanSelectionDialog({
    Key? key,
    required this.prayerName,
    required this.currentOption,
  }) : super(key: key);

  @override
  State<AdhanSelectionDialog> createState() => _AdhanSelectionDialogState();
}

class _AdhanSelectionDialogState extends State<AdhanSelectionDialog> {
  late AdhanOption _selectedOption;

  @override
  void initState() {
    super.initState();
    _selectedOption = widget.currentOption;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'Select Adhan for ${widget.prayerName}',
        style: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Colors.grey[800],
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildOptionTile(AdhanOption.defaultAdhan),
            _buildOptionTile(AdhanOption.fajrAdhan),
            _buildOptionTile(AdhanOption.meccaAdhan),
            _buildOptionTile(AdhanOption.madinaAdhan),
            _buildOptionTile(AdhanOption.alAqsaAdhan),
            _buildOptionTile(AdhanOption.shortNotification),
            _buildOptionTile(AdhanOption.vibrationOnly),
            _buildOptionTile(AdhanOption.silentNotification),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: GoogleFonts.poppins(
              color: Colors.grey[700],
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () => Navigator.pop(context, _selectedOption),
          style: ElevatedButton.styleFrom(
            backgroundColor: Color(URLs().colorScheme),
          ),
          child: Text(
            'Save',
            style: GoogleFonts.poppins(
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOptionTile(AdhanOption option) {
    final isSelected = _selectedOption == option;
    
    return RadioListTile<AdhanOption>(
      title: Text(
        option.name,
        style: GoogleFonts.poppins(
          fontSize: 14,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          color: isSelected ? Color(URLs().colorScheme) : Colors.grey[800],
        ),
      ),
      subtitle: _getSubtitle(option),
      value: option,
      groupValue: _selectedOption,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedOption = value;
          });
        }
      },
      activeColor: Color(URLs().colorScheme),
      contentPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 4),
      dense: true,
    );
  }
  
  Widget? _getSubtitle(AdhanOption option) {
    String? subtitleText;
    
    switch (option) {
      case AdhanOption.defaultAdhan:
        subtitleText = 'Standard adhan sound';
        break;
      case AdhanOption.fajrAdhan:
        subtitleText = 'Special adhan for Fajr prayer';
        break;
      case AdhanOption.meccaAdhan:
        subtitleText = 'Adhan from Masjid al-Haram, Mecca';
        break;
      case AdhanOption.madinaAdhan:
        subtitleText = 'Adhan from Masjid an-Nabawi, Madina';
        break;
      case AdhanOption.alAqsaAdhan:
        subtitleText = 'Adhan from Masjid al-Aqsa, Jerusalem';
        break;
      case AdhanOption.shortNotification:
        subtitleText = 'Brief notification sound';
        break;
      case AdhanOption.vibrationOnly:
        subtitleText = 'Phone will only vibrate, no sound';
        break;
      case AdhanOption.silentNotification:
        subtitleText = 'Silent notification, no sound or vibration';
        break;
    }
    
    if (subtitleText != null) {
      return Text(
        subtitleText,
        style: GoogleFonts.poppins(
          fontSize: 12,
          color: Colors.grey[600],
        ),
      );
    }
    
    return null;
  }
} 