import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';

class ReminderTimeDialog extends StatefulWidget {
  final String prayerName;
  final int currentMinutes;

  const ReminderTimeDialog({
    Key? key,
    required this.prayerName,
    required this.currentMinutes,
  }) : super(key: key);

  @override
  State<ReminderTimeDialog> createState() => _ReminderTimeDialogState();
}

class _ReminderTimeDialogState extends State<ReminderTimeDialog> {
  late int _selectedMinutes;
  final List<int> _presetTimes = [5, 10, 15, 20, 30, 45, 60];
  
  @override
  void initState() {
    super.initState();
    _selectedMinutes = widget.currentMinutes;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'Reminder Time for ${widget.prayerName}',
        style: GoogleFonts.poppins(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Colors.grey[800],
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'How many minutes before prayer time would you like to be notified?',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _presetTimes.map((minutes) => _buildTimeChip(minutes)).toList(),
          ),
          SizedBox(height: 16),
          Text(
            'Custom time (minutes):',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Slider(
                  value: _selectedMinutes.toDouble(),
                  min: 1,
                  max: 120,
                  divisions: 119,
                  label: '${_selectedMinutes.round()} min',
                  onChanged: (value) {
                    setState(() {
                      _selectedMinutes = value.round();
                    });
                  },
                  activeColor: Color(URLs().colorScheme),
                ),
              ),
              Container(
                width: 60,
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Color(URLs().colorScheme).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Color(URLs().colorScheme).withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  '$_selectedMinutes',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(URLs().colorScheme),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: GoogleFonts.poppins(
              color: Colors.grey[700],
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () => Navigator.pop(context, _selectedMinutes),
          style: ElevatedButton.styleFrom(
            backgroundColor: Color(URLs().colorScheme),
          ),
          child: Text(
            'Save',
            style: GoogleFonts.poppins(
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeChip(int minutes) {
    final isSelected = _selectedMinutes == minutes;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedMinutes = minutes;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Color(URLs().colorScheme) : Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? Color(URLs().colorScheme) : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Text(
          '$minutes min',
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
            color: isSelected ? Colors.white : Colors.grey[800],
          ),
        ),
      ),
    );
  }
} 