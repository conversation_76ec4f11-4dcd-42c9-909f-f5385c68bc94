import 'dart:convert';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:flutter/material.dart';
import 'package:flutter_native_timezone/flutter_native_timezone.dart';
import 'package:intl/intl.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/prayer_times/models/prayer_settings.dart';
import 'package:new_islamic_app_uk/features/prayer_times/models/prayer_time.dart';
import 'package:new_islamic_app_uk/features/prayer_times/services/prayer_api_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PrayerNotificationService {
  static final PrayerNotificationService _instance = PrayerNotificationService._internal();
  
  factory PrayerNotificationService() {
    return _instance;
  }
  
  PrayerNotificationService._internal();
  
  final PrayerApiService _apiService = PrayerApiService();
  
  // Notification channel keys
  static const String prayerChannelKey = 'prayer_notifications';
  static const String prayerChannelName = 'Prayer Notifications';
  static const String prayerChannelDescription = 'Notifications for prayer times';
  
  // Notification IDs for each prayer
  static const Map<String, int> notificationIds = {
    'Fajr': 1,
    'Sunrise': 2,
    'Dhuhr': 3,
    'Asr': 4,
    'Maghrib': 5,
    'Isha': 6,
  };
  
  // Initialize the notification service
  Future<bool> initialize() async {
    try {
      // Initialize time zone
      final String timeZoneName = await FlutterNativeTimezone.getLocalTimezone();
      
      // Set localization
      await AwesomeNotifications().setLocalization(
        languageCode: 'en',
      );
      
      // Initialize Awesome Notifications
      await AwesomeNotifications().initialize(
        'resource://drawable/ic_notification',
        [
          NotificationChannel(
            channelGroupKey: 'prayer_time_group',
            channelKey: prayerChannelKey,
            channelName: prayerChannelName,
            channelDescription: prayerChannelDescription,
            defaultColor: Color(URLs().colorScheme),
            ledColor: Colors.white,
            importance: NotificationImportance.High,
            playSound: true,
            enableVibration: true,
            soundSource: 'resource://raw/notification_sound',
            channelShowBadge: true,
          ),
        ],
        debug: true,
      );
      
      // Request notification permissions
      final isAllowed = await AwesomeNotifications().isNotificationAllowed();
      if (!isAllowed) {
        await AwesomeNotifications().requestPermissionToSendNotifications();
      }
      
      // Cancel any existing notifications before scheduling new ones
      await cancelAllNotifications();
      
      // Schedule notifications for today
      await scheduleAllNotifications();
      
      debugPrint('Prayer notification service initialized successfully');
      return true;
    } catch (e) {
      debugPrint('Error initializing prayer notification service: $e');
      return false;
    }
  }
  
  // Get user settings from shared preferences
  Future<PrayerSettings> getSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? settingsJson = prefs.getString('prayer_settings');
      
      if (settingsJson == null) {
        return PrayerSettings.defaultSettings();
      }
      
      return PrayerSettings.fromPrefs(settingsJson);
    } catch (e) {
      debugPrint('Error getting prayer settings: $e');
      return PrayerSettings.defaultSettings();
    }
  }
  
  // Save user settings to shared preferences
  Future<bool> saveSettings(PrayerSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('prayer_settings', jsonEncode(settings.toJson()));
      
      // Reschedule notifications with new settings
      await scheduleAllNotifications();
      return true;
    } catch (e) {
      debugPrint('Error saving prayer settings: $e');
      return false;
    }
  }
  
  // Schedule notifications for all enabled prayers
  Future<void> scheduleAllNotifications() async {
    try {
      final settings = await getSettings();
      
      if (!settings.masterEnabled) {
        await cancelAllNotifications();
        return;
      }
      
      // Cancel any existing notifications before scheduling new ones
      await cancelAllNotifications();
      
      // Get prayer times for today
      final todayPrayerTimes = await _apiService.fetchTodayPrayerTimes();
      debugPrint('Today\'s prayer times loaded: $todayPrayerTimes');
      
      // Get prayer times for tomorrow (for scheduling after midnight)
      final tomorrowPrayerTimes = await _apiService.fetchTomorrowPrayerTimes();
      debugPrint('Tomorrow\'s prayer times loaded: $tomorrowPrayerTimes');
      
      // Get next prayer info
      final nextPrayerInfo = await _apiService.getNextPrayerInfo();
      final nextPrayerName = nextPrayerInfo['name'] as String? ?? '';
      
      debugPrint('Next prayer: $nextPrayerName at ${nextPrayerInfo['timeFormatted']}');
      
      // Define prayer sequence
      final prayerNames = ['Fajr', 'Sunrise', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
      
      // Schedule today's remaining prayers
      for (final prayerName in prayerNames) {
        if (settings.prayerEnabled[prayerName] == true) {
          final prayerTime = todayPrayerTimes.getPrayerByName(prayerName);
          
          if (prayerTime != null && prayerTime.isAfter(DateTime.now())) {
            debugPrint('Scheduling notification for today\'s $prayerName at ${todayPrayerTimes.getFormattedTime(prayerTime)}');
            
            await _scheduleNotification(
              prayerName: prayerName,
              prayerTime: prayerTime,
              settings: settings,
            );
          }
        }
      }
      
      // Schedule tomorrow's first couple of prayers (to ensure we have some scheduled even after midnight)
      int tomorrowPrayersScheduled = 0;
      for (final prayerName in prayerNames) {
        if (settings.prayerEnabled[prayerName] == true) {
          final prayerTime = tomorrowPrayerTimes.getPrayerByName(prayerName);
          
          if (prayerTime != null) {
            debugPrint('Scheduling notification for tomorrow\'s $prayerName at ${tomorrowPrayerTimes.getFormattedTime(prayerTime)}');
            
            await _scheduleNotification(
              prayerName: prayerName,
              prayerTime: prayerTime,
              settings: settings,
              isTomorrow: true,
            );
            
            tomorrowPrayersScheduled++;
            if (tomorrowPrayersScheduled >= 2) break; // Only schedule the first two prayers for tomorrow
          }
        }
      }
      
      debugPrint('Successfully scheduled all prayer notifications');
    } catch (e) {
      debugPrint('Error scheduling all prayer notifications: $e');
    }
  }
  
  // Schedule a notification for a specific prayer
  Future<void> _scheduleNotification({
    required String prayerName,
    required DateTime prayerTime,
    required PrayerSettings settings,
    bool isTomorrow = false,
  }) async {
    try {
      // Calculate notification time based on reminder minutes
      final reminderMinutes = settings.reminderMinutes[prayerName] ?? 15;
      final notificationTime = prayerTime.subtract(Duration(minutes: reminderMinutes));
      
      // Only schedule if the notification time is in the future
      if (notificationTime.isBefore(DateTime.now())) {
        debugPrint('Not scheduling notification for $prayerName as the time has already passed');
        return;
      }
      
      final now = DateTime.now();
      final difference = prayerTime.difference(now);
      final hours = difference.inHours;
      final minutes = (difference.inMinutes % 60);
      final timeRemaining = '$hours hr ${minutes.toString().padLeft(2, '0')} min';
      
      debugPrint('$prayerName prayer is in $timeRemaining at ${DateFormat('h:mm a').format(prayerTime)}');
      
      // Get adhan option
      final adhanOption = settings.adhanOptions[prayerName] ?? AdhanOption.defaultAdhan;
      
      // Determine sound settings
      final bool enableVibration = settings.vibrate && 
                                  adhanOption != AdhanOption.silentNotification;
      
      // Format prayer time
      final prayerTimeFormatted = DateFormat('h:mm a').format(prayerTime);
      
      // Create notification title and body
      String title;
      String body;
      
      if (reminderMinutes > 0) {
        title = '$prayerName Prayer Reminder${isTomorrow ? ' (Tomorrow)' : ''}';
        body = '$prayerName prayer will be at $prayerTimeFormatted (in $reminderMinutes minutes)';
      } else {
        title = '$prayerName Prayer${isTomorrow ? ' (Tomorrow)' : ''}';
        body = 'It\'s time for $prayerName prayer at $prayerTimeFormatted';
      }
      
      // Generate unique ID for notification based on prayer name and timestamp
      final uniqueId = notificationIds[prayerName] ?? 0 + (isTomorrow ? 100 : 0);
      
      // Create the appropriate sound source based on adhan option
      String? soundSourceValue;
      if (adhanOption != AdhanOption.silentNotification) {
        switch (adhanOption) {
          case AdhanOption.defaultAdhan:
            soundSourceValue = 'resource://raw/adhan_default';
            break;
          case AdhanOption.fajrAdhan:
            soundSourceValue = 'resource://raw/adhan_fajr';
            break;
          case AdhanOption.meccaAdhan:
            soundSourceValue = 'resource://raw/adhan_mecca';
            break;
          case AdhanOption.madinaAdhan:
            soundSourceValue = 'resource://raw/adhan_madina';
            break;
          case AdhanOption.alAqsaAdhan:
            soundSourceValue = 'resource://raw/adhan_aqsa';
            break;
          case AdhanOption.shortNotification:
            soundSourceValue = 'resource://raw/notification_short';
            break;
          default:
            soundSourceValue = 'resource://raw/notification_sound';
        }
      }
      
      // Schedule the notification
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: uniqueId,
          channelKey: prayerChannelKey,
          title: title,
          body: body,
          notificationLayout: NotificationLayout.Default,
          category: NotificationCategory.Alarm,
          wakeUpScreen: settings.useHighPriorityNotifications,
          fullScreenIntent: settings.useHighPriorityNotifications,
          criticalAlert: settings.useHighPriorityNotifications,
          autoDismissible: false,
          showWhen: true,
          displayOnForeground: true,
          displayOnBackground: true,
          color: Color(URLs().colorScheme),
          backgroundColor: Color(URLs().colorScheme),
          payload: {
            'prayer_name': prayerName, 
            'prayer_time': prayerTime.millisecondsSinceEpoch.toString(),
            'is_tomorrow': isTomorrow.toString(),
          },
        ),
        actionButtons: [
          NotificationActionButton(
            key: 'MARK_AS_PRAYED',
            label: 'Mark as Prayed',
            color: Color(URLs().colorScheme),
            autoDismissible: true,
          ),
          NotificationActionButton(
            key: 'SNOOZE',
            label: 'Snooze 5 min',
            color: Colors.grey,
            autoDismissible: true,
          ),
        ],
        schedule: NotificationCalendar.fromDate(
          date: notificationTime,
          allowWhileIdle: true,
          preciseAlarm: true,
          repeats: false,
        ),
      );
      
      // Log scheduled notification
      await _logNotification(
        title: title,
        body: body,
        scheduledTime: notificationTime,
        prayerTime: prayerTime,
        prayerName: prayerName,
      );
      
      debugPrint('Scheduled notification for $prayerName at ${notificationTime.toString()}');
    } catch (e) {
      debugPrint('Error scheduling notification for $prayerName: $e');
    }
  }
  
  // Log notification to history
  Future<void> _logNotification({
    required String title,
    required String body,
    required DateTime scheduledTime,
    DateTime? prayerTime,
    String? prayerName,
    String action = '',
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString('prayer_notification_history') ?? '[]';
      final List<dynamic> historyList = json.decode(historyJson);
      
      // Add new notification to history
      historyList.add({
        'title': title,
        'body': body,
        'scheduledTime': scheduledTime.millisecondsSinceEpoch,
        'prayerTime': prayerTime?.millisecondsSinceEpoch,
        'prayerName': prayerName,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'action': action,
      });
      
      // Limit history to 50 items
      if (historyList.length > 50) {
        historyList.removeRange(0, historyList.length - 50);
      }
      
      // Save updated history
      await prefs.setString('prayer_notification_history', json.encode(historyList));
    } catch (e) {
      debugPrint('Error logging notification to history: $e');
    }
  }
  
  // Cancel notification for a specific prayer
  Future<void> cancelNotification(String prayerName) async {
    final notificationId = notificationIds[prayerName] ?? 0;
    await AwesomeNotifications().cancel(notificationId);
    debugPrint('Cancelled notification for $prayerName');
  }
  
  // Cancel all prayer notifications
  Future<void> cancelAllNotifications() async {
    await AwesomeNotifications().cancelAll();
    debugPrint('Cancelled all prayer notifications');
  }
  
  // Handle notification action (when user taps on notification)
  Future<void> handleNotificationAction(ReceivedAction receivedAction) async {
    if (receivedAction.payload?['prayer_name'] != null) {
      final prayerName = receivedAction.payload!['prayer_name']!;
      
      if (receivedAction.buttonKeyPressed == 'MARK_AS_PRAYED') {
        // Handle mark as prayed action
        await cancelNotification(prayerName);
        
        // Log action to history
        await _logNotification(
          title: '$prayerName Prayer',
          body: 'Marked as prayed',
          scheduledTime: DateTime.now(),
          action: 'MARK_AS_PRAYED',
        );
        
        // TODO: Update prayer tracking statistics
      } else if (receivedAction.buttonKeyPressed == 'SNOOZE') {
        // Handle snooze action - reschedule for 5 minutes later
        final settings = await getSettings();
        final adhanOption = settings.adhanOptions[prayerName] ?? AdhanOption.defaultAdhan;
        
        final snoozeTime = DateTime.now().add(Duration(minutes: 5));
        
        // Schedule a new notification for 5 minutes later
        await AwesomeNotifications().createNotification(
          content: NotificationContent(
            id: notificationIds[prayerName] ?? 0,
            channelKey: prayerChannelKey,
            title: '$prayerName Prayer (Snoozed)',
            body: 'It\'s time for $prayerName prayer',
            notificationLayout: NotificationLayout.Default,
            category: NotificationCategory.Alarm,
            wakeUpScreen: settings.useHighPriorityNotifications,
            fullScreenIntent: settings.useHighPriorityNotifications,
            criticalAlert: settings.useHighPriorityNotifications,
            color: Color(URLs().colorScheme),
            backgroundColor: Color(URLs().colorScheme),
            payload: {'prayer_name': prayerName},
          ),
          actionButtons: [
            NotificationActionButton(
              key: 'MARK_AS_PRAYED',
              label: 'Mark as Prayed',
              color: Color(URLs().colorScheme),
              autoDismissible: true,
            ),
            NotificationActionButton(
              key: 'SNOOZE',
              label: 'Snooze Again',
              color: Colors.grey,
              autoDismissible: true,
            ),
          ],
          schedule: NotificationCalendar.fromDate(
            date: snoozeTime,
            allowWhileIdle: true,
            preciseAlarm: true,
            repeats: false,
          ),
        );
        
        // Log action to history
        await _logNotification(
          title: '$prayerName Prayer',
          body: 'Snoozed for 5 minutes',
          scheduledTime: snoozeTime,
          action: 'SNOOZE',
        );
      }
    }
  }
  
  // Create a test notification
  Future<void> createTestNotification() async {
    try {
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: 999,
          channelKey: prayerChannelKey,
          title: 'Test Notification',
          body: 'This is a test prayer notification',
          notificationLayout: NotificationLayout.Default,
          category: NotificationCategory.Message,
          color: Color(URLs().colorScheme),
          backgroundColor: Color(URLs().colorScheme),
        ),
        actionButtons: [
          NotificationActionButton(
            key: 'MARK_AS_PRAYED',
            label: 'Mark as Prayed',
            color: Color(URLs().colorScheme),
            autoDismissible: true,
          ),
          NotificationActionButton(
            key: 'SNOOZE',
            label: 'Snooze',
            color: Colors.grey,
            autoDismissible: true,
          ),
        ],
      );
      
      debugPrint('Test notification created');
    } catch (e) {
      debugPrint('Error creating test notification: $e');
    }
  }
  
  // Get notification history
  Future<List<Map<String, dynamic>>> getNotificationHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString('prayer_notification_history') ?? '[]';
      final List<dynamic> historyList = json.decode(historyJson);
      
      // Convert to List<Map<String, dynamic>>
      return historyList.cast<Map<String, dynamic>>();
    } catch (e) {
      debugPrint('Error getting notification history: $e');
      return [];
    }
  }
  
  // Clear notification history
  Future<void> clearNotificationHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('prayer_notification_history', '[]');
      debugPrint('Notification history cleared');
    } catch (e) {
      debugPrint('Error clearing notification history: $e');
    }
  }
} 