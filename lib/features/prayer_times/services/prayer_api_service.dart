import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/prayer_times/models/prayer_time.dart';
import 'dart:math';
import 'package:http/http.dart' as http;
import 'dart:convert';

class PrayerApiService {
  static final PrayerApiService _instance = PrayerApiService._internal();
  
  factory PrayerApiService() {
    return _instance;
  }
  
  PrayerApiService._internal();
  
  final Dio _dio = Dio();
  
  // Cache for prayer times to reduce API calls
  final Map<String, PrayerTime> _cache = {};
  
  // Get the base URL for prayer times API
  String get apiBaseUrl => '${URLs().WEBURL}/wp-json/dpt/v1/prayertime';
  
  // Get UK-specific parameters for API requests
  Map<String, dynamic> get ukParams => {
    'location': 'nelson', 
    'country': 'uk',
    'filter': 'today',  // Explicitly request today's data
  };
  
  // Fetch today's prayer times
  Future<PrayerTime> fetchTodayPrayerTimes() async {
    final today = DateTime.now();
    final cacheKey = 'today_${today.year}_${today.month}_${today.day}';
    
    // Return cached data if available
    if (_cache.containsKey(cacheKey)) {
      debugPrint('Returning cached prayer times for today');
      return _cache[cacheKey]!;
    }

    try {
      final response = await http.get(
        Uri.parse('$apiBaseUrl').replace(queryParameters: ukParams),
      );

      debugPrint('API Response status: ${response.statusCode}');
      debugPrint('API Response body: ${response.body}');

      if (response.statusCode == 200) {
        dynamic jsonData;
        try {
          jsonData = json.decode(response.body);
          debugPrint('Decoded JSON data: $jsonData');
        } catch (e) {
          debugPrint('Error decoding JSON: $e');
          throw Exception('Invalid API response format');
        }

        // Check if we have valid data or just a list of arguments
        if (jsonData is Map<String, dynamic>) {
          // Create a PrayerTime object from the API response
          final prayerTime = PrayerTime.fromApi(jsonData);
          
          // Check if we got valid prayer times
          if (prayerTime.hasValidPrayerTimes()) {
            _cache[cacheKey] = prayerTime;
            return prayerTime;
          } else {
            debugPrint('API returned no valid prayer times, using fallback data');
            return _createHardcodedPrayerTimes(today);
          }
        } else if (jsonData is List) {
          // If the API returns a list (usually a list of argument filters),
          // we need to create fallback data
          debugPrint('API returned a list instead of a map: $jsonData');
          return _createHardcodedPrayerTimes(today);
        } else {
          debugPrint('Unexpected API response format: ${jsonData.runtimeType}');
          return _createHardcodedPrayerTimes(today);
        }
      } else {
        debugPrint('API returned status code ${response.statusCode}');
        return _createHardcodedPrayerTimes(today);
      }
    } catch (e) {
      debugPrint('Error fetching today\'s prayer times: $e');
      return _createHardcodedPrayerTimes(today);
    }
  }
  
  // Create hardcoded prayer times for Nelson, UK as a fallback
  PrayerTime _createHardcodedPrayerTimes(DateTime date) {
    // UK Nelson prayer times vary by season - these are rough approximations
    final month = date.month;
    
    // Seasonal adjustments for UK prayer times
    String fajrTime, sunriseTime, dhuhrTime, asrTime, maghribTime, ishaTime;
    
    if (month >= 11 || month <= 2) {
      // Winter
      fajrTime = '6:30 AM';
      sunriseTime = '8:00 AM';
      dhuhrTime = '12:15 PM';
      asrTime = '2:45 PM';
      maghribTime = '4:15 PM';
      ishaTime = '6:00 PM';
    } else if (month >= 3 && month <= 5) {
      // Spring
      fajrTime = '4:45 AM';
      sunriseTime = '6:00 AM';
      dhuhrTime = '1:15 PM';
      asrTime = '5:00 PM';
      maghribTime = '8:30 PM';
      ishaTime = '10:15 PM';
    } else if (month >= 6 && month <= 8) {
      // Summer
      fajrTime = '3:30 AM';
      sunriseTime = '4:45 AM';
      dhuhrTime = '1:30 PM';
      asrTime = '6:00 PM';
      maghribTime = '9:45 PM';
      ishaTime = '11:30 PM';
    } else {
      // Autumn
      fajrTime = '5:15 AM';
      sunriseTime = '7:00 AM';
      dhuhrTime = '12:45 PM';
      asrTime = '4:00 PM';
      maghribTime = '6:15 PM';
      ishaTime = '8:00 PM';
    }
    
    // Create a hardcoded data map for PrayerTime.fromApi
    final Map<String, dynamic> hardcodedData = {
      'date': DateFormat('yyyy-MM-dd').format(date),
      'hijri_date': _getApproximateHijriDate(date),
      'fajr': fajrTime,
      'sunrise': sunriseTime,
      'dhuhr': dhuhrTime,
      'asr': asrTime,
      'maghrib': maghribTime,
      'isha': ishaTime,
      // Include note about fallback data
      'note': 'Using fallback prayer times - actual times may vary'
    };
    
    debugPrint('Created hardcoded prayer times for ${date.toString()}: $hardcodedData');
    return PrayerTime.fromApi(hardcodedData);
  }
  
  // Get an approximate Hijri date as fallback
  String _getApproximateHijriDate(DateTime date) {
    // Rough approximation - for display only when API fails
    final hijriMonths = [
      'Rajab', 'Sha\'ban', 'Ramadan', 'Shawwal', 
      'Dhu al-Qi\'dah', 'Dhu al-Hijjah', 'Muharram', 
      'Safar', 'Rabi\' al-awwal', 'Rabi\' al-thani', 
      'Jumada al-awwal', 'Jumada al-thani'
    ];
    
    int hijriMonth = (date.month + 2) % 12;
    if (hijriMonth == 0) hijriMonth = 12;
    int hijriYear = date.year - 579;
    
    return '${date.day} ${hijriMonths[hijriMonth-1]} $hijriYear';
  }

  // Fetch tomorrow's prayer times with better fallback support
  Future<PrayerTime> fetchTomorrowPrayerTimes() async {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    final cacheKey = 'tomorrow_${tomorrow.year}_${tomorrow.month}_${tomorrow.day}';
    
    // Return cached data if available
    if (_cache.containsKey(cacheKey)) {
      debugPrint('Returning cached prayer times for tomorrow');
      return _cache[cacheKey]!;
    }

    try {
      final Map<String, dynamic> tomorrowParams = {
        ...ukParams,
        'filter': 'tomorrow'
      };
      
      final response = await http.get(
        Uri.parse('$apiBaseUrl').replace(queryParameters: tomorrowParams),
      );

      debugPrint('Tomorrow API Response status: ${response.statusCode}');
      debugPrint('Tomorrow API Response body: ${response.body}');

      if (response.statusCode == 200) {
        dynamic jsonData;
        try {
          jsonData = json.decode(response.body);
          debugPrint('Decoded tomorrow JSON data: $jsonData');
        } catch (e) {
          debugPrint('Error decoding tomorrow JSON: $e');
          throw Exception('Invalid tomorrow API response format');
        }

        // Check if we have valid data
        if (jsonData is Map<String, dynamic>) {
          // Create a PrayerTime object from the API response
          final prayerTime = PrayerTime.fromApi(jsonData);
          
          // Check if we got valid prayer times
          if (prayerTime.hasValidPrayerTimes()) {
            _cache[cacheKey] = prayerTime;
            return prayerTime;
          } else {
            debugPrint('API returned no valid prayer times for tomorrow, using fallback data');
            return _createHardcodedPrayerTimes(tomorrow);
          }
        } else if (jsonData is List) {
          // If the API returns a list, create fallback data
          debugPrint('Tomorrow API returned a list instead of a map: $jsonData');
          return _createHardcodedPrayerTimes(tomorrow);
        } else {
          debugPrint('Unexpected tomorrow API response format: ${jsonData.runtimeType}');
          return _createHardcodedPrayerTimes(tomorrow);
        }
      } else {
        debugPrint('Tomorrow API returned status code ${response.statusCode}');
        return _createHardcodedPrayerTimes(tomorrow);
      }
    } catch (e) {
      debugPrint('Error fetching tomorrow\'s prayer times: $e');
      return _createHardcodedPrayerTimes(tomorrow);
    }
  }
  
  /// Gets information about the next prayer
  Map<String, dynamic> getNextPrayerInfo() {
    try {
      final now = DateTime.now();
      final todayPrayerTimesEntry = _cache.entries
          .where((entry) => entry.key.startsWith('today_'))
          .firstOrNull;
      
      // If cache doesn't have today's prayer times, use defaults
      if (todayPrayerTimesEntry == null) {
        debugPrint('No cached today prayer times found for next prayer info');
        return {
          'name': 'Dhuhr',
          'timeFormatted': '12:15 PM',
          'timeRemaining': 'N/A',
          'isTomorrow': false
        };
      }
      
      final todayPrayerTimes = todayPrayerTimesEntry.value;
      debugPrint('Getting next prayer info from: ${todayPrayerTimes.date}');
      
      // Get the next prayer for today
      final nextPrayerTime = todayPrayerTimes.getNextPrayer(now);
      
      // If there is a next prayer today
      if (nextPrayerTime != null) {
        // Determine which prayer this is
        String prayerName = 'Unknown';
        if (nextPrayerTime == todayPrayerTimes.fajr) prayerName = 'Fajr';
        else if (nextPrayerTime == todayPrayerTimes.dhuhr) prayerName = 'Dhuhr';
        else if (nextPrayerTime == todayPrayerTimes.asr) prayerName = 'Asr';
        else if (nextPrayerTime == todayPrayerTimes.maghrib) prayerName = 'Maghrib';
        else if (nextPrayerTime == todayPrayerTimes.isha) prayerName = 'Isha';
        
        // Skip Sunrise
        if (nextPrayerTime == todayPrayerTimes.sunrise) {
          // Find next prayer after Sunrise
          if (todayPrayerTimes.dhuhr != null && todayPrayerTimes.dhuhr!.isAfter(now)) {
            prayerName = 'Dhuhr';
            final timeRemaining = _getTimeRemaining(now, todayPrayerTimes.dhuhr!);
            final formattedTime = todayPrayerTimes.getFormattedTime(todayPrayerTimes.dhuhr);
            return {
              'name': prayerName,
              'timeFormatted': formattedTime,
              'timeRemaining': timeRemaining,
              'isTomorrow': false
            };
          }
        }
        
        final timeRemaining = _getTimeRemaining(now, nextPrayerTime);
        final formattedTime = todayPrayerTimes.getFormattedTime(nextPrayerTime);
        
        debugPrint('Next prayer: $prayerName at $formattedTime');
        
        return {
          'name': prayerName,
          'timeFormatted': formattedTime,
          'timeRemaining': timeRemaining,
          'isTomorrow': false
        };
      }
      
      // Check if we should get tomorrow's fajr
      try {
        // Try to get tomorrow's prayer times
        final tomorrowEntries = _cache.entries
            .where((entry) => entry.key.startsWith('tomorrow_'));
            
        if (tomorrowEntries.isNotEmpty) {
          final tomorrowPrayerTimes = tomorrowEntries.first.value;
          
          // Get Fajr for tomorrow
          final fajrTime = tomorrowPrayerTimes.fajr;
          
          if (fajrTime != null) {
            final timeRemaining = _getTimeRemaining(now, fajrTime);
            final formattedTime = tomorrowPrayerTimes.getFormattedTime(fajrTime);
            
            return {
              'name': 'Fajr',
              'timeFormatted': formattedTime,
              'timeRemaining': timeRemaining,
              'isTomorrow': true
            };
          }
        }
        
        // If we get here, something went wrong - use Dhuhr as fallback
        debugPrint('No next prayer found, using default Dhuhr');
        return {
          'name': 'Dhuhr',
          'timeFormatted': '12:15 PM',
          'timeRemaining': 'N/A',
          'isTomorrow': false
        };
      } catch (e) {
        debugPrint('Error getting tomorrow\'s prayer times: $e');
        // Fallback to Dhuhr
        return {
          'name': 'Dhuhr',
          'timeFormatted': '12:15 PM',
          'timeRemaining': 'N/A',
          'isTomorrow': false
        };
      }
    } catch (e) {
      debugPrint('Error in getNextPrayerInfo: $e');
      // Fallback to Dhuhr
      return {
        'name': 'Dhuhr',
        'timeFormatted': '12:15 PM',
        'timeRemaining': 'N/A',
        'isTomorrow': false
      };
    }
  }
  
  /// Calculate time remaining until a prayer time
  String _getTimeRemaining(DateTime now, DateTime prayerTime) {
    final difference = prayerTime.difference(now);
    final hours = difference.inHours;
    final minutes = (difference.inMinutes % 60);
    return '$hours hr ${minutes.toString().padLeft(2, '0')} min';
  }
  
  // Clear the cache to force refresh
  void clearCache() {
    _cache.clear();
    debugPrint('Prayer time cache cleared');
  }
} 