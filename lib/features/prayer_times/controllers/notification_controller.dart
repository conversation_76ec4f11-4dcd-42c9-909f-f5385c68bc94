import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:flutter/material.dart';
import 'package:new_islamic_app_uk/features/prayer_times/services/prayer_notification_service.dart';
import 'package:new_islamic_app_uk/features/prayer_times/services/prayer_api_service.dart';
import 'package:new_islamic_app_uk/features/prayer_times/models/prayer_settings.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationController {
  static final NotificationController _instance = NotificationController._internal();
  
  factory NotificationController() {
    return _instance;
  }
  
  NotificationController._internal();
  
  // Initialize notification listeners
  static Future<void> initializeNotificationListeners() async {
    // Listen to notification creation events
    AwesomeNotifications().setListeners(
      onActionReceivedMethod: onActionReceivedMethod,
      onNotificationCreatedMethod: onNotificationCreatedMethod,
      onNotificationDisplayedMethod: onNotificationDisplayedMethod,
      onDismissActionReceivedMethod: onDismissActionReceivedMethod,
    );
  }
  
  // Called when a notification is created
  @pragma('vm:entry-point')
  static Future<void> onNotificationCreatedMethod(ReceivedNotification receivedNotification) async {
    debugPrint('Notification created: ${receivedNotification.id}');
  }
  
  // Called when a notification is displayed
  @pragma('vm:entry-point')
  static Future<void> onNotificationDisplayedMethod(ReceivedNotification receivedNotification) async {
    debugPrint('Notification displayed: ${receivedNotification.id}');
  }
  
  // Called when a notification is dismissed
  @pragma('vm:entry-point')
  static Future<void> onDismissActionReceivedMethod(ReceivedAction receivedAction) async {
    debugPrint('Notification dismissed: ${receivedAction.id}');
  }
  
  // Called when a notification action is received
  @pragma('vm:entry-point')
  static Future<void> onActionReceivedMethod(ReceivedAction receivedAction) async {
    debugPrint('Notification action received: ${receivedAction.id}');
    
    // Handle different actions
    if (receivedAction.actionType == ActionType.Default) {
      // Open the app
    } else if (receivedAction.buttonKeyPressed == 'MARK_AS_PRAYED') {
      // Handle mark as prayed
      await _markPrayerAsPrayed(receivedAction);
    } else if (receivedAction.buttonKeyPressed == 'SNOOZE') {
      // Handle snooze
      await _snoozePrayer(receivedAction);
    }
  }
  
  // Handle marking a prayer as prayed
  static Future<void> _markPrayerAsPrayed(ReceivedAction receivedAction) async {
    try {
      final prayerName = receivedAction.payload?['prayer_name'];
      if (prayerName != null) {
        // Update prayer statistics
        final prefs = await SharedPreferences.getInstance();
        final stats = prefs.getStringList('prayer_stats_${DateTime.now().toString().split(' ')[0]}') ?? [];
        stats.add(prayerName);
        await prefs.setStringList('prayer_stats_${DateTime.now().toString().split(' ')[0]}', stats);
        
        // Log to prayer history
        await PrayerNotificationService().handleNotificationAction(receivedAction);
      }
    } catch (e) {
      debugPrint('Error marking prayer as prayed: $e');
    }
  }
  
  // Handle snoozing a prayer notification
  static Future<void> _snoozePrayer(ReceivedAction receivedAction) async {
    try {
      final prayerName = receivedAction.payload?['prayer_name'];
      if (prayerName != null) {
        // Handle snooze action
        await PrayerNotificationService().handleNotificationAction(receivedAction);
      }
    } catch (e) {
      debugPrint('Error snoozing prayer: $e');
    }
  }
  
  // Create a test notification
  static Future<void> createTestNotification() async {
    await PrayerNotificationService().createTestNotification();
  }
  
  // Refresh prayer notifications
  static Future<void> refreshPrayerNotifications() async {
    debugPrint('Refreshing prayer notifications...');
    try {
      // Clear API cache to ensure fresh data
      final apiService = PrayerApiService();
      apiService.clearCache();
      
      // Schedule notifications with fresh prayer times
      await PrayerNotificationService().scheduleAllNotifications();
      
      debugPrint('Prayer notifications refreshed successfully');
    } catch (e) {
      debugPrint('Error refreshing prayer notifications: $e');
    }
  }
  
  // Get notification settings
  static Future<PrayerSettings> getNotificationSettings() async {
    return await PrayerNotificationService().getSettings();
  }
  
  // Update notification settings
  static Future<bool> updateNotificationSettings(PrayerSettings settings) async {
    return await PrayerNotificationService().saveSettings(settings);
  }
  
  // Open notification settings
  static Future<void> openNotificationSettings() async {
    await AwesomeNotifications().showNotificationConfigPage();
  }
  
  // Check if notifications are allowed
  static Future<bool> checkNotificationPermissions() async {
    return await AwesomeNotifications().isNotificationAllowed();
  }
  
  // Request notification permissions
  static Future<bool> requestNotificationPermissions() async {
    return await AwesomeNotifications().requestPermissionToSendNotifications();
  }
} 