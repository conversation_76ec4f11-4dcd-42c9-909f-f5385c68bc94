import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import '../../../URLs.dart';
import '../controllers/journal_controller.dart';

class JournalFilterOptions {
  static void showFilterOptions(BuildContext context, TabController tabController) {
    final urls = URLs();
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) {
        return Container(
          padding: EdgeInsets.only(
            top: 24.0,
            left: 24.0,
            right: 24.0,
            bottom: MediaQuery.of(context).viewInsets.bottom + 24.0,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(24.0)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10.0,
                offset: Offset(0, -5),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Filter Journal Entries',
                    style: GoogleFonts.poppins(
                      fontSize: 20.0,
                      fontWeight: FontWeight.w600,
                      color: Color(urls.textPrimary),
                    ),
                  ),
                  IconButton(
                    icon: Container(
                      padding: EdgeInsets.all(4.0),
                      decoration: BoxDecoration(
                        color: Color(urls.textSecondary).withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.close,
                        color: Color(urls.textSecondary),
                        size: 20.0,
                      ),
                    ),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              SizedBox(height: 8.0),
              Text(
                'Sort and filter your journal entries',
                style: GoogleFonts.poppins(
                  fontSize: 14.0,
                  color: Color(urls.textSecondary),
                ),
              ),
              SizedBox(height: 24.0),
              Text(
                'SORT BY',
                style: GoogleFonts.poppins(
                  fontSize: 12.0,
                  fontWeight: FontWeight.w600,
                  color: Color(urls.textSecondary),
                  letterSpacing: 1.2,
                ),
              ),
              SizedBox(height: 8.0),
              _buildFilterOption(
                context,
                'Most Recent First',
                FontAwesomeIcons.clockRotateLeft,
                () {
                  final controller = Provider.of<JournalController>(context, listen: false);
                  controller.sortEntries((a, b) => b.date.compareTo(a.date));
                  Navigator.pop(context);
                },
                urls,
              ),
              _buildFilterOption(
                context,
                'Oldest First',
                FontAwesomeIcons.clock,
                () {
                  final controller = Provider.of<JournalController>(context, listen: false);
                  controller.sortEntries((a, b) => a.date.compareTo(b.date));
                  Navigator.pop(context);
                },
                urls,
              ),
              SizedBox(height: 24.0),
              Text(
                'FILTER BY',
                style: GoogleFonts.poppins(
                  fontSize: 12.0,
                  fontWeight: FontWeight.w600,
                  color: Color(urls.textSecondary),
                  letterSpacing: 1.2,
                ),
              ),
              SizedBox(height: 8.0),
              _buildFilterOption(
                context,
                'Favorites Only',
                FontAwesomeIcons.solidHeart,
                () {
                  tabController.animateTo(1);
                  Navigator.pop(context);
                },
                urls,
              ),
              _buildFilterOption(
                context,
                'Group by Mood',
                FontAwesomeIcons.faceLaugh,
                () {
                  tabController.animateTo(2);
                  Navigator.pop(context);
                },
                urls,
              ),
              // New filter option for date range
              _buildFilterOption(
                context,
                'Date Range',
                FontAwesomeIcons.calendarDays,
                () async {
                  Navigator.pop(context);
                  final DateTimeRange? picked = await showDateRangePicker(
                    context: context,
                    firstDate: DateTime(2020),
                    lastDate: DateTime.now(),
                    builder: (BuildContext context, Widget? child) {
                      return Theme(
                        data: ThemeData.light().copyWith(
                          colorScheme: ColorScheme.light(
                            primary: Color(urls.journalPrimaryColor),
                            onPrimary: Colors.white,
                            surface: Colors.white,
                            onSurface: Color(urls.textPrimary),
                          ),
                          dialogBackgroundColor: Colors.white,
                        ),
                        child: child!,
                      );
                    },
                  );
                  
                  if (picked != null) {
                    final controller = Provider.of<JournalController>(context, listen: false);
                    controller.filterByDateRange(picked.start, picked.end);
                  }
                },
                urls,
              ),
            ],
          ),
        );
      },
    );
  }
  
  static Widget _buildFilterOption(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
    URLs urls,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.0),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 12.0),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(10.0),
                decoration: BoxDecoration(
                  color: Color(urls.journalPrimaryColor).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10.0),
                ),
                child: Icon(
                  icon,
                  color: Color(urls.journalPrimaryColor),
                  size: 18.0,
                ),
              ),
              SizedBox(width: 16.0),
              Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 16.0,
                  fontWeight: FontWeight.w500,
                  color: Color(urls.textPrimary),
                ),
              ),
              Spacer(),
              Icon(
                FontAwesomeIcons.angleRight,
                color: Color(urls.textSecondary).withOpacity(0.5),
                size: 16.0,
              ),
            ],
          ),
        ),
      ),
    );
  }
} 