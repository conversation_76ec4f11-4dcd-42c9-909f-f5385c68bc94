import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class JournalSearchField extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onClose;
  final ValueChanged<String> onChanged;
  final FocusNode? focusNode;

  const JournalSearchField({
    Key? key,
    required this.controller,
    required this.onClose,
    required this.onChanged,
    this.focusNode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      focusNode: focusNode,
      autofocus: true,
      onChanged: onChanged,
      decoration: InputDecoration(
        hintText: 'Search your journal...',
        hintStyle: GoogleFonts.poppins(
          color: Colors.white70,
        ),
        border: InputBorder.none,
        prefixIcon: Icon(FontAwesomeIcons.magnifyingGlass, color: Colors.white70, size: 16),
        suffixIcon: IconButton(
          icon: Icon(Icons.close, color: Colors.white70),
          onPressed: onClose,
        ),
      ),
      style: GoogleFonts.poppins(
        color: Colors.white,
        fontSize: 16.0,
      ),
      cursorColor: Colors.white,
      textInputAction: TextInputAction.search,
    );
  }
} 