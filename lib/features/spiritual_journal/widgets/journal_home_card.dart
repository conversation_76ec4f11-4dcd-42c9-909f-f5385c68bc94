import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../URLs.dart';
import '../controllers/journal_controller.dart';
import '../models/journal_entry.dart';
import '../screens/journal_list_screen.dart';

class JournalHomeCard extends StatelessWidget {
  const JournalHomeCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Card(
      elevation: urls.cardElevation,
      margin: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
      ),
      child: InkWell(
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => JournalListScreen()),
        ),
        borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Color(urls.journalPrimaryColor),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(urls.defaultBorderRadius),
                  topRight: Radius.circular(urls.defaultBorderRadius),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.auto_stories,
                        color: Colors.white,
                        size: 24.0,
                      ),
                      SizedBox(width: 12.0),
                      Text(
                        'Spiritual Mood Journal',
                        style: GoogleFonts.poppins(
                          fontSize: 18.0,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white,
                    size: 16.0,
                  ),
                ],
              ),
            ),
            
            // Content
            Padding(
              padding: EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Track your spiritual journey',
                    style: GoogleFonts.poppins(
                      fontSize: 16.0,
                      fontWeight: FontWeight.w500,
                      color: Color(urls.textPrimary),
                    ),
                  ),
                  SizedBox(height: 8.0),
                  Text(
                    'Record your spiritual mood, reflections, and prayers. Receive Islamic guidance based on your emotional state.',
                    style: GoogleFonts.poppins(
                      fontSize: 14.0,
                      color: Color(urls.textSecondary),
                      height: 1.5,
                    ),
                  ),
                  SizedBox(height: 16.0),
                  
                  // Latest entry or create new
                  _buildLatestEntryPreview(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildLatestEntryPreview(BuildContext context) {
    final urls = URLs();
    final controller = Provider.of<JournalController>(context);
    
    if (controller.isLoading) {
      return Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 16.0),
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              Color(urls.journalPrimaryColor),
            ),
          ),
        ),
      );
    }
    
    if (controller.entries.isEmpty) {
      return _buildCreateNewButton(context);
    }
    
    // Display the latest entry
    final latestEntry = controller.entries.first;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              latestEntry.getMoodIcon(),
              color: latestEntry.getMoodColor(),
              size: 20.0,
            ),
            SizedBox(width: 8.0),
            Text(
              'Latest Entry • ${latestEntry.formattedDate}',
              style: GoogleFonts.poppins(
                fontSize: 14.0,
                fontWeight: FontWeight.w500,
                color: Color(urls.journalPrimaryColor),
              ),
            ),
          ],
        ),
        SizedBox(height: 8.0),
        Text(
          latestEntry.gratitude,
          style: GoogleFonts.poppins(
            fontSize: 14.0,
            color: Color(urls.textPrimary),
            height: 1.5,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(height: 16.0),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            OutlinedButton(
              onPressed: () => Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => JournalListScreen()),
              ),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: Color(urls.journalPrimaryColor)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0),
                ),
              ),
              child: Text(
                'View All Entries',
                style: GoogleFonts.poppins(
                  fontSize: 12.0,
                  color: Color(urls.journalPrimaryColor),
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => _navigateToNewEntry(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(urls.journalAccentColor),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.0),
                ),
              ),
              child: Text(
                'New Entry',
                style: GoogleFonts.poppins(
                  fontSize: 12.0,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildCreateNewButton(BuildContext context) {
    final urls = URLs();
    
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.add_circle_outline,
            size: 48.0,
            color: Color(urls.journalAccentColor),
          ),
          SizedBox(height: 16.0),
          Text(
            'Start your spiritual journal today',
            style: GoogleFonts.poppins(
              fontSize: 14.0,
              color: Color(urls.textSecondary),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.0),
          ElevatedButton(
            onPressed: () => _navigateToNewEntry(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(urls.journalAccentColor),
              padding: EdgeInsets.symmetric(
                horizontal: 24.0,
                vertical: 12.0,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.0),
              ),
            ),
            child: Text(
              'Create First Entry',
              style: GoogleFonts.poppins(
                fontSize: 14.0,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  void _navigateToNewEntry(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => JournalListScreen(),
      ),
    ).then((_) {
      // Refresh the controller after returning
      final controller = Provider.of<JournalController>(context, listen: false);
      controller.initialize();
    });
  }
} 