import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../URLs.dart';
import '../models/journal_entry.dart';
import 'journal_entry_card.dart';
import 'empty_state_widget.dart';

class JournalTabViews {
  static Widget buildJournalList(
    List<JournalEntry> entries, 
    String searchQuery, 
    Function(JournalEntry) onTap,
    Function(JournalEntry) onFavorite,
  ) {
    final filteredEntries = searchQuery.isEmpty
        ? entries
        : entries.where((entry) =>
            entry.gratitude.toLowerCase().contains(searchQuery.toLowerCase()) ||
            entry.reflection.toLowerCase().contains(searchQuery.toLowerCase()) ||
            entry.prayer.toLowerCase().contains(searchQuery.toLowerCase()) ||
            entry.islamicReminder.toLowerCase().contains(searchQuery.toLowerCase()) ||
            entry.tags.any((tag) => tag.toLowerCase().contains(searchQuery.toLowerCase())) ||
            entry.getMoodText().toLowerCase().contains(searchQuery.toLowerCase())
          ).toList();
    
    if (filteredEntries.isEmpty) {
      return EmptyStateWidget(
        title: searchQuery.isEmpty ? 'No journal entries yet' : 'No matching entries found',
        subtitle: searchQuery.isEmpty ? 'Start your spiritual journey by adding your first entry' : 'Try a different search term',
        icon: searchQuery.isEmpty ? FontAwesomeIcons.bookOpen : Icons.search_off,
        showCreateButton: searchQuery.isEmpty,
      );
    }
    
    return AnimatedSwitcher(
      duration: Duration(milliseconds: 300),
      child: ListView.builder(
        key: ValueKey<int>(filteredEntries.length),
        padding: EdgeInsets.only(top: 16.0, bottom: 100.0),
        itemCount: filteredEntries.length,
        itemBuilder: (context, index) {
          final entry = filteredEntries[index];
          return AnimatedOpacity(
            opacity: 1.0,
            duration: Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            child: JournalEntryCard(
              entry: entry,
              onTap: () => onTap(entry),
              onFavorite: () => onFavorite(entry),
            ),
          );
        },
      ),
    );
  }
  
  static Widget buildMoodCategorizedList(
    List<JournalEntry> entries, 
    String searchQuery,
    Function(JournalEntry) onTap,
    Function(JournalEntry) onFavorite,
  ) {
    final urls = URLs();
    final filteredEntries = searchQuery.isEmpty
        ? entries
        : entries.where((entry) =>
            entry.gratitude.toLowerCase().contains(searchQuery.toLowerCase()) ||
            entry.reflection.toLowerCase().contains(searchQuery.toLowerCase()) ||
            entry.prayer.toLowerCase().contains(searchQuery.toLowerCase()) ||
            entry.islamicReminder.toLowerCase().contains(searchQuery.toLowerCase()) ||
            entry.tags.any((tag) => tag.toLowerCase().contains(searchQuery.toLowerCase())) ||
            entry.getMoodText().toLowerCase().contains(searchQuery.toLowerCase())
          ).toList();
    
    if (filteredEntries.isEmpty) {
      return EmptyStateWidget(
        title: searchQuery.isEmpty ? 'No journal entries yet' : 'No matching entries found',
        subtitle: searchQuery.isEmpty ? 'Start your spiritual journey by adding your first entry' : 'Try a different search term',
        icon: searchQuery.isEmpty ? FontAwesomeIcons.bookOpen : Icons.search_off,
        showCreateButton: searchQuery.isEmpty,
      );
    }
    
    // Group entries by mood
    final Map<String, List<JournalEntry>> entriesByMood = {};
    for (var entry in filteredEntries) {
      final mood = entry.getMoodText();
      if (!entriesByMood.containsKey(mood)) {
        entriesByMood[mood] = [];
      }
      entriesByMood[mood]!.add(entry);
    }
    
    // Sort moods by level (excellent to poor)
    final moodOrder = {
      'Excellent': 5,
      'Good': 4,
      'Neutral': 3,
      'Low': 2,
      'Poor': 1,
    };
    
    final sortedMoods = entriesByMood.keys.toList()
      ..sort((a, b) => (moodOrder[b] ?? 0).compareTo(moodOrder[a] ?? 0));
    
    return ListView.builder(
      padding: EdgeInsets.only(top: 16.0, bottom: 100.0),
      itemCount: sortedMoods.length,
      itemBuilder: (context, index) {
        final mood = sortedMoods[index];
        final moodEntries = entriesByMood[mood]!;
        final sampleEntry = moodEntries.first; // For color reference
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    sampleEntry.getMoodColor(),
                    sampleEntry.getMoodColor().withOpacity(0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16.0),
                boxShadow: [
                  BoxShadow(
                    color: sampleEntry.getMoodColor().withOpacity(0.3),
                    blurRadius: 8.0,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(10.0),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      sampleEntry.getMoodIcon(),
                      color: Colors.white,
                      size: 24.0,
                    ),
                  ),
                  SizedBox(width: 16.0),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          mood,
                          style: GoogleFonts.poppins(
                            fontSize: 18.0,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          '${moodEntries.length} ${moodEntries.length == 1 ? 'entry' : 'entries'}',
                          style: GoogleFonts.poppins(
                            fontSize: 14.0,
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.all(8.0),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      FontAwesomeIcons.angleDown,
                      color: Colors.white,
                      size: 16.0,
                    ),
                  ),
                ],
              ),
            ),
            ...moodEntries.map((entry) => JournalEntryCard(
              entry: entry,
              onTap: () => onTap(entry),
              onFavorite: () => onFavorite(entry),
            )).toList(),
            SizedBox(height: 16.0),
          ],
        );
      },
    );
  }
} 