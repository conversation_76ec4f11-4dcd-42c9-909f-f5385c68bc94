import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../URLs.dart';
import '../screens/journal_edit_screen.dart';

class EmptyStateWidget extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final bool showCreateButton;

  const EmptyStateWidget({
    Key? key,
    required this.title,
    required this.subtitle,
    required this.icon,
    this.showCreateButton = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(24.0),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(urls.journalPrimaryColor).withOpacity(0.2),
                  Color(urls.journalSecondaryColor).withOpacity(0.2),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Color(urls.journalPrimaryColor).withOpacity(0.1),
                  blurRadius: 10.0,
                  spreadRadius: 2.0,
                ),
              ],
            ),
            child: Icon(
              icon,
              size: 48.0,
              color: Color(urls.journalPrimaryColor),
            ),
          ),
          SizedBox(height: 24.0),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 22.0,
              fontWeight: FontWeight.w600,
              color: Color(urls.textPrimary),
            ),
          ),
          SizedBox(height: 12.0),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 32.0),
            child: Text(
              subtitle,
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: 16.0,
                color: Color(urls.textSecondary),
                height: 1.5,
              ),
            ),
          ),
          SizedBox(height: 32.0),
          if (showCreateButton)
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(urls.journalPrimaryColor),
                    Color(urls.journalSecondaryColor),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12.0),
                boxShadow: [
                  BoxShadow(
                    color: Color(urls.journalPrimaryColor).withOpacity(0.3),
                    blurRadius: 8.0,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: ElevatedButton.icon(
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => JournalEditScreen(),
                  ),
                ),
                icon: Icon(FontAwesomeIcons.plus, color: Colors.white, size: 16),
                label: Text(
                  'Create First Entry',
                  style: GoogleFonts.poppins(
                    fontSize: 16.0,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                    letterSpacing: 0.5,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(horizontal: 24.0, vertical: 14.0),
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
} 