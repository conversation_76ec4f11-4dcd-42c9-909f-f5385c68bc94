import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../URLs.dart';
import '../screens/journal_edit_screen.dart';

class JournalFAB extends StatelessWidget {
  const JournalFAB({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(urls.journalPrimaryColor),
            Color(urls.journalSecondaryColor),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: Color(urls.journalPrimaryColor).withOpacity(0.3),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: FloatingActionButton.extended(
        onPressed: () => _navigateToAddEntry(context),
        backgroundColor: Colors.transparent,
        elevation: 0,
        icon: Icon(FontAwesomeIcons.plus, color: Colors.white, size: 16),
        label: Text(
          'New Entry',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
            color: Colors.white,
            letterSpacing: 0.5,
          ),
        ),
      ),
    );
  }
  
  void _navigateToAddEntry(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => JournalEditScreen(),
      ),
    );
  }
} 