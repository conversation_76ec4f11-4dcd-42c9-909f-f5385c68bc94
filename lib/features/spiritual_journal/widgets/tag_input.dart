import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../URLs.dart';

class TagInput extends StatefulWidget {
  final List<String> tags;
  final Function(List<String>) onTagsChanged;
  final List<String> suggestedTags;

  const TagInput({
    Key? key,
    required this.tags,
    required this.onTagsChanged,
    this.suggestedTags = const [],
  }) : super(key: key);

  @override
  _TagInputState createState() => _TagInputState();
}

class _TagInputState extends State<TagInput> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _showSuggestions = false;
  List<String> _filteredSuggestions = [];

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      setState(() {
        _showSuggestions = _focusNode.hasFocus && _controller.text.isNotEmpty;
        _updateFilteredSuggestions();
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _updateFilteredSuggestions() {
    if (_controller.text.isEmpty) {
      _filteredSuggestions = [];
    } else {
      _filteredSuggestions = widget.suggestedTags
          .where((tag) => 
              tag.toLowerCase().contains(_controller.text.toLowerCase()) && 
              !widget.tags.contains(tag))
          .toList();
    }
  }

  void _addTag(String tag) {
    if (tag.isNotEmpty && !widget.tags.contains(tag)) {
      final updatedTags = List<String>.from(widget.tags)..add(tag);
      widget.onTagsChanged(updatedTags);
      _controller.clear();
      setState(() {
        _showSuggestions = false;
        _filteredSuggestions = [];
      });
    }
  }

  void _removeTag(String tag) {
    final updatedTags = List<String>.from(widget.tags)..remove(tag);
    widget.onTagsChanged(updatedTags);
  }

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: GoogleFonts.poppins(
            fontSize: 16.0,
            fontWeight: FontWeight.w600,
            color: Color(urls.journalPrimaryColor),
          ),
        ),
        SizedBox(height: 8.0),
        Text(
          'Add tags to categorize your journal entry',
          style: GoogleFonts.poppins(
            fontSize: 14.0,
            color: Color(urls.textSecondary),
          ),
        ),
        SizedBox(height: 16.0),
        
        // Tag input field
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
            border: Border.all(
              color: _focusNode.hasFocus 
                  ? Color(urls.journalPrimaryColor) 
                  : Color(urls.textSecondary).withOpacity(0.3),
              width: 1.0,
            ),
            boxShadow: _focusNode.hasFocus ? [
              BoxShadow(
                color: Color(urls.journalPrimaryColor).withOpacity(0.1),
                blurRadius: 8.0,
                offset: Offset(0, 2),
              ),
            ] : null,
          ),
          padding: EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            children: [
              Icon(
                Icons.tag,
                color: Color(urls.textSecondary),
                size: 20.0,
              ),
              SizedBox(width: 12.0),
              Expanded(
                child: TextField(
                  controller: _controller,
                  focusNode: _focusNode,
                  decoration: InputDecoration(
                    hintText: 'Add a tag...',
                    hintStyle: GoogleFonts.poppins(
                      color: Color(urls.textSecondary).withOpacity(0.7),
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(vertical: 16.0),
                  ),
                  style: GoogleFonts.poppins(
                    fontSize: 14.0,
                    color: Color(urls.textPrimary),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _showSuggestions = value.isNotEmpty;
                      _updateFilteredSuggestions();
                    });
                  },
                  onSubmitted: (value) {
                    _addTag(value.trim());
                  },
                ),
              ),
              IconButton(
                icon: Icon(
                  Icons.add_circle,
                  color: _controller.text.isEmpty 
                      ? Color(urls.textSecondary).withOpacity(0.5)
                      : Color(urls.journalPrimaryColor),
                ),
                onPressed: _controller.text.isEmpty 
                    ? null 
                    : () => _addTag(_controller.text.trim()),
              ),
            ],
          ),
        ),
        
        // Tag suggestions
        if (_showSuggestions && _filteredSuggestions.isNotEmpty)
          AnimatedContainer(
            duration: Duration(milliseconds: 200),
            margin: EdgeInsets.only(top: 8.0),
            padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
              border: Border.all(
                color: Color(urls.textSecondary).withOpacity(0.2),
                width: 1.0,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8.0,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 8.0, bottom: 8.0),
                  child: Text(
                    'Suggestions',
                    style: GoogleFonts.poppins(
                      fontSize: 12.0,
                      fontWeight: FontWeight.w500,
                      color: Color(urls.textSecondary),
                    ),
                  ),
                ),
                Wrap(
                  spacing: 8.0,
                  runSpacing: 8.0,
                  children: _filteredSuggestions.map((tag) {
                    return InkWell(
                      onTap: () => _addTag(tag),
                      borderRadius: BorderRadius.circular(16.0),
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
                        decoration: BoxDecoration(
                          color: Color(urls.journalSecondaryColor).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(16.0),
                          border: Border.all(
                            color: Color(urls.journalSecondaryColor).withOpacity(0.2),
                            width: 1.0,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              tag,
                              style: GoogleFonts.poppins(
                                fontSize: 12.0,
                                color: Color(urls.journalPrimaryColor),
                              ),
                            ),
                            SizedBox(width: 4.0),
                            Icon(
                              Icons.add,
                              size: 14.0,
                              color: Color(urls.journalPrimaryColor),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        
        // Selected tags
        if (widget.tags.isNotEmpty)
          Container(
            margin: EdgeInsets.only(top: 16.0),
            child: Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: widget.tags.map((tag) {
                return AnimatedScale(
                  scale: 1.0,
                  duration: Duration(milliseconds: 200),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
                    decoration: BoxDecoration(
                      color: Color(urls.journalPrimaryColor).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16.0),
                      border: Border.all(
                        color: Color(urls.journalPrimaryColor).withOpacity(0.2),
                        width: 1.0,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '#$tag',
                          style: GoogleFonts.poppins(
                            fontSize: 12.0,
                            fontWeight: FontWeight.w500,
                            color: Color(urls.journalPrimaryColor),
                          ),
                        ),
                        SizedBox(width: 4.0),
                        InkWell(
                          onTap: () => _removeTag(tag),
                          borderRadius: BorderRadius.circular(12.0),
                          child: Padding(
                            padding: EdgeInsets.all(2.0),
                            child: Icon(
                              Icons.close,
                              size: 14.0,
                              color: Color(urls.journalPrimaryColor),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
      ],
    );
  }
} 