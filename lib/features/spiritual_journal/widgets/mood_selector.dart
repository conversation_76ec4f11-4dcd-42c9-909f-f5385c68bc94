import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../URLs.dart';
import '../models/journal_entry.dart';

class MoodSelector extends StatelessWidget {
  final MoodLevel selectedMood;
  final Function(MoodLevel) onMoodSelected;

  const MoodSelector({
    Key? key,
    required this.selectedMood,
    required this.onMoodSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    final screenWidth = MediaQuery.of(context).size.width;
    final itemWidth = (screenWidth - 120) / 5; // Adjust based on screen width
    
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: Color(urls.journalPrimaryColor).withOpacity(0.1),
            blurRadius: 10.0,
            offset: Offset(0, 4),
          ),
        ],
      ),
      padding: EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: Color(urls.journalPrimaryColor).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Icon(
                  FontAwesomeIcons.faceSmile,
                  size: 16.0,
                  color: Color(urls.journalPrimaryColor),
                ),
              ),
              SizedBox(width: 12.0),
              Expanded(
                child: Text(
                  'How are you feeling spiritually today?',
                  style: GoogleFonts.poppins(
                    fontSize: 16.0,
                    fontWeight: FontWeight.w600,
                    color: Color(urls.journalPrimaryColor),
                    letterSpacing: 0.5,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.0),
          Text(
            'Select the mood that best reflects your spiritual state',
            style: GoogleFonts.poppins(
              fontSize: 14.0,
              color: Color(urls.textSecondary),
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
          SizedBox(height: 24.0),
          Container(
            height: 140,
            child: ListView(
              scrollDirection: Axis.horizontal,
              physics: BouncingScrollPhysics(),
              children: [
                _buildMoodOption(
                  context,
                  MoodLevel.excellent,
                  FontAwesomeIcons.faceSmileBeam,
                  'Excellent',
                  Color(urls.moodExcellent),
                  'Alhamdulillah',
                  itemWidth,
                ),
                SizedBox(width: 8.0),
                _buildMoodOption(
                  context,
                  MoodLevel.good,
                  FontAwesomeIcons.faceSmile,
                  'Good',
                  Color(urls.moodGood),
                  'Grateful',
                  itemWidth,
                ),
                SizedBox(width: 8.0),
                _buildMoodOption(
                  context,
                  MoodLevel.neutral,
                  FontAwesomeIcons.faceMeh,
                  'Neutral',
                  Color(urls.moodNeutral),
                  'Content',
                  itemWidth,
                ),
                SizedBox(width: 8.0),
                _buildMoodOption(
                  context,
                  MoodLevel.low,
                  FontAwesomeIcons.faceFrown,
                  'Low',
                  Color(urls.moodLow),
                  'Struggling',
                  itemWidth,
                ),
                SizedBox(width: 8.0),
                _buildMoodOption(
                  context,
                  MoodLevel.poor,
                  FontAwesomeIcons.faceSadTear,
                  'Poor',
                  Color(urls.moodPoor),
                  'Seeking Help',
                  itemWidth,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMoodOption(
    BuildContext context,
    MoodLevel mood,
    IconData icon,
    String label,
    Color color,
    String subtitle,
    double width,
  ) {
    final isSelected = selectedMood == mood;
    
    return GestureDetector(
      onTap: () => onMoodSelected(mood),
      child: AnimatedContainer(
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        width: width,
        padding: EdgeInsets.symmetric(vertical: 16.0, horizontal: 4.0),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [
                    color,
                    color.withOpacity(0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected ? null : Colors.white,
          borderRadius: BorderRadius.circular(16.0),
          border: Border.all(
            color: isSelected ? color : Colors.grey.withOpacity(0.3),
            width: isSelected ? 2.0 : 1.0,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: color.withOpacity(0.3),
              blurRadius: 8.0,
              spreadRadius: 1.0,
              offset: Offset(0, 4),
            ),
          ] : [],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedContainer(
              duration: Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              transform: isSelected 
                  ? (Matrix4.identity()..scale(1.2))
                  : Matrix4.identity(),
              transformAlignment: Alignment.center,
              child: Icon(
                icon,
                color: isSelected ? Colors.white : Colors.grey,
                size: 28.0,
              ),
            ),
            SizedBox(height: 8.0),
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 12.0,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected ? Colors.white : Colors.grey,
                letterSpacing: 0.5,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 4.0),
            Text(
              subtitle,
              style: GoogleFonts.poppins(
                fontSize: 10.0,
                fontStyle: FontStyle.italic,
                color: isSelected ? Colors.white.withOpacity(0.9) : Colors.grey.withOpacity(0.7),
                letterSpacing: 0.3,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
} 