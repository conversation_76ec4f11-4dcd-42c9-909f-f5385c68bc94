import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../URLs.dart';
import 'journal_search_field.dart';

class JournalAppBar extends StatelessWidget implements PreferredSizeWidget {
  final TabController tabController;
  final bool showSearch;
  final TextEditingController searchController;
  final VoidCallback onSearchToggle;
  final VoidCallback onFilterTap;
  final ValueChanged<String> onSearchChanged;
  
  const JournalAppBar({
    Key? key,
    required this.tabController,
    required this.showSearch,
    required this.searchController,
    required this.onSearchToggle,
    required this.onFilterTap,
    required this.onSearchChanged,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return AppBar(
      backgroundColor: Color(urls.journalPrimaryColor),
      elevation: 0,
      title: showSearch 
        ? JournalSearchField(
            controller: searchController,
            onClose: onSearchToggle,
            onChanged: onSearchChanged,
          )
        : Row(
            children: [
              Icon(
                FontAwesomeIcons.bookOpen,
                size: 20,
                color: Colors.white,
              ),
              SizedBox(width: 12),
              Text(
                'Spiritual Journal',
                style: GoogleFonts.poppins(
                  fontSize: 20.0,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
      actions: [
        if (!showSearch)
          IconButton(
            icon: Icon(Icons.search),
            onPressed: onSearchToggle,
            tooltip: 'Search journal entries',
          ),
        IconButton(
          icon: Icon(Icons.filter_list),
          onPressed: onFilterTap,
          tooltip: 'Filter entries',
        ),
      ],
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(60.0),
        child: Container(
          decoration: BoxDecoration(
            color: Color(urls.journalPrimaryColor),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                offset: Offset(0, 4),
                blurRadius: 6.0,
              ),
            ],
          ),
          child: TabBar(
            controller: tabController,
            indicatorColor: Colors.white,
            indicatorWeight: 3.0,
            indicatorSize: TabBarIndicatorSize.label,
            labelStyle: GoogleFonts.poppins(
              fontSize: 14.0,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.5,
            ),
            unselectedLabelStyle: GoogleFonts.poppins(
              fontSize: 14.0,
              fontWeight: FontWeight.w400,
              letterSpacing: 0.5,
            ),
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white.withOpacity(0.7),
            tabs: [
              Tab(
                icon: Icon(FontAwesomeIcons.book, size: 16),
                text: 'All',
              ),
              Tab(
                icon: Icon(FontAwesomeIcons.solidHeart, size: 16),
                text: 'Favorites',
              ),
              Tab(
                icon: Icon(FontAwesomeIcons.faceMeh, size: 16),
                text: 'By Mood',
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight + 60.0);
} 