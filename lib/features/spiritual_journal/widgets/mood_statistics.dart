import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../URLs.dart';
import '../models/journal_entry.dart';

class MoodStatistics extends StatelessWidget {
  final Map<MoodLevel, int> moodDistribution;
  final int totalEntries;

  const MoodStatistics({
    Key? key,
    required this.moodDistribution,
    required this.totalEntries,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Card(
      elevation: urls.cardElevation,
      margin: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Spiritual Mood Trends',
              style: GoogleFonts.poppins(
                fontSize: 18.0,
                fontWeight: FontWeight.w600,
                color: Color(urls.journalPrimaryColor),
              ),
            ),
            SizedBox(height: 16.0),
            
            if (totalEntries == 0)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 24.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.note_add,
                        size: 48.0,
                        color: Color(urls.textSecondary).withOpacity(0.5),
                      ),
                      SizedBox(height: 16.0),
                      Text(
                        'No journal entries yet',
                        style: GoogleFonts.poppins(
                          fontSize: 16.0,
                          color: Color(urls.textSecondary),
                        ),
                      ),
                      SizedBox(height: 8.0),
                      Text(
                        'Start journaling to see your mood trends',
                        style: GoogleFonts.poppins(
                          fontSize: 14.0,
                          color: Color(urls.textSecondary).withOpacity(0.7),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              )
            else
              Column(
                children: [
                  _buildMoodBar(
                    'Excellent',
                    moodDistribution[MoodLevel.excellent] ?? 0,
                    totalEntries,
                    Color(urls.moodExcellent),
                  ),
                  SizedBox(height: 12.0),
                  _buildMoodBar(
                    'Good',
                    moodDistribution[MoodLevel.good] ?? 0,
                    totalEntries,
                    Color(urls.moodGood),
                  ),
                  SizedBox(height: 12.0),
                  _buildMoodBar(
                    'Neutral',
                    moodDistribution[MoodLevel.neutral] ?? 0,
                    totalEntries,
                    Color(urls.moodNeutral),
                  ),
                  SizedBox(height: 12.0),
                  _buildMoodBar(
                    'Low',
                    moodDistribution[MoodLevel.low] ?? 0,
                    totalEntries,
                    Color(urls.moodLow),
                  ),
                  SizedBox(height: 12.0),
                  _buildMoodBar(
                    'Poor',
                    moodDistribution[MoodLevel.poor] ?? 0,
                    totalEntries,
                    Color(urls.moodPoor),
                  ),
                  
                  SizedBox(height: 24.0),
                  
                  // Insights
                  _buildInsights(),
                ],
              ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildMoodBar(String label, int count, int total, Color color) {
    final percentage = total > 0 ? (count / total * 100) : 0.0;
    
    return Row(
      children: [
        SizedBox(
          width: 80.0,
          child: Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 14.0,
              color: Color(URLs().textPrimary),
            ),
          ),
        ),
        Expanded(
          child: Stack(
            children: [
              Container(
                height: 16.0,
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.0),
                ),
              ),
              FractionallySizedBox(
                widthFactor: percentage / 100,
                child: Container(
                  height: 16.0,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: 8.0),
        SizedBox(
          width: 40.0,
          child: Text(
            '${percentage.toStringAsFixed(0)}%',
            style: GoogleFonts.poppins(
              fontSize: 14.0,
              fontWeight: FontWeight.w500,
              color: Color(URLs().textPrimary),
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }
  
  Widget _buildInsights() {
    if (totalEntries < 3) return SizedBox.shrink();
    
    final urls = URLs();
    String insightText = '';
    IconData insightIcon;
    Color insightColor;
    
    // Calculate dominant mood
    MoodLevel? dominantMood;
    int maxCount = 0;
    
    moodDistribution.forEach((mood, count) {
      if (count > maxCount) {
        maxCount = count;
        dominantMood = mood;
      }
    });
    
    // Generate insight based on dominant mood
    switch (dominantMood) {
      case MoodLevel.excellent:
        insightText = 'Your spiritual state has been excellent! Continue with your current practices and share your blessings with others.';
        insightIcon = Icons.emoji_emotions;
        insightColor = Color(urls.moodExcellent);
        break;
      case MoodLevel.good:
        insightText = 'You\'ve been maintaining a good spiritual state. Consider adding more dhikr to elevate your connection further.';
        insightIcon = Icons.sentiment_satisfied;
        insightColor = Color(urls.moodGood);
        break;
      case MoodLevel.neutral:
        insightText = 'Your spiritual state has been balanced. Try incorporating more Quran recitation to strengthen your connection.';
        insightIcon = Icons.sentiment_neutral;
        insightColor = Color(urls.moodNeutral);
        break;
      case MoodLevel.low:
        insightText = 'You\'ve been experiencing some spiritual lows. Consider increasing your prayers and seeking knowledge to uplift your state.';
        insightIcon = Icons.sentiment_dissatisfied;
        insightColor = Color(urls.moodLow);
        break;
      case MoodLevel.poor:
        insightText = 'You\'ve been going through spiritual challenges. Remember that Allah tests those He loves. Increase your dua and seek support from your community.';
        insightIcon = Icons.sentiment_very_dissatisfied;
        insightColor = Color(urls.moodPoor);
        break;
      default:
        return SizedBox.shrink();
    }
    
    return Container(
      padding: EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: insightColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
        border: Border.all(
          color: insightColor.withOpacity(0.3),
          width: 1.0,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            insightIcon,
            color: insightColor,
            size: 24.0,
          ),
          SizedBox(width: 12.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Spiritual Insight',
                  style: GoogleFonts.poppins(
                    fontSize: 16.0,
                    fontWeight: FontWeight.w600,
                    color: insightColor,
                  ),
                ),
                SizedBox(height: 4.0),
                Text(
                  insightText,
                  style: GoogleFonts.poppins(
                    fontSize: 14.0,
                    color: Color(urls.textPrimary),
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 