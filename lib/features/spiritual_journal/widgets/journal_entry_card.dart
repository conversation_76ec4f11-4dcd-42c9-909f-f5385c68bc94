import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../URLs.dart';
import '../models/journal_entry.dart';

class JournalEntryCard extends StatefulWidget {
  final JournalEntry entry;
  final VoidCallback onTap;
  final VoidCallback onFavorite;
  final VoidCallback? onDelete;
  final bool isDetailed;

  const JournalEntryCard({
    Key? key,
    required this.entry,
    required this.onTap,
    required this.onFavorite,
    this.onDelete,
    this.isDetailed = false,
  }) : super(key: key);

  @override
  _JournalEntryCardState createState() => _JournalEntryCardState();
}

class _JournalEntryCardState extends State<JournalEntryCard> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 150),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (!widget.isDetailed) {
      setState(() {
        _isPressed = true;
      });
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (!widget.isDetailed) {
      setState(() {
        _isPressed = false;
      });
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (!widget.isDetailed) {
      setState(() {
        _isPressed = false;
      });
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: child,
        );
      },
      child: GestureDetector(
        onTapDown: _onTapDown,
        onTapUp: _onTapUp,
        onTapCancel: _onTapCancel,
        onTap: widget.onTap,
        child: Card(
          elevation: widget.isDetailed ? urls.cardElevation : (_isPressed ? 1.0 : 3.0),
          shadowColor: widget.entry.getMoodColor().withOpacity(0.3),
          margin: EdgeInsets.symmetric(
            vertical: 10.0,
            horizontal: widget.isDetailed ? 0.0 : 16.0,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
            side: BorderSide(
              color: widget.entry.getMoodColor().withOpacity(0.3),
              width: 1.0,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with mood and date
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 12.0,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      widget.entry.getMoodColor().withOpacity(0.2),
                      widget.entry.getMoodColor().withOpacity(0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16.0),
                    topRight: Radius.circular(16.0),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Row(
                        children: [
                          Container(
                            padding: EdgeInsets.all(8.0),
                            decoration: BoxDecoration(
                              color: widget.entry.getMoodColor().withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              widget.entry.getMoodIcon(),
                              color: widget.entry.getMoodColor(),
                              size: 24.0,
                            ),
                          ),
                          SizedBox(width: 12.0),
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.entry.getMoodText(),
                                  style: GoogleFonts.poppins(
                                    fontSize: 16.0,
                                    fontWeight: FontWeight.w600,
                                    color: widget.entry.getMoodColor(),
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                                Text(
                                  widget.entry.formattedDate,
                                  style: GoogleFonts.poppins(
                                    fontSize: 12.0,
                                    color: Color(urls.textSecondary),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (widget.entry.isFavorite)
                      Container(
                        padding: EdgeInsets.all(6.0),
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.favorite,
                          color: Colors.red,
                          size: 16.0,
                        ),
                      ),
                  ],
                ),
              ),
              
              // Content
              Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (widget.isDetailed) ...[
                      _buildSectionTitle('Gratitude'),
                      _buildSectionContent(widget.entry.gratitude),
                      SizedBox(height: 16.0),
                      
                      _buildSectionTitle('Reflection'),
                      _buildSectionContent(widget.entry.reflection),
                      SizedBox(height: 16.0),
                      
                      _buildSectionTitle('Prayer'),
                      _buildSectionContent(widget.entry.prayer),
                      SizedBox(height: 16.0),
                      
                      if (widget.entry.islamicReminder.isNotEmpty) ...[
                        _buildSectionTitle('Islamic Reminder'),
                        Container(
                          padding: EdgeInsets.all(16.0),
                          decoration: BoxDecoration(
                            color: Color(urls.journalSecondaryColor).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16.0),
                            border: Border.all(
                              color: Color(urls.journalSecondaryColor).withOpacity(0.3),
                              width: 1.0,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    FontAwesomeIcons.bookQuran,
                                    size: 14.0,
                                    color: Color(urls.journalPrimaryColor),
                                  ),
                                  SizedBox(width: 8.0),
                                  Text(
                                    'Spiritual Guidance',
                                    style: GoogleFonts.poppins(
                                      fontSize: 14.0,
                                      fontWeight: FontWeight.w600,
                                      color: Color(urls.journalPrimaryColor),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 12.0),
                              Text(
                                widget.entry.islamicReminder,
                                style: GoogleFonts.poppins(
                                  fontSize: 14.0,
                                  fontStyle: FontStyle.italic,
                                  color: Color(urls.textPrimary),
                                  height: 1.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 16.0),
                      ],
                      
                      if (widget.entry.tags.isNotEmpty) ...[
                        _buildSectionTitle('Tags'),
                        Wrap(
                          spacing: 8.0,
                          runSpacing: 8.0,
                          children: widget.entry.tags.map((tag) => _buildTag(tag, urls)).toList(),
                        ),
                      ],
                    ] else ...[
                      // Preview mode - show only gratitude
                      Text(
                        widget.entry.gratitude,
                        style: GoogleFonts.poppins(
                          fontSize: 14.0,
                          color: Color(urls.textPrimary),
                          height: 1.5,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      if (widget.entry.tags.isNotEmpty) ...[
                        SizedBox(height: 12.0),
                        SizedBox(
                          width: double.infinity,
                          child: Wrap(
                            spacing: 8.0,
                            runSpacing: 8.0,
                            children: widget.entry.tags.take(3).map((tag) => _buildTag(tag, urls)).toList(),
                          ),
                        ),
                      ],
                    ],
                  ],
                ),
              ),
              
              // Footer with actions
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    _buildActionButton(
                      widget.entry.isFavorite ? FontAwesomeIcons.solidHeart : FontAwesomeIcons.heart,
                      widget.entry.isFavorite ? Colors.red : Color(urls.textSecondary),
                      'Favorite',
                      widget.onFavorite,
                    ),
                    if (widget.onDelete != null && widget.isDetailed)
                      _buildActionButton(
                        FontAwesomeIcons.trash,
                        Color(urls.textSecondary),
                        'Delete',
                        widget.onDelete!,
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildSectionTitle(String title) {
    final urls = URLs();
    return Padding(
      padding: EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(
            _getSectionIcon(title),
            size: 14.0,
            color: Color(urls.journalPrimaryColor),
          ),
          SizedBox(width: 8.0),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 16.0,
              fontWeight: FontWeight.w600,
              color: Color(urls.journalPrimaryColor),
            ),
          ),
        ],
      ),
    );
  }
  
  IconData _getSectionIcon(String title) {
    switch (title) {
      case 'Gratitude':
        return FontAwesomeIcons.handsPraying;
      case 'Reflection':
        return FontAwesomeIcons.lightbulb;
      case 'Prayer':
        return FontAwesomeIcons.solidHeart;
      case 'Islamic Reminder':
        return FontAwesomeIcons.bookQuran;
      case 'Tags':
        return FontAwesomeIcons.tags;
      default:
        return FontAwesomeIcons.solidCircle;
    }
  }
  
  Widget _buildSectionContent(String content) {
    final urls = URLs();
    return Text(
      content,
      style: GoogleFonts.poppins(
        fontSize: 14.0,
        color: Color(urls.textPrimary),
        height: 1.5,
      ),
    );
  }
  
  Widget _buildTag(String tag, URLs urls) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
      decoration: BoxDecoration(
        color: Color(urls.journalSecondaryColor).withOpacity(0.1),
        borderRadius: BorderRadius.circular(16.0),
        border: Border.all(
          color: Color(urls.journalSecondaryColor).withOpacity(0.2),
          width: 1.0,
        ),
      ),
      child: Text(
        '#$tag',
        style: GoogleFonts.poppins(
          fontSize: 12.0,
          color: Color(urls.journalPrimaryColor),
        ),
      ),
    );
  }
  
  Widget _buildActionButton(
    IconData icon,
    Color color,
    String tooltip,
    VoidCallback onPressed,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(20.0),
        child: Padding(
          padding: EdgeInsets.all(8.0),
          child: Tooltip(
            message: tooltip,
            child: Icon(
              icon,
              color: color,
              size: 18.0,
            ),
          ),
        ),
      ),
    );
  }
} 