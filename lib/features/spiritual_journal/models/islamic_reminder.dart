class IslamicReminder {
  final String id;
  final String title;
  final String content;
  final String source;
  final String category;
  final List<String> tags;

  IslamicReminder({
    required this.id,
    required this.title,
    required this.content,
    required this.source,
    required this.category,
    this.tags = const [],
  });

  // Convert IslamicReminder to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'source': source,
      'category': category,
      'tags': tags,
    };
  }

  // Create IslamicReminder from JSON
  factory IslamicReminder.fromJson(Map<String, dynamic> json) {
    return IslamicReminder(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      source: json['source'],
      category: json['category'],
      tags: List<String>.from(json['tags'] ?? []),
    );
  }
}

// Predefined Islamic reminders for spiritual guidance
class IslamicReminderData {
  static List<IslamicReminder> getReminders() {
    return [
      IslamicReminder(
        id: '1',
        title: 'Gratitude in Islam',
        content: 'The Prophet Muhammad (peace be upon him) said: "Wondrous is the affair of the believer for there is good for him in every matter and this is not the case with anyone except the believer. If he is happy, then he thanks <PERSON> and thus there is good for him. If he is harmed, then he shows patience and thus there is good for him." (Muslim)',
        source: 'Sahih Muslim',
        category: 'Gratitude',
        tags: ['gratitude', 'patience', 'faith'],
      ),
      IslamicReminder(
        id: '2',
        title: 'Dealing with Anxiety',
        content: 'Allah says in the Quran: "Verily, in the remembrance of Allah do hearts find rest." (Surah Ar-Ra\'d 13:28). Remember Allah often when feeling anxious or overwhelmed.',
        source: 'Quran 13:28',
        category: 'Anxiety',
        tags: ['anxiety', 'peace', 'remembrance'],
      ),
      IslamicReminder(
        id: '3',
        title: 'Patience in Hardship',
        content: 'Allah says: "And certainly, We shall test you with something of fear, hunger, loss of wealth, lives and fruits, but give glad tidings to the patient ones." (Surah Al-Baqarah 2:155)',
        source: 'Quran 2:155',
        category: 'Patience',
        tags: ['patience', 'hardship', 'test'],
      ),
      IslamicReminder(
        id: '4',
        title: 'Seeking Forgiveness',
        content: 'The Prophet Muhammad (peace be upon him) said: "By Allah, I seek forgiveness from Allah and turn to Him in repentance more than seventy times a day." (Bukhari)',
        source: 'Sahih Bukhari',
        category: 'Forgiveness',
        tags: ['forgiveness', 'repentance', 'mercy'],
      ),
      IslamicReminder(
        id: '5',
        title: 'Importance of Dua',
        content: 'The Prophet Muhammad (peace be upon him) said: "Dua is the essence of worship." (Tirmidhi). Make dua consistently, especially in times of need.',
        source: 'Tirmidhi',
        category: 'Dua',
        tags: ['dua', 'prayer', 'worship'],
      ),
      IslamicReminder(
        id: '6',
        title: 'Controlling Anger',
        content: 'The Prophet Muhammad (peace be upon him) said: "The strong is not the one who overcomes the people by his strength, but the strong is the one who controls himself while in anger." (Bukhari)',
        source: 'Sahih Bukhari',
        category: 'Anger',
        tags: ['anger', 'control', 'strength'],
      ),
      IslamicReminder(
        id: '7',
        title: 'Kindness to Others',
        content: 'The Prophet Muhammad (peace be upon him) said: "Kindness is a mark of faith, and whoever is not kind has no faith." (Muslim)',
        source: 'Sahih Muslim',
        category: 'Kindness',
        tags: ['kindness', 'faith', 'character'],
      ),
      IslamicReminder(
        id: '8',
        title: 'Contentment',
        content: 'The Prophet Muhammad (peace be upon him) said: "Richness is not having many possessions. Rather, true richness is the richness of the soul." (Bukhari and Muslim)',
        source: 'Bukhari and Muslim',
        category: 'Contentment',
        tags: ['contentment', 'wealth', 'soul'],
      ),
      IslamicReminder(
        id: '9',
        title: 'Hope in Allah',
        content: 'Allah says: "Say, \'O My servants who have transgressed against themselves [by sinning], do not despair of the mercy of Allah. Indeed, Allah forgives all sins. Indeed, it is He who is the Forgiving, the Merciful.\'" (Surah Az-Zumar 39:53)',
        source: 'Quran 39:53',
        category: 'Hope',
        tags: ['hope', 'mercy', 'forgiveness'],
      ),
      IslamicReminder(
        id: '10',
        title: 'Importance of Reflection',
        content: 'Allah says in the Quran: "Indeed, in the creation of the heavens and the earth and the alternation of the night and the day are signs for those of understanding." (Surah Ali \'Imran 3:190). Take time to reflect on Allah\'s creation and signs.',
        source: 'Quran 3:190',
        category: 'Reflection',
        tags: ['reflection', 'creation', 'understanding'],
      ),
    ];
  }

  // Get a reminder based on mood level and tags
  static IslamicReminder getRandomReminderForMood(String mood, List<String> tags) {
    List<IslamicReminder> allReminders = getReminders();
    List<IslamicReminder> matchingReminders = [];
    
    // First try to find reminders that match both mood and tags
    for (var reminder in allReminders) {
      bool moodMatch = reminder.category.toLowerCase() == mood.toLowerCase() ||
                      reminder.tags.contains(mood.toLowerCase());
      
      bool tagMatch = false;
      if (tags.isNotEmpty) {
        for (var tag in tags) {
          if (reminder.tags.contains(tag.toLowerCase())) {
            tagMatch = true;
            break;
          }
        }
      } else {
        tagMatch = true; // If no tags provided, consider it a match
      }
      
      if (moodMatch && tagMatch) {
        matchingReminders.add(reminder);
      }
    }
    
    // If no matches found, just return based on mood
    if (matchingReminders.isEmpty) {
      for (var reminder in allReminders) {
        if (reminder.category.toLowerCase() == mood.toLowerCase() ||
            reminder.tags.contains(mood.toLowerCase())) {
          matchingReminders.add(reminder);
        }
      }
    }
    
    // If still no matches, return a random reminder
    if (matchingReminders.isEmpty) {
      matchingReminders = allReminders;
    }
    
    // Return a random reminder from the matching ones
    return matchingReminders[DateTime.now().millisecond % matchingReminders.length];
  }
} 