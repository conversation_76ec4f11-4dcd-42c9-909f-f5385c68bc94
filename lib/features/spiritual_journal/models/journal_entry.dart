import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

enum MoodLevel {
  excellent,
  good,
  neutral,
  low,
  poor,
}

class JournalEntry {
  final String id;
  final DateTime date;
  final MoodLevel moodLevel;
  final String gratitude;
  final String reflection;
  final String prayer;
  final List<String> tags;
  final String islamicReminder;
  final bool isFavorite;

  JournalEntry({
    required this.id,
    required this.date,
    required this.moodLevel,
    required this.gratitude,
    required this.reflection,
    required this.prayer,
    this.tags = const [],
    this.islamicReminder = '',
    this.isFavorite = false,
  });

  // Create a copy of the journal entry with updated fields
  JournalEntry copyWith({
    String? id,
    DateTime? date,
    MoodLevel? moodLevel,
    String? gratitude,
    String? reflection,
    String? prayer,
    List<String>? tags,
    String? islamicReminder,
    bool? isFavorite,
  }) {
    return JournalEntry(
      id: id ?? this.id,
      date: date ?? this.date,
      moodLevel: moodLevel ?? this.moodLevel,
      gratitude: gratitude ?? this.gratitude,
      reflection: reflection ?? this.reflection,
      prayer: prayer ?? this.prayer,
      tags: tags ?? this.tags,
      islamicReminder: islamicReminder ?? this.islamicReminder,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }

  // Convert JournalEntry to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'moodLevel': moodLevel.toString().split('.').last,
      'gratitude': gratitude,
      'reflection': reflection,
      'prayer': prayer,
      'tags': tags,
      'islamicReminder': islamicReminder,
      'isFavorite': isFavorite,
    };
  }

  // Create JournalEntry from JSON
  factory JournalEntry.fromJson(Map<String, dynamic> json) {
    return JournalEntry(
      id: json['id'],
      date: DateTime.parse(json['date']),
      moodLevel: MoodLevel.values.firstWhere(
        (e) => e.toString().split('.').last == json['moodLevel'],
        orElse: () => MoodLevel.neutral,
      ),
      gratitude: json['gratitude'],
      reflection: json['reflection'],
      prayer: json['prayer'],
      tags: List<String>.from(json['tags'] ?? []),
      islamicReminder: json['islamicReminder'] ?? '',
      isFavorite: json['isFavorite'] ?? false,
    );
  }

  // Format date as a readable string
  String get formattedDate => DateFormat('EEEE, MMMM d, yyyy').format(date);
  
  // Format time as a readable string
  String get formattedTime => DateFormat('h:mm a').format(date);
  
  // Get color based on mood level
  Color getMoodColor() {
    switch (moodLevel) {
      case MoodLevel.excellent:
        return Color(0xFF2ecc71);
      case MoodLevel.good:
        return Color(0xFF3498db);
      case MoodLevel.neutral:
        return Color(0xFFf39c12);
      case MoodLevel.low:
        return Color(0xFFe67e22);
      case MoodLevel.poor:
        return Color(0xFFe74c3c);
    }
  }
  
  // Get mood icon based on mood level
  IconData getMoodIcon() {
    switch (moodLevel) {
      case MoodLevel.excellent:
        return Icons.sentiment_very_satisfied;
      case MoodLevel.good:
        return Icons.sentiment_satisfied;
      case MoodLevel.neutral:
        return Icons.sentiment_neutral;
      case MoodLevel.low:
        return Icons.sentiment_dissatisfied;
      case MoodLevel.poor:
        return Icons.sentiment_very_dissatisfied;
    }
  }
  
  // Get mood text based on mood level
  String getMoodText() {
    switch (moodLevel) {
      case MoodLevel.excellent:
        return 'Excellent';
      case MoodLevel.good:
        return 'Good';
      case MoodLevel.neutral:
        return 'Neutral';
      case MoodLevel.low:
        return 'Low';
      case MoodLevel.poor:
        return 'Poor';
    }
  }
} 