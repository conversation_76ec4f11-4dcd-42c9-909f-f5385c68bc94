import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/journal_entry.dart';
import '../models/islamic_reminder.dart';
import 'package:intl/intl.dart';

class JournalController extends ChangeNotifier {
  List<JournalEntry> _entries = [];
  List<JournalEntry> get entries => _entries;
  
  List<JournalEntry> _favoriteEntries = [];
  List<JournalEntry> get favoriteEntries => _favoriteEntries;
  
  bool _isLoading = false;
  bool get isLoading => _isLoading;
  
  String _errorMessage = '';
  String get errorMessage => _errorMessage;
  
  // Initialize the controller
  Future<void> initialize() async {
    await loadEntries();
  }
  
  // Load entries from local storage
  Future<void> loadEntries() async {
    _setLoading(true);
    _clearError();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final entriesJson = prefs.getStringList('journal_entries') ?? [];
      
      _entries = entriesJson
          .map((json) => JournalEntry.fromJson(jsonDecode(json)))
          .toList();
      
      // Sort entries by date (newest first)
      _entries.sort((a, b) => b.date.compareTo(a.date));
      
      // Filter favorite entries
      _favoriteEntries = _entries.where((entry) => entry.isFavorite).toList();
      
      _setLoading(false);
    } catch (e) {
      _setError('Failed to load journal entries: $e');
    }
  }
  
  // Save entries to local storage
  Future<void> saveEntries() async {
    _setLoading(true);
    _clearError();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final entriesJson = _entries
          .map((entry) => jsonEncode(entry.toJson()))
          .toList();
      
      await prefs.setStringList('journal_entries', entriesJson);
      
      _setLoading(false);
    } catch (e) {
      _setError('Failed to save journal entries: $e');
    }
  }
  
  // Add a new journal entry
  Future<void> addEntry({
    required MoodLevel moodLevel,
    required String gratitude,
    required String reflection,
    required String prayer,
    List<String> tags = const [],
  }) async {
    _clearError();
    
    try {
      final uuid = Uuid();
      final newEntry = JournalEntry(
        id: uuid.v4(),
        date: DateTime.now(),
        moodLevel: moodLevel,
        gratitude: gratitude,
        reflection: reflection,
        prayer: prayer,
        tags: tags,
        islamicReminder: _getIslamicReminderForMood(moodLevel, tags),
      );
      
      _entries.insert(0, newEntry); // Add to beginning of list
      
      await saveEntries();
      notifyListeners();
    } catch (e) {
      _setError('Failed to add journal entry: $e');
    }
  }
  
  // Update an existing journal entry
  Future<void> updateEntry(JournalEntry updatedEntry) async {
    _clearError();
    
    try {
      final index = _entries.indexWhere((entry) => entry.id == updatedEntry.id);
      
      if (index != -1) {
        _entries[index] = updatedEntry;
        
        // Update favorite entries if needed
        if (updatedEntry.isFavorite) {
          final favIndex = _favoriteEntries.indexWhere((entry) => entry.id == updatedEntry.id);
          if (favIndex != -1) {
            _favoriteEntries[favIndex] = updatedEntry;
          } else {
            _favoriteEntries.add(updatedEntry);
          }
        } else {
          _favoriteEntries.removeWhere((entry) => entry.id == updatedEntry.id);
        }
        
        await saveEntries();
        notifyListeners();
      } else {
        _setError('Entry not found');
      }
    } catch (e) {
      _setError('Failed to update journal entry: $e');
    }
  }
  
  // Delete a journal entry
  Future<void> deleteEntry(String id) async {
    _clearError();
    
    try {
      _entries.removeWhere((entry) => entry.id == id);
      _favoriteEntries.removeWhere((entry) => entry.id == id);
      
      await saveEntries();
      notifyListeners();
    } catch (e) {
      _setError('Failed to delete journal entry: $e');
    }
  }
  
  // Toggle favorite status of an entry
  Future<void> toggleFavorite(String id) async {
    _clearError();
    
    try {
      final index = _entries.indexWhere((entry) => entry.id == id);
      
      if (index != -1) {
        final entry = _entries[index];
        final updatedEntry = entry.copyWith(isFavorite: !entry.isFavorite);
        
        _entries[index] = updatedEntry;
        
        if (updatedEntry.isFavorite) {
          _favoriteEntries.add(updatedEntry);
        } else {
          _favoriteEntries.removeWhere((e) => e.id == id);
        }
        
        await saveEntries();
        notifyListeners();
      } else {
        _setError('Entry not found');
      }
    } catch (e) {
      _setError('Failed to update favorite status: $e');
    }
  }
  
  // Get entries filtered by mood level
  List<JournalEntry> getEntriesByMood(MoodLevel moodLevel) {
    return _entries.where((entry) => entry.moodLevel == moodLevel).toList();
  }
  
  // Get entries filtered by date range
  List<JournalEntry> getEntriesByDateRange(DateTime start, DateTime end) {
    return _entries.where((entry) => 
      entry.date.isAfter(start) && entry.date.isBefore(end.add(Duration(days: 1)))
    ).toList();
  }
  
  // Filter entries by date range and notify listeners
  void filterByDateRange(DateTime start, DateTime end) {
    _filteredEntries = getEntriesByDateRange(start, end);
    _isFiltered = true;
    _filterDescription = 'Entries from ${DateFormat('MMM d, yyyy').format(start)} to ${DateFormat('MMM d, yyyy').format(end)}';
    notifyListeners();
  }
  
  // Clear all filters and return to showing all entries
  void clearFilters() {
    _isFiltered = false;
    _filteredEntries = [];
    _filterDescription = '';
    notifyListeners();
  }
  
  // Variables to track filtered state
  bool _isFiltered = false;
  bool get isFiltered => _isFiltered;
  
  List<JournalEntry> _filteredEntries = [];
  List<JournalEntry> get filteredEntries => _isFiltered ? _filteredEntries : _entries;
  
  String _filterDescription = '';
  String get filterDescription => _filterDescription;
  
  // Get entries filtered by tags
  List<JournalEntry> getEntriesByTags(List<String> tags) {
    return _entries.where((entry) => 
      tags.any((tag) => entry.tags.contains(tag))
    ).toList();
  }
  
  // Get all unique tags from entries
  List<String> getAllTags() {
    final Set<String> tags = {};
    
    for (var entry in _entries) {
      tags.addAll(entry.tags);
    }
    
    return tags.toList()..sort();
  }
  
  // Get mood distribution statistics
  Map<MoodLevel, int> getMoodDistribution() {
    final Map<MoodLevel, int> distribution = {
      MoodLevel.excellent: 0,
      MoodLevel.good: 0,
      MoodLevel.neutral: 0,
      MoodLevel.low: 0,
      MoodLevel.poor: 0,
    };
    
    for (var entry in _entries) {
      distribution[entry.moodLevel] = (distribution[entry.moodLevel] ?? 0) + 1;
    }
    
    return distribution;
  }
  
  // Get Islamic reminder based on mood and tags
  String _getIslamicReminderForMood(MoodLevel moodLevel, List<String> tags) {
    String mood;
    
    switch (moodLevel) {
      case MoodLevel.excellent:
        mood = 'Gratitude';
        break;
      case MoodLevel.good:
        mood = 'Contentment';
        break;
      case MoodLevel.neutral:
        mood = 'Reflection';
        break;
      case MoodLevel.low:
        mood = 'Hope';
        break;
      case MoodLevel.poor:
        mood = 'Patience';
        break;
    }
    
    final reminder = IslamicReminderData.getRandomReminderForMood(mood, tags);
    return '${reminder.title}: ${reminder.content} (${reminder.source})';
  }
  
  // Sort entries using the provided comparison function
  void sortEntries(int Function(JournalEntry a, JournalEntry b) compare) {
    _entries.sort(compare);
    notifyListeners();
  }
  
  // Helper methods for loading state and errors
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setError(String message) {
    _errorMessage = message;
    _isLoading = false;
    notifyListeners();
  }
  
  void _clearError() {
    _errorMessage = '';
    notifyListeners();
  }
} 