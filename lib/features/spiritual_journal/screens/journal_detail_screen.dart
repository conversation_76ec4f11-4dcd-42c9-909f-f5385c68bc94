import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../URLs.dart';
import '../controllers/journal_controller.dart';
import '../models/journal_entry.dart';
import '../widgets/journal_entry_card.dart';
import 'journal_edit_screen.dart';

class JournalDetailScreen extends StatefulWidget {
  final JournalEntry entry;

  const JournalDetailScreen({
    Key? key,
    required this.entry,
  }) : super(key: key);

  @override
  _JournalDetailScreenState createState() => _JournalDetailScreenState();
}

class _JournalDetailScreenState extends State<JournalDetailScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  bool _isDeleting = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 800),
    );
    
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _shareEntry() {
    final entry = widget.entry;
    final shareText = '''
📝 Journal Entry - ${entry.formattedDate}
Mood: ${entry.getMoodText()}

🙏 Gratitude:
${entry.gratitude}

💭 Reflection:
${entry.reflection}

🤲 Prayer:
${entry.prayer}

${entry.islamicReminder.isNotEmpty ? '📖 Islamic Reminder:\n${entry.islamicReminder}\n' : ''}
${entry.tags.isNotEmpty ? '🏷️ Tags: ${entry.tags.map((t) => '#$t').join(' ')}' : ''}

Shared from my Spiritual Mood Journal
''';

    Share.share(shareText);
  }

  void _confirmDelete() {
    final urls = URLs();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              FontAwesomeIcons.trash,
              size: 20,
              color: Colors.red,
            ),
            SizedBox(width: 12),
            Text(
              'Delete Journal Entry?',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
                color: Color(urls.textPrimary),
              ),
            ),
          ],
        ),
        content: Text(
          'This action cannot be undone. Are you sure you want to delete this journal entry?',
          style: GoogleFonts.poppins(
            color: Color(urls.textSecondary),
            height: 1.5,
          ),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(
                color: Color(urls.textSecondary),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: TextButton.icon(
              onPressed: _deleteEntry,
              icon: Icon(
                FontAwesomeIcons.trash,
                size: 14,
                color: Colors.white,
              ),
              label: Text(
                'Delete',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteEntry() async {
    setState(() {
      _isDeleting = true;
    });
    
    try {
      await _animationController.reverse();
      
      if (!mounted) return;
      Navigator.pop(context); // Close dialog
      
      final controller = Provider.of<JournalController>(context, listen: false);
      await controller.deleteEntry(widget.entry.id);
      
      if (mounted) {
        Navigator.pop(context); // Return to list screen
        
        // Show success snackbar
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(FontAwesomeIcons.circleCheck, color: Colors.white, size: 16),
                SizedBox(width: 12),
                Text(
                  'Journal entry deleted successfully',
                  style: GoogleFonts.poppins(),
                ),
              ],
            ),
            backgroundColor: Color(URLs().successColor),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isDeleting = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(FontAwesomeIcons.circleExclamation, color: Colors.white, size: 16),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Failed to delete entry: $e',
                    style: GoogleFonts.poppins(),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  void _navigateToEdit() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => JournalEditScreen(entry: widget.entry),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Scaffold(
      backgroundColor: Color(urls.journalBackgroundColor),
      appBar: AppBar(
        backgroundColor: Color(urls.journalPrimaryColor),
        elevation: 0,
        title: Row(
          children: [
            Icon(
              FontAwesomeIcons.bookOpen,
              size: 18,
              color: Colors.white,
            ),
            SizedBox(width: 12),
            Flexible(
              child: Text(
                'Journal Entry',
                style: GoogleFonts.poppins(
                  fontSize: 20.0,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  letterSpacing: 0.5,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(FontAwesomeIcons.penToSquare, size: 18),
            tooltip: 'Edit',
            onPressed: _navigateToEdit,
          ),
          IconButton(
            icon: Icon(FontAwesomeIcons.shareNodes, size: 18),
            tooltip: 'Share',
            onPressed: _shareEntry,
          ),
          IconButton(
            icon: Icon(FontAwesomeIcons.trash, size: 18),
            tooltip: 'Delete',
            onPressed: _confirmDelete,
          ),
        ],
      ),
      body: _isDeleting
          ? Center(
              child: Container(
                padding: EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Color(urls.journalPrimaryColor).withOpacity(0.1),
                      blurRadius: 20,
                      offset: Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Color(urls.journalPrimaryColor),
                      ),
                    ),
                    SizedBox(height: 24.0),
                    Text(
                      'Deleting entry...',
                      style: GoogleFonts.poppins(
                        fontSize: 16.0,
                        fontWeight: FontWeight.w500,
                        color: Color(urls.textPrimary),
                      ),
                    ),
                    SizedBox(height: 8.0),
                    Text(
                      'This will only take a moment',
                      style: GoogleFonts.poppins(
                        fontSize: 14.0,
                        color: Color(urls.textSecondary),
                      ),
                    ),
                  ],
                ),
              ),
            )
          : Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(urls.journalBackgroundColor),
                    Colors.white,
                  ],
                  stops: [0.0, 0.3],
                ),
              ),
              child: SafeArea(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: SingleChildScrollView(
                      padding: EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Date display with mood badge
                          Container(
                            width: double.infinity,
                            padding: EdgeInsets.symmetric(vertical: 16.0, horizontal: 20.0),
                            margin: EdgeInsets.only(bottom: 20.0),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  widget.entry.getMoodColor(),
                                  widget.entry.getMoodColor().withOpacity(0.7),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(16.0),
                              boxShadow: [
                                BoxShadow(
                                  color: widget.entry.getMoodColor().withOpacity(0.3),
                                  blurRadius: 10.0,
                                  offset: Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(10.0),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.2),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        widget.entry.getMoodIcon(),
                                        color: Colors.white,
                                        size: 24.0,
                                      ),
                                    ),
                                    SizedBox(width: 16.0),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            widget.entry.getMoodText(),
                                            style: GoogleFonts.poppins(
                                              fontSize: 20.0,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.white,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          SizedBox(height: 4.0),
                                          Row(
                                            children: [
                                              Icon(
                                                FontAwesomeIcons.calendar,
                                                size: 12.0,
                                                color: Colors.white.withOpacity(0.8),
                                              ),
                                              SizedBox(width: 6.0),
                                              Flexible(
                                                child: Text(
                                                  widget.entry.formattedDate,
                                                  style: GoogleFonts.poppins(
                                                    fontSize: 14.0,
                                                    color: Colors.white.withOpacity(0.8),
                                                  ),
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    if (widget.entry.isFavorite)
                                      Container(
                                        padding: EdgeInsets.all(8.0),
                                        decoration: BoxDecoration(
                                          color: Colors.white.withOpacity(0.2),
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          FontAwesomeIcons.solidHeart,
                                          color: Colors.white,
                                          size: 16.0,
                                        ),
                                      ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          
                          // Journal entry content
                          Card(
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16.0),
                              side: BorderSide(
                                color: Color(urls.journalPrimaryColor).withOpacity(0.1),
                                width: 1.0,
                              ),
                            ),
                            color: Colors.white,
                            child: Padding(
                              padding: EdgeInsets.all(20.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  _buildSectionWithIcon(
                                    'Gratitude',
                                    widget.entry.gratitude,
                                    FontAwesomeIcons.handsPraying,
                                    urls,
                                  ),
                                  Divider(height: 32.0, thickness: 1.0, color: Color(urls.journalPrimaryColor).withOpacity(0.1)),
                                  _buildSectionWithIcon(
                                    'Reflection',
                                    widget.entry.reflection,
                                    FontAwesomeIcons.lightbulb,
                                    urls,
                                  ),
                                  Divider(height: 32.0, thickness: 1.0, color: Color(urls.journalPrimaryColor).withOpacity(0.1)),
                                  _buildSectionWithIcon(
                                    'Prayer',
                                    widget.entry.prayer,
                                    FontAwesomeIcons.solidHeart,
                                    urls,
                                  ),
                                  
                                  if (widget.entry.islamicReminder.isNotEmpty) ...[
                                    Divider(height: 32.0, thickness: 1.0, color: Color(urls.journalPrimaryColor).withOpacity(0.1)),
                                    _buildReminderSection(urls),
                                  ],
                                  
                                  if (widget.entry.tags.isNotEmpty) ...[
                                    Divider(height: 32.0, thickness: 1.0, color: Color(urls.journalPrimaryColor).withOpacity(0.1)),
                                    _buildTagsSection(urls),
                                  ],
                                ],
                              ),
                            ),
                          ),
                          
                          SizedBox(height: 24.0),
                          
                          // Action buttons
                          _buildActionButtons(urls),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
    );
  }
  
  Widget _buildSectionWithIcon(String title, String content, IconData icon, URLs urls) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color: Color(urls.journalPrimaryColor).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Icon(
                icon,
                size: 16.0,
                color: Color(urls.journalPrimaryColor),
              ),
            ),
            SizedBox(width: 12.0),
            Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: 18.0,
                fontWeight: FontWeight.w600,
                color: Color(urls.journalPrimaryColor),
                letterSpacing: 0.5,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.0),
        Text(
          content,
          style: GoogleFonts.poppins(
            fontSize: 15.0,
            color: Color(urls.textPrimary),
            height: 1.6,
          ),
        ),
      ],
    );
  }
  
  Widget _buildReminderSection(URLs urls) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color: Color(urls.journalPrimaryColor).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Icon(
                FontAwesomeIcons.bookQuran,
                size: 16.0,
                color: Color(urls.journalPrimaryColor),
              ),
            ),
            SizedBox(width: 12.0),
            Text(
              'Islamic Reminder',
              style: GoogleFonts.poppins(
                fontSize: 18.0,
                fontWeight: FontWeight.w600,
                color: Color(urls.journalPrimaryColor),
                letterSpacing: 0.5,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.0),
        Container(
          padding: EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color(urls.journalSecondaryColor).withOpacity(0.1),
                Color(urls.journalSecondaryColor).withOpacity(0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12.0),
            border: Border.all(
              color: Color(urls.journalSecondaryColor).withOpacity(0.2),
              width: 1.0,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    FontAwesomeIcons.quoteLeft,
                    size: 14.0,
                    color: Color(urls.journalPrimaryColor),
                  ),
                  SizedBox(width: 8.0),
                  Text(
                    'Spiritual Guidance',
                    style: GoogleFonts.poppins(
                      fontSize: 14.0,
                      fontWeight: FontWeight.w600,
                      color: Color(urls.journalPrimaryColor),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12.0),
              Text(
                widget.entry.islamicReminder,
                style: GoogleFonts.poppins(
                  fontSize: 15.0,
                  fontStyle: FontStyle.italic,
                  color: Color(urls.textPrimary),
                  height: 1.6,
                ),
              ),
              SizedBox(height: 8.0),
              Align(
                alignment: Alignment.bottomRight,
                child: Icon(
                  FontAwesomeIcons.quoteRight,
                  size: 14.0,
                  color: Color(urls.journalPrimaryColor),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  Widget _buildTagsSection(URLs urls) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color: Color(urls.journalPrimaryColor).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Icon(
                FontAwesomeIcons.tags,
                size: 16.0,
                color: Color(urls.journalPrimaryColor),
              ),
            ),
            SizedBox(width: 12.0),
            Text(
              'Tags',
              style: GoogleFonts.poppins(
                fontSize: 18.0,
                fontWeight: FontWeight.w600,
                color: Color(urls.journalPrimaryColor),
                letterSpacing: 0.5,
              ),
            ),
          ],
        ),
        SizedBox(height: 12.0),
        Wrap(
          spacing: 8.0,
          runSpacing: 8.0,
          children: widget.entry.tags.map((tag) {
            return Container(
              padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(urls.journalPrimaryColor).withOpacity(0.1),
                    Color(urls.journalSecondaryColor).withOpacity(0.1),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20.0),
                border: Border.all(
                  color: Color(urls.journalPrimaryColor).withOpacity(0.2),
                  width: 1.0,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    FontAwesomeIcons.tag,
                    size: 10.0,
                    color: Color(urls.journalPrimaryColor),
                  ),
                  SizedBox(width: 6.0),
                  Text(
                    tag,
                    style: GoogleFonts.poppins(
                      fontSize: 12.0,
                      fontWeight: FontWeight.w500,
                      color: Color(urls.journalPrimaryColor),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
  
  Widget _buildActionButtons(URLs urls) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
        side: BorderSide(
          color: Color(urls.journalPrimaryColor).withOpacity(0.1),
          width: 1.0,
        ),
      ),
      color: Colors.white,
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.0),
                  decoration: BoxDecoration(
                    color: Color(urls.journalPrimaryColor).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Icon(
                    FontAwesomeIcons.circleInfo,
                    size: 16.0,
                    color: Color(urls.journalPrimaryColor),
                  ),
                ),
                SizedBox(width: 12.0),
                Text(
                  'Journal Entry Actions',
                  style: GoogleFonts.poppins(
                    fontSize: 18.0,
                    fontWeight: FontWeight.w600,
                    color: Color(urls.journalPrimaryColor),
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.0),
            _buildActionButton(
              icon: FontAwesomeIcons.penToSquare,
              label: 'Edit Entry',
              onTap: _navigateToEdit,
              color: Color(urls.journalPrimaryColor),
            ),
            Divider(height: 16.0, color: Color(urls.journalPrimaryColor).withOpacity(0.1)),
            _buildActionButton(
              icon: FontAwesomeIcons.shareNodes,
              label: 'Share Entry',
              onTap: _shareEntry,
              color: Color(urls.journalSecondaryColor),
            ),
            Divider(height: 16.0, color: Color(urls.journalPrimaryColor).withOpacity(0.1)),
            _buildActionButton(
              icon: FontAwesomeIcons.trash,
              label: 'Delete Entry',
              onTap: _confirmDelete,
              color: Colors.red,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required Color color,
  }) {
    final urls = URLs();
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8.0),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 12.0),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(10.0),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10.0),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 18.0,
                ),
              ),
              SizedBox(width: 16.0),
              Text(
                label,
                style: GoogleFonts.poppins(
                  fontSize: 15.0,
                  fontWeight: FontWeight.w500,
                  color: Color(urls.textPrimary),
                ),
              ),
              Spacer(),
              Icon(
                FontAwesomeIcons.angleRight,
                color: Color(urls.textSecondary).withOpacity(0.5),
                size: 16.0,
              ),
            ],
          ),
        ),
      ),
    );
  }
} 