import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../URLs.dart';
import '../controllers/journal_controller.dart';
import '../models/journal_entry.dart';
import '../widgets/mood_selector.dart';
import '../widgets/tag_input.dart';

class JournalEntryScreen extends StatefulWidget {
  @override
  _JournalEntryScreenState createState() => _JournalEntryScreenState();
}

class _JournalEntryScreenState extends State<JournalEntryScreen> {
  final _formKey = GlobalKey<FormState>();
  
  MoodLevel _selectedMood = MoodLevel.neutral;
  final TextEditingController _gratitudeController = TextEditingController();
  final TextEditingController _reflectionController = TextEditingController();
  final TextEditingController _prayerController = TextEditingController();
  List<String> _selectedTags = [];
  
  bool _isSaving = false;
  
  @override
  void dispose() {
    _gratitudeController.dispose();
    _reflectionController.dispose();
    _prayerController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'New Journal Entry',
          style: GoogleFonts.poppins(
            fontSize: 20.0,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Color(urls.journalPrimaryColor),
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _isSaving ? null : _saveEntry,
            child: Text(
              'Save',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 16.0,
              ),
            ),
          ),
        ],
      ),
      body: _isSaving
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Color(urls.journalPrimaryColor),
                    ),
                  ),
                  SizedBox(height: 16.0),
                  Text(
                    'Saving your journal entry...',
                    style: GoogleFonts.poppins(
                      fontSize: 16.0,
                      color: Color(urls.textSecondary),
                    ),
                  ),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Date display
                    Text(
                      'Today, ${DateTime.now().toString().substring(0, 10)}',
                      style: GoogleFonts.poppins(
                        fontSize: 16.0,
                        color: Color(urls.textSecondary),
                      ),
                    ),
                    SizedBox(height: 24.0),
                    
                    // Mood selector
                    MoodSelector(
                      selectedMood: _selectedMood,
                      onMoodSelected: (mood) {
                        setState(() {
                          _selectedMood = mood;
                        });
                      },
                    ),
                    SizedBox(height: 24.0),
                    
                    // Gratitude field
                    _buildTextField(
                      controller: _gratitudeController,
                      label: 'What are you grateful for today?',
                      hint: 'Express your gratitude to Allah...',
                      maxLines: 3,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter something you are grateful for';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 24.0),
                    
                    // Reflection field
                    _buildTextField(
                      controller: _reflectionController,
                      label: 'Spiritual Reflection',
                      hint: 'Reflect on your spiritual journey today...',
                      maxLines: 5,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a reflection';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 24.0),
                    
                    // Prayer field
                    _buildTextField(
                      controller: _prayerController,
                      label: 'Prayer / Dua',
                      hint: 'Write a prayer or dua for yourself or others...',
                      maxLines: 3,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a prayer or dua';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 24.0),
                    
                    // Tags input
                    TagInput(
                      tags: _selectedTags,
                      onTagsChanged: (tags) {
                        setState(() {
                          _selectedTags = tags;
                        });
                      },
                      suggestedTags: [
                        'gratitude',
                        'prayer',
                        'reflection',
                        'quran',
                        'dhikr',
                        'salah',
                        'fasting',
                        'charity',
                        'patience',
                        'forgiveness',
                        'family',
                        'community',
                        'knowledge',
                        'growth',
                        'challenge',
                      ],
                    ),
                    SizedBox(height: 32.0),
                    
                    // Save button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _saveEntry,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(urls.journalAccentColor),
                          padding: EdgeInsets.symmetric(vertical: 16.0),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                          ),
                        ),
                        child: Text(
                          'Save Journal Entry',
                          style: GoogleFonts.poppins(
                            fontSize: 16.0,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
  
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required int maxLines,
    required String? Function(String?) validator,
  }) {
    final urls = URLs();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 16.0,
            fontWeight: FontWeight.w600,
            color: Color(urls.journalPrimaryColor),
          ),
        ),
        SizedBox(height: 8.0),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: GoogleFonts.poppins(
              color: Colors.grey,
              fontSize: 14.0,
            ),
            filled: true,
            fillColor: Colors.grey.withOpacity(0.1),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
              borderSide: BorderSide.none,
            ),
            contentPadding: EdgeInsets.all(16.0),
          ),
          style: GoogleFonts.poppins(
            fontSize: 14.0,
            color: Color(urls.textPrimary),
          ),
          maxLines: maxLines,
          validator: validator,
        ),
      ],
    );
  }
  
  void _saveEntry() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSaving = true;
      });
      
      try {
        final controller = Provider.of<JournalController>(context, listen: false);
        
        await controller.addEntry(
          moodLevel: _selectedMood,
          gratitude: _gratitudeController.text,
          reflection: _reflectionController.text,
          prayer: _prayerController.text,
          tags: _selectedTags,
        );
        
        Navigator.pop(context);
      } catch (e) {
        setState(() {
          _isSaving = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save journal entry: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
} 