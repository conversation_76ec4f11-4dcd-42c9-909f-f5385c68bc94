import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../URLs.dart';
import '../controllers/journal_controller.dart';
import '../models/journal_entry.dart';
import '../widgets/journal_app_bar.dart';
import '../widgets/journal_fab.dart';
import '../widgets/journal_tab_views.dart';
import '../widgets/journal_filter_options.dart';
import 'journal_entry_screen.dart';
import 'journal_detail_screen.dart';
import '../../../widgets/common/loading_state.dart';
import '../../../widgets/common/error_state.dart';
import 'journal_edit_screen.dart';

class JournalListScreen extends StatefulWidget {
  const JournalListScreen({Key? key}) : super(key: key);

  @override
  _JournalListScreenState createState() => _JournalListScreenState();
}

class _JournalListScreenState extends State<JournalListScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  MoodLevel? _selectedMoodFilter;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  bool _showSearch = false;
  final FocusNode _searchFocusNode = FocusNode();
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Load entries when screen is first opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<JournalController>(context, listen: false).loadEntries();
    });
    
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }
  
  void _toggleSearch() {
    setState(() {
      _showSearch = !_showSearch;
      if (!_showSearch) {
        _searchController.clear();
      } else {
        _searchFocusNode.requestFocus();
      }
    });
  }
  
  void _showFilterOptions() {
    JournalFilterOptions.showFilterOptions(context, _tabController);
  }
  
  void _navigateToEntryDetail(BuildContext context, JournalEntry entry) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => JournalDetailScreen(entry: entry),
      ),
    );
  }
  
  void _toggleFavorite(JournalEntry entry) {
    Provider.of<JournalController>(context, listen: false).toggleFavorite(entry.id);
  }
  
  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    final controller = Provider.of<JournalController>(context);
    
    return Scaffold(
      backgroundColor: Color(urls.journalBackgroundColor),
      appBar: JournalAppBar(
        tabController: _tabController,
        showSearch: _showSearch,
        searchController: _searchController,
        onSearchToggle: _toggleSearch,
        onFilterTap: _showFilterOptions,
        onSearchChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
      body: SafeArea(
        child: Consumer<JournalController>(
          builder: (context, controller, child) {
            if (controller.isLoading) {
              return LoadingState(message: 'Loading your journal entries...');
            }
            
            if (controller.errorMessage.isNotEmpty) {
              return ErrorState(
                message: controller.errorMessage,
                onRetry: () => controller.loadEntries(),
              );
            }
            
            // Show filter indicator if filters are applied
            if (controller.isFiltered) {
              return Column(
                children: [
                  _buildFilterIndicator(controller),
                  Expanded(
                    child: _buildTabBarView(controller),
                  ),
                ],
              );
            }
            
            return _buildTabBarView(controller);
          },
        ),
      ),
      floatingActionButton: JournalFAB(),
    );
  }
  
  Widget _buildFilterIndicator(JournalController controller) {
    final urls = URLs();
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      color: Color(urls.journalPrimaryColor).withOpacity(0.1),
      child: Row(
        children: [
          Icon(
            Icons.filter_list,
            size: 16.0,
            color: Color(urls.journalPrimaryColor),
          ),
          SizedBox(width: 8.0),
          Expanded(
            child: Text(
              'Filtered: ${controller.filterDescription}',
              style: TextStyle(
                color: Color(urls.journalPrimaryColor),
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          TextButton.icon(
            onPressed: () => controller.clearFilters(),
            icon: Icon(
              Icons.clear,
              size: 16.0,
              color: Color(urls.journalPrimaryColor),
            ),
            label: Text(
              'Clear',
              style: TextStyle(
                color: Color(urls.journalPrimaryColor),
                fontWeight: FontWeight.w500,
              ),
            ),
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 8.0),
              minimumSize: Size(0, 0),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildTabBarView(JournalController controller) {
    final urls = URLs();
    
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(urls.journalBackgroundColor),
            Colors.white,
          ],
          stops: [0.0, 0.3],
        ),
      ),
      child: TabBarView(
        controller: _tabController,
        children: [
          JournalTabViews.buildJournalList(
            controller.filteredEntries, 
            _searchQuery,
            (entry) => _navigateToEntryDetail(context, entry),
            (entry) => _toggleFavorite(entry),
          ),
          JournalTabViews.buildJournalList(
            controller.filteredEntries.where((e) => e.isFavorite).toList(), 
            _searchQuery,
            (entry) => _navigateToEntryDetail(context, entry),
            (entry) => _toggleFavorite(entry),
          ),
          JournalTabViews.buildMoodCategorizedList(
            controller.filteredEntries, 
            _searchQuery,
            (entry) => _navigateToEntryDetail(context, entry),
            (entry) => _toggleFavorite(entry),
          ),
        ],
      ),
    );
  }
} 