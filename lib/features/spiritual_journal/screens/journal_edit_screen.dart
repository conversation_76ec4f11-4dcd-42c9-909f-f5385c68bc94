import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../URLs.dart';
import '../controllers/journal_controller.dart';
import '../models/journal_entry.dart';
import '../widgets/mood_selector.dart';
import '../widgets/tag_input.dart';

class JournalEditScreen extends StatefulWidget {
  final JournalEntry? entry;
  
  const JournalEditScreen({
    Key? key,
    this.entry,
  }) : super(key: key);
  
  @override
  _JournalEditScreenState createState() => _JournalEditScreenState();
}

class _JournalEditScreenState extends State<JournalEditScreen> with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  
  late TextEditingController _gratitudeController;
  late TextEditingController _reflectionController;
  late TextEditingController _prayerController;
  late TextEditingController _islamicReminderController;
  
  late MoodLevel _selectedMood;
  List<String> _tags = [];
  bool _isSaving = false;
  bool _isEditing = false;
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  @override
  void initState() {
    super.initState();
    _isEditing = widget.entry != null;
    
    // Initialize controllers with existing data if editing
    _gratitudeController = TextEditingController(
      text: _isEditing ? widget.entry!.gratitude : '',
    );
    _reflectionController = TextEditingController(
      text: _isEditing ? widget.entry!.reflection : '',
    );
    _prayerController = TextEditingController(
      text: _isEditing ? widget.entry!.prayer : '',
    );
    _islamicReminderController = TextEditingController(
      text: _isEditing ? widget.entry!.islamicReminder : '',
    );
    
    if (_isEditing) {
      _selectedMood = widget.entry!.moodLevel;
      _tags = List.from(widget.entry!.tags);
    } else {
      _selectedMood = MoodLevel.neutral;
    }
    
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 600),
    );
    
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    _animationController.forward();
  }
  
  @override
  void dispose() {
    _gratitudeController.dispose();
    _reflectionController.dispose();
    _prayerController.dispose();
    _islamicReminderController.dispose();
    _animationController.dispose();
    super.dispose();
  }
  
  void _saveEntry() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSaving = true;
      });
      
      final journalController = Provider.of<JournalController>(context, listen: false);
      
      if (_isEditing) {
        final updatedEntry = widget.entry!.copyWith(
          moodLevel: _selectedMood,
          gratitude: _gratitudeController.text.trim(),
          reflection: _reflectionController.text.trim(),
          prayer: _prayerController.text.trim(),
          islamicReminder: _islamicReminderController.text.trim(),
          tags: _tags,
        );
        
        await journalController.updateEntry(updatedEntry);
      } else {
        await journalController.addEntry(
          moodLevel: _selectedMood,
          gratitude: _gratitudeController.text.trim(),
          reflection: _reflectionController.text.trim(),
          prayer: _prayerController.text.trim(),
          tags: _tags,
        );
      }
      
      setState(() {
        _isSaving = false;
      });
      
      if (mounted) {
        Navigator.pop(context);
        
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(FontAwesomeIcons.circleCheck, color: Colors.white, size: 16),
                SizedBox(width: 12),
                Text(
                  _isEditing ? 'Journal entry updated' : 'Journal entry saved',
                  style: GoogleFonts.poppins(),
                ),
              ],
            ),
            backgroundColor: Color(URLs().successColor),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            ),
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () {},
            ),
          ),
        );
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Scaffold(
      backgroundColor: Color(urls.journalBackgroundColor),
      appBar: AppBar(
        backgroundColor: Color(urls.journalPrimaryColor),
        elevation: 0,
        title: Row(
          children: [
            Icon(
              _isEditing ? FontAwesomeIcons.penToSquare : FontAwesomeIcons.plus,
              size: 18,
              color: Colors.white,
            ),
            SizedBox(width: 12),
            Text(
              _isEditing ? 'Edit Journal Entry' : 'New Journal Entry',
              style: GoogleFonts.poppins(
                fontSize: 20.0,
                fontWeight: FontWeight.w600,
                color: Colors.white,
                letterSpacing: 0.5,
              ),
            ),
          ],
        ),
        actions: [
          if (_isSaving)
            Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20.0,
                height: 20.0,
                child: CircularProgressIndicator(
                  strokeWidth: 2.0,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            IconButton(
              icon: Icon(FontAwesomeIcons.check, size: 18),
              onPressed: _saveEntry,
              tooltip: 'Save',
            ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(urls.journalBackgroundColor),
              Colors.white,
            ],
            stops: [0.0, 0.3],
          ),
        ),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Mood Selector
                  MoodSelector(
                    selectedMood: _selectedMood,
                    onMoodSelected: (mood) {
                      setState(() {
                        _selectedMood = mood;
                      });
                    },
                  ),
                  SizedBox(height: 24.0),
                  
                  // Gratitude
                  _buildInputCard(
                    title: 'What are you grateful for today?',
                    icon: FontAwesomeIcons.handsPraying,
                    child: _buildTextField(
                      controller: _gratitudeController,
                      hintText: 'Express your gratitude...',
                      maxLines: 4,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please share something you are grateful for';
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(height: 24.0),
                  
                  // Reflection
                  _buildInputCard(
                    title: 'Reflect on your spiritual journey',
                    icon: FontAwesomeIcons.lightbulb,
                    child: _buildTextField(
                      controller: _reflectionController,
                      hintText: 'Share your thoughts and reflections...',
                      maxLines: 5,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please share your reflections';
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(height: 24.0),
                  
                  // Prayer
                  _buildInputCard(
                    title: 'Prayer intentions',
                    icon: FontAwesomeIcons.solidHeart,
                    child: _buildTextField(
                      controller: _prayerController,
                      hintText: 'What are you praying for today?',
                      maxLines: 4,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please share your prayer intentions';
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(height: 24.0),
                  
                  // Islamic Reminder
                  _buildInputCard(
                    title: 'Islamic reminder or quote (optional)',
                    icon: FontAwesomeIcons.bookQuran,
                    child: _buildTextField(
                      controller: _islamicReminderController,
                      hintText: 'Add a verse, hadith, or spiritual quote...',
                      maxLines: 3,
                      validator: null, // Optional field
                    ),
                  ),
                  SizedBox(height: 24.0),
                  
                  // Tags
                  _buildInputCard(
                    title: 'Tags',
                    icon: FontAwesomeIcons.tags,
                    child: TagInput(
                      tags: _tags,
                      onTagsChanged: (tags) {
                        setState(() {
                          _tags = tags;
                        });
                      },
                    ),
                  ),
                  SizedBox(height: 32.0),
                  
                  // Save Button
                  SizedBox(
                    width: double.infinity,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(urls.journalPrimaryColor),
                            Color(urls.journalSecondaryColor),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12.0),
                        boxShadow: [
                          BoxShadow(
                            color: Color(urls.journalPrimaryColor).withOpacity(0.3),
                            blurRadius: 8.0,
                            offset: Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ElevatedButton.icon(
                        onPressed: _isSaving ? null : _saveEntry,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(vertical: 16.0),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                          elevation: 0,
                          disabledBackgroundColor: Colors.transparent,
                          disabledForegroundColor: Colors.white.withOpacity(0.7),
                        ),
                        icon: Icon(
                          _isEditing ? FontAwesomeIcons.floppyDisk : FontAwesomeIcons.plus,
                          size: 16.0,
                        ),
                        label: Text(
                          _isEditing ? 'Update Entry' : 'Save Entry',
                          style: GoogleFonts.poppins(
                            fontSize: 16.0,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildInputCard({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    final urls = URLs();
    
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: Color(urls.journalPrimaryColor).withOpacity(0.1),
            blurRadius: 10.0,
            offset: Offset(0, 4),
          ),
        ],
      ),
      padding: EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: Color(urls.journalPrimaryColor).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Icon(
                  icon,
                  size: 16.0,
                  color: Color(urls.journalPrimaryColor),
                ),
              ),
              SizedBox(width: 12.0),
              Flexible(
                child: Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 16.0,
                    fontWeight: FontWeight.w600,
                    color: Color(urls.journalPrimaryColor),
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.0),
          child,
        ],
      ),
    );
  }
  
  Widget _buildTextField({
    required TextEditingController controller,
    required String hintText,
    required int maxLines,
    required FormFieldValidator<String>? validator,
  }) {
    final urls = URLs();
    
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: GoogleFonts.poppins(
          color: Color(urls.textSecondary).withOpacity(0.6),
          fontSize: 14.0,
        ),
        filled: true,
        fillColor: Color(urls.journalBackgroundColor).withOpacity(0.5),
        contentPadding: EdgeInsets.all(16.0),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide(
            color: Color(urls.journalPrimaryColor),
            width: 1.0,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.0),
          borderSide: BorderSide(
            color: Colors.red,
            width: 1.0,
          ),
        ),
      ),
      style: GoogleFonts.poppins(
        fontSize: 14.0,
        color: Color(urls.textPrimary),
        height: 1.5,
      ),
      validator: validator,
    );
  }
} 