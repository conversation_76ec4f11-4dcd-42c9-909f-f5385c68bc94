import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/hadith/models/hadith_models.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_reader_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/services/hadith_service.dart';
import 'package:new_islamic_app_uk/loading_indicator.dart';
import 'dart:math' as Math;

class HadithSearchScreen extends StatefulWidget {
  final String? initialQuery;
  
  const HadithSearchScreen({
    Key? key,
    this.initialQuery,
  }) : super(key: key);

  @override
  State<HadithSearchScreen> createState() => _HadithSearchScreenState();
}

class _HadithSearchScreenState extends State<HadithSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  final HadithService _hadithService = HadithService();
  final Color primaryColor = Color(URLs().colorScheme);
  
  List<Hadith>? _searchResults;
  bool _isLoading = false;
  String? _errorMessage;
  
  // Selected collection for filtering (null means all collections)
  String? _selectedCollection;
  
  // Collections for filtering
  final Map<String, String> _collections = {
    'bukhari': 'Sahih al-Bukhari',
    'muslim': 'Sahih Muslim',
    'abudawud': 'Sunan Abu Dawud',
    'tirmidhi': 'Jami at-Tirmidhi',
    'nasai': 'Sunan an-Nasa\'i',
    'ibnmajah': 'Sunan Ibn Majah',
    'malik': 'Muwatta Malik',
    'riyadussalihin': 'Riyad as-Salihin',
    'nawawi40': 'An-Nawawi\'s 40 Hadith',
  };

  @override
  void initState() {
    super.initState();
    
    // If an initial query was provided, set it and perform search
    if (widget.initialQuery != null && widget.initialQuery!.isNotEmpty) {
      _searchController.text = widget.initialQuery!;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _performSearch();
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  Future<void> _performSearch() async {
    final query = _searchController.text.trim();
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
        _errorMessage = 'Please enter a search term';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Show a longer loading indicator for search operations that might take time
      await Future.delayed(Duration(milliseconds: 300));
      
      // Use the actual search method from HadithService
      final results = await _hadithService.searchHadiths(
        query,
        collectionName: _selectedCollection,
        limit: 20, // Reasonable limit for mobile UI
      );
      
      if (mounted) {
        setState(() {
          _searchResults = results;
          _isLoading = false;
          
          if (results.isEmpty) {
            _errorMessage = 'No hadiths found matching "$query"';
            if (_selectedCollection != null) {
              _errorMessage = '$_errorMessage in ${_collections[_selectedCollection]}';
            }
          }
        });
      }
    } catch (e) {
      print('Search error: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Error searching hadiths';
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to search hadiths. Please try again.'),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _performSearch,
            ),
          ),
        );
      }
    }
  }

  void _navigateToHadith(Hadith hadith) {
    // Parse the ID parts (now in format "collection_book_number")
    final idParts = hadith.id.split('_');
    final String collectionId;
    final int hadithNumber;
    
    if (idParts.length >= 3) {
      // New format: "collection_book_number"
      collectionId = idParts[0];
      hadithNumber = hadith.number;
    } else if (idParts.length == 2) {
      // Legacy format: "collection_number"
      collectionId = idParts[0];
      hadithNumber = int.tryParse(idParts[1]) ?? hadith.number;
    } else {
      // Unknown format, use defaults
      collectionId = 'bukhari';
      hadithNumber = 1;
    }
    
    // Find the collection
    final collections = _hadithService.getAllCollections();
    final HadithCollection collection;
    
    try {
      collection = collections.firstWhere(
        (c) => c.name == collectionId,
        orElse: () => throw Exception('Collection not found'),
      );
    } catch (e) {
      print('Error finding collection: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Could not find the hadith collection'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }
    
    // Navigate to the reader
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HadithReaderScreen(
          collection: collection,
          initialHadithNumber: hadithNumber,
        ),
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Filter by Collection',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 16),
                  Wrap(
                    spacing: 8,
                    runSpacing: 12,
                    children: [
                      _buildFilterChip(
                        label: 'All Collections',
                        isSelected: _selectedCollection == null,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() => _selectedCollection = null);
                            Navigator.pop(context);
                            _performSearch();
                          }
                        },
                      ),
                      ..._collections.entries.map((entry) {
                        return _buildFilterChip(
                          label: entry.value,
                          isSelected: _selectedCollection == entry.key,
                          onSelected: (selected) {
                            if (selected) {
                              setState(() => _selectedCollection = entry.key);
                              Navigator.pop(context);
                              _performSearch();
                            }
                          },
                        );
                      }).toList(),
                    ],
                  ),
                  SizedBox(height: 16),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    required Function(bool) onSelected,
  }) {
    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.poppins(
          fontSize: 13,
          color: isSelected ? Colors.white : Colors.grey[800],
        ),
      ),
      selected: isSelected,
      onSelected: onSelected,
      backgroundColor: Colors.grey[200],
      selectedColor: primaryColor,
      checkmarkColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30),
      ),
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: primaryColor,
        elevation: 0,
        title: Text(
          'Search Hadiths',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search box
          Container(
            color: primaryColor,
            padding: EdgeInsets.only(left: 16, right: 16, bottom: 16),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextField(
                      controller: _searchController,
                      focusNode: _searchFocusNode,
                      decoration: InputDecoration(
                        hintText: 'Search hadiths...',
                        hintStyle: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.grey[400],
                        ),
                        prefixIcon: Icon(
                          FontAwesomeIcons.magnifyingGlass,
                          size: 16,
                          color: Colors.grey[400],
                        ),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: Icon(
                                  Icons.close,
                                  size: 20,
                                  color: Colors.grey[500],
                                ),
                                onPressed: () {
                                  _searchController.clear();
                                  setState(() {
                                    _searchResults = null;
                                    _errorMessage = null;
                                  });
                                },
                              )
                            : null,
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                      ),
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                      ),
                      onSubmitted: (_) => _performSearch(),
                    ),
                  ),
                ),
                SizedBox(width: 12),
                Material(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(30),
                  elevation: 2,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(30),
                    onTap: _performSearch,
                    child: Container(
                      padding: EdgeInsets.all(12),
                      child: Icon(
                        Icons.search,
                        color: primaryColor,
                        size: 24,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Filter bar
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Text(
                  'Filter:',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: InkWell(
                    onTap: _showFilterBottomSheet,
                    borderRadius: BorderRadius.circular(30),
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: _selectedCollection != null
                            ? primaryColor.withOpacity(0.1)
                            : Colors.grey[200],
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            FontAwesomeIcons.bookOpen,
                            size: 14,
                            color: _selectedCollection != null
                                ? primaryColor
                                : Colors.grey[700],
                          ),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _selectedCollection != null
                                  ? _collections[_selectedCollection] ?? 'Unknown Collection'
                                  : 'All Collections',
                              style: GoogleFonts.poppins(
                                fontSize: 13,
                                color: _selectedCollection != null
                                    ? primaryColor
                                    : Colors.grey[700],
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Icon(
                            Icons.arrow_drop_down,
                            color: _selectedCollection != null
                                ? primaryColor
                                : Colors.grey[700],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Search results or empty state
          Expanded(
            child: _isLoading
                ? Center(child: LoadingIndicator())
                : _searchResults == null
                    ? _buildInitialState()
                    : _searchResults!.isEmpty
                        ? _buildEmptyResults()
                        : _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FontAwesomeIcons.magnifyingGlass,
              size: 64,
              color: Colors.grey[300],
            ),
            SizedBox(height: 24),
            Text(
              'Search for Hadiths',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            Text(
              'Enter keywords to find hadiths. You can search by topics, narrator names, or specific words.',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FontAwesomeIcons.faceFrown,
            size: 64,
            color: Colors.grey[300],
          ),
          SizedBox(height: 24),
          Text(
            'No Results Found',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              _errorMessage ?? 'Try different keywords or change your filters',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: 24),
          if (_selectedCollection != null)
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _selectedCollection = null;
                });
                _performSearch();
              },
              icon: Icon(FontAwesomeIcons.filter, size: 16),
              label: Text('Clear Filter'),
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: _searchResults!.length,
      itemBuilder: (context, index) {
        final hadith = _searchResults![index];
        
        // Get highlighted match section
        final query = _searchController.text.trim().toLowerCase();
        final englishText = hadith.english.toLowerCase();
        int matchStart = englishText.indexOf(query);
        
        // If no direct match found, use the first part of the text
        if (matchStart == -1 || query.length < 3) {
          matchStart = 0;
        }
        
        // Get a reasonable highlight section with context before and after match
        final contextSize = 40;
        final startWithContext = Math.max(0, matchStart - contextSize);
        final endWithContext = Math.min(
          englishText.length, 
          matchStart + query.length + contextSize
        );
        
        final previewText = hadith.english.substring(startWithContext, endWithContext);
        
        // Add ellipses if we're not showing from the beginning or to the end
        final prefix = startWithContext > 0 ? '...' : '';
        final suffix = endWithContext < hadith.english.length ? '...' : '';
        
        final displayText = '$prefix$previewText$suffix';
        
        return Card(
          margin: EdgeInsets.only(bottom: 16),
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: Colors.grey.shade200),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () => _navigateToHadith(hadith),
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Collection and reference
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                        decoration: BoxDecoration(
                          color: primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getCollectionDisplayName(hadith.id.split('_')[0]),
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: primaryColor,
                          ),
                        ),
                      ),
                      SizedBox(width: 8),
                      Text(
                        '• ${hadith.reference}',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  
                  // Arabic text preview
                  if (hadith.arabic.isNotEmpty)
                    Text(
                      hadith.arabic,
                      style: GoogleFonts.amiri(
                        fontSize: 18,
                        height: 1.5,
                      ),
                      textDirection: TextDirection.rtl,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  SizedBox(height: 16),
                  
                  // English text preview
                  Text(
                    displayText,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      height: 1.5,
                      color: Colors.grey[800],
                    ),
                  ),
                  
                  SizedBox(height: 12),
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton.icon(
                      onPressed: () => _navigateToHadith(hadith),
                      icon: Text(
                        'Read Full',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: primaryColor,
                        ),
                      ),
                      label: Icon(
                        FontAwesomeIcons.angleRight,
                        size: 14,
                        color: primaryColor,
                      ),
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
  
  // Helper method to get a nicer display name for collections
  String _getCollectionDisplayName(String collectionId) {
    switch (collectionId) {
      case 'bukhari':
        return 'Sahih al-Bukhari';
      case 'muslim':
        return 'Sahih Muslim';
      case 'nasai':
        return 'Sunan an-Nasa\'i';
      case 'abudawud':
        return 'Sunan Abu Dawud';
      case 'tirmidhi':
        return 'Jami at-Tirmidhi';
      case 'ibnmajah':
        return 'Sunan Ibn Majah';
      case 'malik':
        return 'Muwatta Malik';
      case 'riyadussalihin':
        return 'Riyad as-Salihin';
      default:
        // Format collection ID for display by capitalizing
        if (collectionId.isEmpty) return 'Unknown';
        return collectionId.substring(0, 1).toUpperCase() + 
               collectionId.substring(1);
    }
  }
} 