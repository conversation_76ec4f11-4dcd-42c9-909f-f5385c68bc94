import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/hadith/models/hadith_models.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_collection_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_search_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/favorite_hadiths_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/services/hadith_service.dart';
import 'package:new_islamic_app_uk/features/hadith/widgets/daily_hadith_card.dart';
import 'package:new_islamic_app_uk/features/hadith/widgets/hadith_collection_card.dart';
import 'package:new_islamic_app_uk/features/hadith/widgets/quick_action_button.dart';
import 'package:new_islamic_app_uk/features/hadith/widgets/section_header.dart';
import 'package:new_islamic_app_uk/loading_indicator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:share_plus/share_plus.dart';
import 'package:new_islamic_app_uk/theme/app_theme.dart';
import 'package:new_islamic_app_uk/theme/app_colors.dart';
import 'package:new_islamic_app_uk/theme/app_text_styles.dart';
import 'package:new_islamic_app_uk/widgets/common/modern_app_bar.dart';
import 'package:new_islamic_app_uk/widgets/common/modern_search_bar.dart';

class HadithHomeScreen extends StatefulWidget {
  const HadithHomeScreen({Key? key}) : super(key: key);

  @override
  State<HadithHomeScreen> createState() => _HadithHomeScreenState();
}

class _HadithHomeScreenState extends State<HadithHomeScreen> {
  final HadithService _hadithService = HadithService();
  final TextEditingController _searchController = TextEditingController();
  final Color primaryColor = Color(URLs().colorScheme);

  bool _isLoading = true;
  bool _hasError = false;
  late List<HadithCollection> _collections;
  DailyHadith? _dailyHadith;
  String _searchQuery = '';
  HadithReadingProgress? _lastReadProgress;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      // Load collections
      _collections = _hadithService.getAllCollections();

      // Load daily hadith
      _dailyHadith = await _hadithService.getDailyHadith();

      // Load last read information
      await _loadLastReadProgress();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading hadith data: $e');
      setState(() {
        _isLoading = false;
        _hasError = true;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load hadith data'),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _loadData,
            ),
          ),
        );
      }
    }
  }

  Future<void> _loadLastReadProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastReadStr = prefs.getString('last_read_hadith');

      if (lastReadStr != null) {
        try {
          // Directly decode the JSON string to a Map
          final Map<String, dynamic> lastReadMap = jsonDecode(lastReadStr);
          _lastReadProgress = HadithReadingProgress.fromJson(lastReadMap);
        } catch (e) {
          print('Error parsing last read data: $e');
          // Create a default reading progress if parse fails
          _lastReadProgress = HadithReadingProgress(
            collectionName: 'bukhari',
            lastReadHadith: 1,
            lastReadTime: DateTime.now(),
          );
        }
      }
    } catch (e) {
      print('Error loading last read progress: $e');
      // Default to null if there's an error
      _lastReadProgress = null;
    }
  }

  void _navigateToCollection(HadithCollection collection) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HadithCollectionScreen(collection: collection),
      ),
    );
  }

  void _navigateToSearch() {
    if (_searchQuery.trim().isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => HadithSearchScreen(
            initialQuery: _searchQuery,
          ),
        ),
      );
    } else {
      // Open search with empty query
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const HadithSearchScreen(),
        ),
      );
    }
  }

  void _navigateToFavorites() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FavoriteHadithsScreen(),
      ),
    );
  }

  // Show random hadith dialog
  Future<void> _showRandomHadith() async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(color: primaryColor),
              SizedBox(height: 20),
              Text(
                'Fetching a random hadith...',
                style: GoogleFonts.poppins(fontSize: 16),
              ),
            ],
          ),
        ),
      ),
    );

    try {
      // Get a random hadith
      final dailyHadith = await _hadithService.getDailyHadith();

      // Close loading dialog
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      // Check if the random hadith is a fallback hadith
      final isFallback = dailyHadith.hadith.id == 'fallback';

      // Show the hadith dialog
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => Dialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Container(
                  padding: EdgeInsets.symmetric(vertical: 20, horizontal: 24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        primaryColor,
                        primaryColor.withOpacity(0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        FontAwesomeIcons.random,
                        color: Colors.white,
                        size: 20,
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Random Hadith',
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      if (isFallback)
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.amber,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'Fallback',
                            style: GoogleFonts.poppins(
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                              color: Colors.black,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // Content
                Container(
                  padding: EdgeInsets.all(24),
                  constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * 0.6),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Collection badge
                        if (!isFallback)
                          Container(
                            margin: EdgeInsets.only(bottom: 16),
                            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(30),
                            ),
                            child: Text(
                              dailyHadith.collectionName.toUpperCase(),
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: primaryColor,
                              ),
                            ),
                          ),

                        // Arabic text
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            dailyHadith.hadith.arabic,
                            style: GoogleFonts.amiri(
                              fontSize: 20,
                              height: 1.5,
                            ),
                            textAlign: TextAlign.right,
                            textDirection: TextDirection.rtl,
                          ),
                        ),
                        SizedBox(height: 20),

                        // English translation
                        Text(
                          dailyHadith.hadith.english,
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            height: 1.6,
                            color: Colors.grey[800],
                          ),
                        ),
                        SizedBox(height: 16),

                        // Reference
                        if (dailyHadith.hadith.reference.isNotEmpty)
                          Text(
                            dailyHadith.hadith.reference,
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontStyle: FontStyle.italic,
                              color: Colors.grey[600],
                            ),
                          ),

                        // Narrator
                        if (dailyHadith.hadith.narrator.isNotEmpty) ...[
                          SizedBox(height: 4),
                          Text(
                            'Narrator: ${dailyHadith.hadith.narrator}',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],

                        // Warning message if this is a fallback hadith
                        if (isFallback) ...[
                          SizedBox(height: 16),
                          Container(
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.amber.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.amber),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  FontAwesomeIcons.triangleExclamation,
                                  color: Colors.amber[800],
                                  size: 16,
                                ),
                                SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    'Could not load a random hadith. Showing a fallback hadith instead.',
                                    style: GoogleFonts.poppins(
                                      fontSize: 12,
                                      color: Colors.amber[800],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                // Action buttons
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Share button
                      TextButton.icon(
                        onPressed: () => _shareHadith(dailyHadith.hadith),
                        icon: Icon(
                          FontAwesomeIcons.share,
                          size: 16,
                          color: Colors.blue[600],
                        ),
                        label: Text(
                          'Share',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.blue[600],
                          ),
                        ),
                      ),

                      // New random button
                      TextButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          _showRandomHadith();
                        },
                        icon: Icon(
                          FontAwesomeIcons.random,
                          size: 16,
                          color: primaryColor,
                        ),
                        label: Text(
                          'New Random',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: primaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      }
    } catch (e) {
      print('Error getting random hadith: $e');

      // Close loading dialog
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not fetch a random hadith. Please try again.'),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _showRandomHadith,
            ),
          ),
        );
      }
    }
  }

  // Helper method to share hadith text
  Future<void> _shareHadith(Hadith hadith) async {
    try {
      final String textToShare = '''
${hadith.arabic}

${hadith.english}

${hadith.reference}
''';

      await Share.share(textToShare);
    } catch (e) {
      print('Error sharing hadith: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not share the hadith. Please try again.'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  // Get display name for hadith collection
  String _getHadithCollectionName(String name) {
    switch (name) {
      case 'bukhari':
        return 'Sahih al-Bukhari';
      case 'muslim':
        return 'Sahih Muslim';
      case 'nasai':
        return 'Sunan an-Nasa\'i';
      case 'abudawud':
        return 'Sunan Abu Dawud';
      case 'tirmidhi':
        return 'Jami` at-Tirmidhi';
      case 'ibnmajah':
        return 'Sunan Ibn Majah';
      case 'malik':
        return 'Muwatta Imam Malik';
      case 'riyadussalihin':
        return 'Riyad as-Salihin';
      default:
        return name.substring(0, 1).toUpperCase() + name.substring(1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: ModernAppBar(
        title: 'Hadith',
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(
              FontAwesomeIcons.magnifyingGlass,
              size: AppTheme.iconMedium,
              color: AppColors.primary,
            ),
            onPressed: _navigateToSearch,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadData,
        color: AppColors.primary,
        child: _isLoading
            ? const Center(child: LoadingIndicator())
            : _hasError
                ? _buildErrorView()
                : _buildModernContent(),
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FontAwesomeIcons.circleExclamation,
              size: 64,
              color: Colors.grey[400],
            ),
            SizedBox(height: 24),
            Text(
              'Failed to Load Data',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            Text(
              'We couldn\'t load the hadith data. Please check your connection and try again.',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadData,
              icon: Icon(Icons.refresh),
              label: Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernContent() {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.all(AppTheme.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search Bar
          ModernSearchBar(
            hintText: 'Search hadith by keyword...',
            controller: _searchController,
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
            onClear: () {
              setState(() {
                _searchQuery = '';
              });
            },
          ),

          const SizedBox(height: AppTheme.spacing24),

          // Quick Actions Section
          _buildSectionHeader(
            'Quick Actions',
            'Access common functions quickly',
            FontAwesomeIcons.bolt,
          ),
          const SizedBox(height: AppTheme.spacing16),

          // Quick action cards in a row
          SizedBox(
            height: 110,
            child: Row(
              children: [
                Expanded(
                  child: QuickActionButton(
                    title: 'Favorites',
                    icon: FontAwesomeIcons.solidHeart,
                    color: Colors.redAccent,
                    onTap: _navigateToFavorites,
                  ),
                ),
                const SizedBox(width: AppTheme.spacing12),
                Expanded(
                  child: QuickActionButton(
                    title: 'Continue Reading',
                    icon: FontAwesomeIcons.bookOpenReader,
                    color: Colors.blue,
                    onTap: _continuePreviousReading,
                    isActive: _lastReadProgress != null,
                  ),
                ),
                const SizedBox(width: AppTheme.spacing12),
                Expanded(
                  child: QuickActionButton(
                    title: 'Random',
                    icon: FontAwesomeIcons.shuffle,
                    color: Colors.purple,
                    onTap: _showRandomHadith,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: AppTheme.spacing24),

          // Daily Hadith Card
          if (_dailyHadith != null) ...[
            _buildSectionHeader(
              'Hadith of the Day',
              'Updated daily for your reflection',
              FontAwesomeIcons.calendarDays,
              action: _buildPill('Daily', AppColors.primary),
            ),
            const SizedBox(height: AppTheme.spacing16),
            DailyHadithCard(dailyHadith: _dailyHadith!),
            const SizedBox(height: AppTheme.spacing24),
          ],

          // Collections Header
          _buildSectionHeader(
            'Hadith Collections',
            'Explore authentic collections of hadiths',
            FontAwesomeIcons.bookOpen,
          ),
          const SizedBox(height: AppTheme.spacing16),

          // Collections Grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.8,
              crossAxisSpacing: AppTheme.spacing16,
              mainAxisSpacing: AppTheme.spacing16,
            ),
            itemCount: _collections.length,
            itemBuilder: (context, index) {
              final collection = _collections[index];
              return HadithCollectionCard(
                collection: collection,
                colorIndex: index,
                onTap: () => _navigateToCollection(collection),
              );
            },
          ),

          // Bottom padding
          const SizedBox(height: AppTheme.spacing24),
        ],
      ),
    );
  }

  // Custom section header with icon
  Widget _buildSectionHeader(String title, String? subtitle, IconData icon, {Widget? action}) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            size: 16,
            color: primaryColor,
          ),
        ),
        SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              if (subtitle != null)
                Text(
                  subtitle,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),
        ),
        if (action != null) action,
      ],
    );
  }

  // Pill-shaped label for section headers
  Widget _buildPill(String text, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        text,
        style: GoogleFonts.poppins(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
    );
  }

  Future<void> _continuePreviousReading() async {
    if (_lastReadProgress == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('No previous reading found'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    // Find the collection
    try {
      final collection = _collections.firstWhere(
        (c) => c.name == _lastReadProgress!.collectionName,
      );

      // Navigate to the reader screen
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => HadithCollectionScreen(
            collection: collection,
          ),
        ),
      );
    } catch (e) {
      print('Error navigating to last read: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to continue reading'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
}