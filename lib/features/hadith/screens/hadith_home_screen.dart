import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/hadith/models/hadith_models.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_collection_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_search_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/favorite_hadiths_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_detail_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/services/hadith_service.dart';
import 'package:new_islamic_app_uk/loading_indicator.dart';

import 'package:new_islamic_app_uk/theme/app_colors.dart';
import 'package:new_islamic_app_uk/theme/app_text_styles.dart';

class HadithHomeScreen extends StatefulWidget {
  const HadithHomeScreen({Key? key}) : super(key: key);

  @override
  State<HadithHomeScreen> createState() => _HadithHomeScreenState();
}

class _HadithHomeScreenState extends State<HadithHomeScreen> {
  final HadithService _hadithService = HadithService();
  final TextEditingController _searchController = TextEditingController();
  final Color primaryColor = Color(URLs().colorScheme);

  bool _isLoading = true;
  bool _hasError = false;
  late List<HadithCollection> _collections;
  DailyHadith? _dailyHadith;
  final String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      // Load collections
      _collections = _hadithService.getAllCollections();

      // Load daily hadith
      _dailyHadith = await _hadithService.getDailyHadith();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading hadith data: $e');
      setState(() {
        _isLoading = false;
        _hasError = true;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load hadith data'),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _loadData,
            ),
          ),
        );
      }
    }
  }



  void _navigateToCollection(HadithCollection collection) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HadithCollectionScreen(collection: collection),
      ),
    );
  }

  void _navigateToSearch() {
    if (_searchQuery.trim().isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => HadithSearchScreen(
            initialQuery: _searchQuery,
          ),
        ),
      );
    } else {
      // Open search with empty query
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const HadithSearchScreen(),
        ),
      );
    }
  }

  void _navigateToFavorites() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FavoriteHadithsScreen(),
      ),
    );
  }





  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Hadith',
          style: AppTextStyles.headlineMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.bookmark_border, color: AppColors.textPrimary),
            onPressed: _navigateToFavorites,
          ),
          IconButton(
            icon: const Icon(Icons.share, color: AppColors.textPrimary),
            onPressed: () {
              // Share functionality
            },
          ),
          IconButton(
            icon: const Icon(Icons.text_fields, color: AppColors.textPrimary),
            onPressed: () {
              // Text size adjustment
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadData,
        color: AppColors.primary,
        child: _isLoading
            ? const Center(child: LoadingIndicator())
            : _hasError
                ? _buildErrorView()
                : _buildModernContent(),
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FontAwesomeIcons.circleExclamation,
              size: 64,
              color: Colors.grey[400],
            ),
            SizedBox(height: 24),
            Text(
              'Failed to Load Data',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            Text(
              'We couldn\'t load the hadith data. Please check your connection and try again.',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadData,
              icon: Icon(Icons.refresh),
              label: Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernContent() {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search Bar
          Container(
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant,
              borderRadius: BorderRadius.circular(12.0),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search Hadith',
                hintStyle: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: AppColors.textSecondary,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 16.0,
                ),
              ),
              onSubmitted: (value) => _navigateToSearch(),
            ),
          ),
          const SizedBox(height: 30.0),

          // Hadith of the Day
          Text(
            'Hadith of the Day',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 16.0),
          _buildHadithOfTheDay(),
          const SizedBox(height: 30.0),

          // Hadith Collections
          Text(
            'Hadith Collections',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 16.0),
          _buildHadithCollections(),
        ],
      ),
    );
  }

  Widget _buildHadithOfTheDay() {
    if (_dailyHadith == null) {
      return Container(
        padding: const EdgeInsets.all(20.0),
        decoration: BoxDecoration(
          color: AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Center(
          child: Text(
            'Loading hadith of the day...',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ),
      );
    }

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => HadithDetailScreen(
              hadithText: _dailyHadith!.hadith.arabic,
              narrator: _dailyHadith!.hadith.narrator,
              translation: _dailyHadith!.hadith.english,
              explanation: 'This hadith emphasizes the importance of consistency in performing good deeds, even if they seem small. Regularity in worship and righteous actions is more pleasing to Allah than sporadic acts of great magnitude.',
              reference: _dailyHadith!.hadith.reference,
              collection: _dailyHadith!.collectionName,
            ),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(20.0),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              const Color(0xFFF5F1E8),
              const Color(0xFFEDE7D3),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16.0),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10.0,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Islamic pattern or icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: const Color(0xFFD4AF37).withOpacity(0.2),
                borderRadius: BorderRadius.circular(12.0),
              ),
              child: const Icon(
                FontAwesomeIcons.quoteLeft,
                color: Color(0xFFD4AF37),
                size: 24,
              ),
            ),
            const SizedBox(height: 16.0),
            Text(
              'The best of you are those who have the best character.',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 12.0),
            Text(
              'Sahih Bukhari 6035',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHadithCollections() {
    final List<Map<String, dynamic>> hadithCollections = [
      {
        'name': 'Bukhari',
        'icon': FontAwesomeIcons.bookOpen,
        'color': const Color(0xFFE8B86D),
      },
      {
        'name': 'Muslim',
        'icon': FontAwesomeIcons.mosque,
        'color': const Color(0xFF4A9B8E),
      },
      {
        'name': 'Abu Dawood',
        'icon': FontAwesomeIcons.book,
        'color': const Color(0xFF2E8B8B),
      },
      {
        'name': 'Tirmidhi',
        'icon': FontAwesomeIcons.scroll,
        'color': const Color(0xFF4A9B8E),
      },
      {
        'name': 'Nasa\'i',
        'icon': FontAwesomeIcons.feather,
        'color': const Color(0xFF2E8B8B),
      },
      {
        'name': 'Ibn Majah',
        'icon': FontAwesomeIcons.bookQuran,
        'color': const Color(0xFF4A9B8E),
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: 16.0,
        mainAxisSpacing: 16.0,
      ),
      itemCount: hadithCollections.length,
      itemBuilder: (context, index) {
        final collection = hadithCollections[index];
        return GestureDetector(
          onTap: () {
            // Find the actual collection and navigate
            try {
              final actualCollection = _collections.firstWhere(
                (c) => c.title.toLowerCase().contains(collection['name'].toLowerCase()) ||
                       c.name.toLowerCase().contains(collection['name'].toLowerCase()),
              );
              _navigateToCollection(actualCollection);
            } catch (e) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Collection not available'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }
          },
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.cardBackground,
              borderRadius: BorderRadius.circular(16.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8.0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: collection['color'].withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Icon(
                    collection['icon'],
                    color: collection['color'],
                    size: 24,
                  ),
                ),
                const SizedBox(height: 12.0),
                Text(
                  collection['name'],
                  style: AppTextStyles.titleSmall.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }


}