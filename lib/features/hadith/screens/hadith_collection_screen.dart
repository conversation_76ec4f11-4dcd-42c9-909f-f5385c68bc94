import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/hadith/models/hadith_models.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_reader_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/services/hadith_service.dart';
import 'package:new_islamic_app_uk/loading_indicator.dart';

class HadithCollectionScreen extends StatefulWidget {
  final HadithCollection collection;

  const HadithCollectionScreen({Key? key, required this.collection}) : super(key: key);

  @override
  State<HadithCollectionScreen> createState() => _HadithCollectionScreenState();
}

class _HadithCollectionScreenState extends State<HadithCollectionScreen> with SingleTickerProviderStateMixin {
  final HadithService _hadithService = HadithService();
  bool _isLoading = true;
  List<Hadith>? _hadiths;
  List<HadithChapter>? _chapters;
  int _currentPage = 1;
  final int _pageSize = 15;
  bool _isLoadingMore = false;
  bool _hasReachedEnd = false;
  late ScrollController _scrollController;
  
  // Tab controller for switching between hadiths and chapters
  late TabController _tabController;

  // Scroll listener with debouncing
  bool _isLoadingMoreDebounced = false;

  @override
  void initState() {
    super.initState();
    _hadiths = [];
    _chapters = [];
    _tabController = TabController(length: 2, vsync: this);
    _scrollController = ScrollController()..addListener(_scrollListener);
    _loadInitialData();
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }
  
  void _scrollListener() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 500 &&
        !_isLoadingMore &&
        !_hasReachedEnd &&
        !_isLoadingMoreDebounced &&
        _tabController.index == 0) {
      
      _isLoadingMoreDebounced = true;
      
      // Use debouncing to avoid multiple rapid calls
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          _loadMoreHadiths();
          _isLoadingMoreDebounced = false;
        }
      });
    }
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
      _hadiths = [];
      _chapters = [];
    });

    try {
      // Load collection data in parallel using Future.wait for better performance
      final results = await Future.wait([
        _hadithService.getCollection(
          widget.collection.name,
          page: _currentPage,
          limit: _pageSize,
        ),
        _hadithService.getChapters(widget.collection.name),
      ]);
      
      final collection = results[0] as HadithCollection;
      final chapters = results[1] as List<HadithChapter>?;
      
      // Clean hadith text to ensure no HTML-like tags are displayed
      List<Hadith> cleanedHadiths = [];
      if (collection.hadiths.isNotEmpty) {
        cleanedHadiths = collection.hadiths.map(_cleanHadithText).toList();
      }
      
      if (mounted) {
        setState(() {
          _hadiths = cleanedHadiths;
          _chapters = chapters ?? [];
          _isLoading = false;
          _hasReachedEnd = collection.hadiths.length < _pageSize;
        });
      }
    } catch (e) {
      print('Error loading initial data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hadiths = [];
          _chapters = [];
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load hadith collection data'),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _loadInitialData,
            ),
          ),
        );
      }
    }
  }
  
  // Helper function to clean hadith text at the UI level
  Hadith _cleanHadithText(Hadith hadith) {
    String cleanArabic = hadith.arabic;
    String cleanEnglish = hadith.english;
    String cleanChapterTitle = hadith.chapterTitle;
    
    // Clean tooltip tags
    cleanArabic = cleanArabic.replaceAll(RegExp(r'="\w+"'), '');
    cleanArabic = cleanArabic.replaceAll(RegExp(r'="tooltip[^"]*"'), '');
    
    // Clean narrator ID tags
    cleanArabic = cleanArabic.replaceAll(RegExp(r'narrator id="[^"]*"'), '');
    
    // Remove HTML-like tags
    cleanArabic = cleanArabic.replaceAll(RegExp(r'<[^>]*>'), '');
    
    // Remove square brackets
    cleanArabic = cleanArabic.replaceAll('[', '').replaceAll(']', '');
    
    // Fix common formatting issues
    cleanArabic = cleanArabic.replaceAll('p>', '');
    cleanArabic = cleanArabic.replaceAll('prematn', '');
    cleanArabic = cleanArabic.replaceAll('matn', '');
    cleanArabic = cleanArabic.replaceAll('//', '');
    cleanArabic = cleanArabic.replaceAll('narrator', '');
    cleanArabic = cleanArabic.replaceAll('tooltip', '');
    
    // Fix double spaces and trim
    cleanArabic = cleanArabic.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    // Apply similar cleaning to English text
    cleanEnglish = cleanEnglish.replaceAll(RegExp(r'<[^>]*>'), '');
    cleanEnglish = cleanEnglish.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    // Clean chapter title
    cleanChapterTitle = cleanChapterTitle.replaceAll(RegExp(r'<[^>]*>'), '');
    cleanChapterTitle = cleanChapterTitle.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    return hadith.copyWith(
      arabic: cleanArabic,
      english: cleanEnglish,
      chapterTitle: cleanChapterTitle,
    );
  }
  
  Future<void> _loadMoreHadiths() async {
    if (_isLoadingMore || _hasReachedEnd) return;
    
    setState(() {
      _isLoadingMore = true;
    });
    
    try {
      _currentPage++;
      final collection = await _hadithService.getCollection(
        widget.collection.name,
        page: _currentPage,
        limit: _pageSize,
      );
      
      if (mounted) {
        setState(() {
          if (_hadiths != null) {
            _hadiths!.addAll(collection.hadiths.map(_cleanHadithText).toList());
          } else {
            _hadiths = collection.hadiths.map(_cleanHadithText).toList();
          }
          _isLoadingMore = false;
          _hasReachedEnd = collection.hadiths.length < _pageSize;
        });
      }
    } catch (e) {
      print('Error loading more hadiths: $e');
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }
  
  void _navigateToHadith(Hadith hadith) {
    // Save the reading position
    _hadithService.saveReadingProgress(widget.collection.name, hadith.number);
    
    // Navigate to the hadith reader
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HadithReaderScreen(
          collection: widget.collection,
          initialHadithNumber: hadith.number,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: RefreshIndicator(
        onRefresh: _loadInitialData,
        color: Color(URLs().colorScheme),
        child: _isLoading ? 
          const Center(child: LoadingIndicator()) : 
          NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                SliverAppBar(
                  expandedHeight: 150.0,
                  floating: false,
                  pinned: true,
                  backgroundColor: Color(URLs().colorScheme),
                  leading: IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                  flexibleSpace: FlexibleSpaceBar(
                    title: Text(
                      widget.collection.title,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    background: Stack(
                      fit: StackFit.expand,
                      children: [
                        // Gradient background
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Color(URLs().colorScheme),
                                Color(URLs().colorScheme).withOpacity(0.8),
                              ],
                            ),
                          ),
                        ),
                        // Decorative elements
                        Positioned(
                          right: -20,
                          bottom: -20,
                          child: Opacity(
                            opacity: 0.1,
                            child: Icon(
                              FontAwesomeIcons.bookQuran,
                              size: 100,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        // Statistics
                        Positioned(
                          bottom: 60,
                          left: 16,
                          right: 16,
                          child: Row(
                            children: [
                              // Hadiths count
                              Row(
                                children: [
                                  Icon(
                                    FontAwesomeIcons.book,
                                    size: 14,
                                    color: Colors.white.withOpacity(0.9),
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    '${widget.collection.totalHadithCount} Hadiths',
                                    style: GoogleFonts.poppins(
                                      fontSize: 12,
                                      color: Colors.white.withOpacity(0.9),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(width: 16),
                              // Chapters count
                              Row(
                                children: [
                                  Icon(
                                    FontAwesomeIcons.layerGroup,
                                    size: 14,
                                    color: Colors.white.withOpacity(0.9),
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    _chapters != null ? 
                                      '${_chapters!.length} Chapters' : 
                                      'Loading chapters...',
                                    style: GoogleFonts.poppins(
                                      fontSize: 12,
                                      color: Colors.white.withOpacity(0.9),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SliverPersistentHeader(
                  delegate: _SliverAppBarDelegate(
                    TabBar(
                      controller: _tabController,
                      indicatorColor: Color(URLs().colorScheme),
                      labelColor: Color(URLs().colorScheme),
                      unselectedLabelColor: Colors.grey[600],
                      labelStyle: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      tabs: const [
                        Tab(text: 'Hadiths'),
                        Tab(text: 'Chapters'),
                      ],
                    ),
                  ),
                  pinned: true,
                ),
              ];
            },
            body: TabBarView(
              controller: _tabController,
              children: [
                _buildHadithsTab(),
                _buildChaptersTab(),
              ],
            ),
          ),
      ),
    );
  }
  
  Widget _buildHadithsTab() {
    if (_hadiths == null || _hadiths!.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FontAwesomeIcons.bookOpen,
              size: 64,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 16),
            Text(
              'No hadiths available',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try refreshing or check another collection',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadInitialData,
              icon: const Icon(Icons.refresh),
              label: const Text('Refresh'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(URLs().colorScheme),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _hadiths!.length + (_hasReachedEnd ? 1 : 2), // +1 for loading indicator or next chapter button
      itemBuilder: (context, index) {
        // If we're at the last item
        if (index == _hadiths!.length) {
          if (_isLoadingMore) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Center(
                child: SizedBox(
                  height: 40,
                  width: 40,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(Color(URLs().colorScheme)),
                  ),
                ),
              ),
            );
          } else if (_hasReachedEnd) {
            // Show next chapter button if there's another chapter
            if (_chapters != null && _chapters!.isNotEmpty) {
              // Find current chapter
              final currentChapter = _getCurrentChapter();
              
              // Find next chapter index
              int nextChapterIndex = -1;
              if (currentChapter != null) {
                nextChapterIndex = _chapters!.indexOf(currentChapter) + 1;
              }
              
              // If there's a next chapter, show button to navigate to it
              if (nextChapterIndex >= 0 && nextChapterIndex < _chapters!.length) {
                final nextChapter = _chapters![nextChapterIndex];
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 24),
                  child: Column(
                    children: [
                      Divider(),
                      const SizedBox(height: 16),
                      Text(
                        'You\'ve reached the end of this chapter',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: () => _navigateToChapter(nextChapter),
                        icon: const Icon(Icons.arrow_forward),
                        label: Text('Next Chapter: ${nextChapter.titleEnglish}'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(URLs().colorScheme),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }
            }
            
            // If no next chapter, show end of collection message
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 24),
              child: Column(
                children: [
                  Divider(),
                  const SizedBox(height: 16),
                  Text(
                    'End of Collection',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You\'ve reached the end of this collection',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }
          return SizedBox.shrink();
        } else if (index == _hadiths!.length + 1) {
          // Footer
          if (!_isLoadingMore && !_hasReachedEnd) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 24),
              child: Center(
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(URLs().colorScheme)),
                ),
              ),
            );
          }
          return SizedBox.shrink();
        }

        final hadith = _hadiths![index];
        return _buildHadithCard(hadith);
      },
    );
  }

  // Helper method to find the current chapter based on hadiths
  HadithChapter? _getCurrentChapter() {
    if (_hadiths == null || _hadiths!.isEmpty || _chapters == null || _chapters!.isEmpty) {
      return null;
    }
    
    // Get the last hadith's chapter number
    final lastHadith = _hadiths!.last;
    
    // Find the chapter that contains this hadith
    for (int i = 0; i < _chapters!.length; i++) {
      final currentChapter = _chapters![i];
      final nextChapterIndex = i + 1;
      
      int endNumber;
      if (nextChapterIndex < _chapters!.length) {
        endNumber = _chapters![nextChapterIndex].startingHadithNumber - 1;
      } else {
        endNumber = widget.collection.totalHadithCount;
      }
      
      if (lastHadith.number >= currentChapter.startingHadithNumber && 
          lastHadith.number <= endNumber) {
        return currentChapter;
      }
    }
    
    return null;
  }

  void _navigateToChapter(HadithChapter chapter) {
    // Reset loading state
    setState(() {
      _isLoading = true;
      _hadiths = [];
      _currentPage = 1; // Reset paging
      _hasReachedEnd = false;
    });
    
    // We need to load hadiths starting from this specific chapter
    // Since the service doesn't support chapter filtering directly,
    // we'll need to fetch individual hadiths starting from the chapter's first hadith
    
    _loadHadithsForChapter(chapter).then((_) {
      // Successfully loaded chapter hadiths
      // Switch to hadiths tab
      _tabController.animateTo(0);
      
      // Scroll to top
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }).catchError((e) {
      print('Error loading chapter hadiths: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to load chapter hadiths'),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: () => _navigateToChapter(chapter),
            ),
          ),
        );
      }
    });
  }

  // Helper method to load hadiths for a specific chapter
  Future<void> _loadHadithsForChapter(HadithChapter chapter) async {
    try {
      List<Hadith> chapterHadiths = [];
      
      // Load individual hadiths starting from the chapter's first hadith
      final startingHadithNumber = chapter.startingHadithNumber;
      for (int i = 0; i < _pageSize; i++) {
        final hadithNumber = startingHadithNumber + i;
        final hadith = await _hadithService.getHadith(widget.collection.name, hadithNumber);
        if (hadith != null) {
          chapterHadiths.add(hadith);
        } else {
          // If we can't get a hadith, we've reached the end
          break;
        }
      }
      
      if (mounted) {
        setState(() {
          _hadiths = chapterHadiths.map(_cleanHadithText).toList();
          _isLoading = false;
          _hasReachedEnd = chapterHadiths.length < _pageSize;
        });
      }
    } catch (e) {
      print('Error in _loadHadithsForChapter: $e');
      throw e; // Rethrow to be caught by the caller
    }
  }

  Widget _buildChaptersTab() {
    if (_chapters == null || _chapters!.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FontAwesomeIcons.layerGroup,
              size: 64,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 16),
            Text(
              'No chapters available',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try refreshing or check another collection',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadInitialData,
              icon: const Icon(Icons.refresh),
              label: const Text('Refresh'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(URLs().colorScheme),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _chapters!.length,
      itemBuilder: (context, index) {
        final chapter = _chapters![index];
        return _buildChapterCard(chapter, index);
      },
    );
  }
  
  Widget _buildHadithCard(Hadith hadith) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _navigateToHadith(hadith),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hadith number and chapter row
              Row(
                children: [
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: Color(URLs().colorScheme),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        '${hadith.number}',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  if (hadith.chapterTitle.isNotEmpty)
                    Expanded(
                      child: Text(
                        hadith.chapterTitle,
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[800],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                ],
              ),
              
              SizedBox(height: 12),
              
              // Arabic text - limited preview
              Container(
                constraints: BoxConstraints(maxHeight: 120),
                child: ClipRect(
                  child: SingleChildScrollView(
                    physics: NeverScrollableScrollPhysics(),
                    child: ShaderMask(
                      shaderCallback: (Rect rect) {
                        return LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [Colors.black, Colors.transparent],
                          stops: [0.7, 1.0],
                        ).createShader(rect);
                      },
                      blendMode: BlendMode.dstIn,
                      child: Text(
                        hadith.arabic,
                        style: GoogleFonts.amiri(
                          fontSize: 18,
                          height: 1.6,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.right,
                        textDirection: TextDirection.rtl,
                      ),
                    ),
                  ),
                ),
              ),
              
              SizedBox(height: 12),
              
              // Reference and read more row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      hadith.reference,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                        color: Colors.grey[600],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 8),
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Color(URLs().colorScheme).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Read',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Color(URLs().colorScheme),
                          ),
                        ),
                        SizedBox(width: 4),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 12,
                          color: Color(URLs().colorScheme),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildChapterCard(HadithChapter chapter, int index) {
    return Card(
      margin: EdgeInsets.only(bottom: 12),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          // Navigate to first hadith in this chapter
          if (_hadiths != null && _hadiths!.isNotEmpty) {
            // Find the first hadith with this chapter number
            final firstHadith = _hadiths!.firstWhere(
              (h) => h.chapterNumber == chapter.chapterNumber,
              orElse: () => Hadith(
                id: '${widget.collection.name}_${chapter.startingHadithNumber}',
                number: chapter.startingHadithNumber,
                arabic: '',
                english: '',
                reference: '',
                chapterNumber: chapter.chapterNumber,
              ),
            );
            _navigateToHadith(firstHadith);
          } else {
            // Fallback to a basic hadith
            _navigateToHadith(Hadith(
              id: '${widget.collection.name}_${chapter.startingHadithNumber}',
              number: chapter.startingHadithNumber,
              arabic: '',
              english: '',
              reference: '',
              chapterNumber: chapter.chapterNumber,
            ));
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Color(URLs().colorScheme),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    '${chapter.chapterNumber}',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // English title
                    Text(
                      chapter.titleEnglish,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 6),
                    Row(
                      children: [
                        Icon(
                          FontAwesomeIcons.bookOpen,
                          size: 12,
                          color: Colors.grey[500],
                        ),
                        SizedBox(width: 6),
                        Text(
                          '${chapter.hadithCount} hadiths',
                          style: GoogleFonts.poppins(
                            fontSize: 13,
                            color: Colors.grey[500],
                          ),
                        ),
                        Spacer(),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Color(URLs().colorScheme).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Read',
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: Color(URLs().colorScheme),
                                ),
                              ),
                              SizedBox(width: 4),
                              Icon(
                                Icons.arrow_forward_ios,
                                size: 12,
                                color: Color(URLs().colorScheme),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;
  
  _SliverAppBarDelegate(this._tabBar);
  
  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Colors.white,
      child: _tabBar,
    );
  }
  
  @override
  double get maxExtent => _tabBar.preferredSize.height;
  
  @override
  double get minExtent => _tabBar.preferredSize.height;
  
  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
} 