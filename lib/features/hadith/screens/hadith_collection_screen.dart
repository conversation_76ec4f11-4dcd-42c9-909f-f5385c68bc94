import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/features/hadith/models/hadith_models.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_reader_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/services/hadith_service.dart';
import 'package:new_islamic_app_uk/loading_indicator.dart';
import '../../../theme/app_colors.dart';
import '../../../theme/app_text_styles.dart';

class HadithCollectionScreen extends StatefulWidget {
  final HadithCollection collection;

  const HadithCollectionScreen({Key? key, required this.collection}) : super(key: key);

  @override
  State<HadithCollectionScreen> createState() => _HadithCollectionScreenState();
}

class _HadithCollectionScreenState extends State<HadithCollectionScreen> with SingleTickerProviderStateMixin {
  final HadithService _hadithService = HadithService();
  bool _isLoading = true;
  List<Hadith>? _hadiths;
  List<HadithChapter>? _chapters;
  int _currentPage = 1;
  final int _pageSize = 15;
  bool _isLoadingMore = false;
  bool _hasReachedEnd = false;
  late ScrollController _scrollController;

  // Tab controller for switching between hadiths and chapters
  late TabController _tabController;

  // Scroll listener with debouncing
  bool _isLoadingMoreDebounced = false;

  // Search functionality
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  List<Hadith>? _searchResults;

  @override
  void initState() {
    super.initState();
    _hadiths = [];
    _chapters = [];
    _tabController = TabController(length: 2, vsync: this);
    _scrollController = ScrollController()..addListener(_scrollListener);
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 500 &&
        !_isLoadingMore &&
        !_hasReachedEnd &&
        !_isLoadingMoreDebounced &&
        _tabController.index == 0) {

      _isLoadingMoreDebounced = true;

      // Use debouncing to avoid multiple rapid calls
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          _loadMoreHadiths();
          _isLoadingMoreDebounced = false;
        }
      });
    }
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
      _hadiths = [];
      _chapters = [];
    });

    try {
      // Load collection data in parallel using Future.wait for better performance
      final results = await Future.wait([
        _hadithService.getCollection(
          widget.collection.name,
          page: _currentPage,
          limit: _pageSize,
        ),
        _hadithService.getChapters(widget.collection.name),
      ]);

      final collection = results[0] as HadithCollection;
      final chapters = results[1] as List<HadithChapter>?;

      // Clean hadith text to ensure no HTML-like tags are displayed
      List<Hadith> cleanedHadiths = [];
      if (collection.hadiths.isNotEmpty) {
        cleanedHadiths = collection.hadiths.map(_cleanHadithText).toList();
      }

      if (mounted) {
        setState(() {
          _hadiths = cleanedHadiths;
          _chapters = chapters ?? [];
          _isLoading = false;
          _hasReachedEnd = collection.hadiths.length < _pageSize;
        });
      }
    } catch (e) {
      print('Error loading initial data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hadiths = [];
          _chapters = [];
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load hadith collection data'),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _loadInitialData,
            ),
          ),
        );
      }
    }
  }

  // Helper function to clean hadith text at the UI level
  Hadith _cleanHadithText(Hadith hadith) {
    String cleanArabic = hadith.arabic;
    String cleanEnglish = hadith.english;
    String cleanChapterTitle = hadith.chapterTitle;

    // Clean tooltip tags
    cleanArabic = cleanArabic.replaceAll(RegExp(r'="\w+"'), '');
    cleanArabic = cleanArabic.replaceAll(RegExp(r'="tooltip[^"]*"'), '');

    // Clean narrator ID tags
    cleanArabic = cleanArabic.replaceAll(RegExp(r'narrator id="[^"]*"'), '');

    // Remove HTML-like tags
    cleanArabic = cleanArabic.replaceAll(RegExp(r'<[^>]*>'), '');

    // Remove square brackets
    cleanArabic = cleanArabic.replaceAll('[', '').replaceAll(']', '');

    // Fix common formatting issues
    cleanArabic = cleanArabic.replaceAll('p>', '');
    cleanArabic = cleanArabic.replaceAll('prematn', '');
    cleanArabic = cleanArabic.replaceAll('matn', '');
    cleanArabic = cleanArabic.replaceAll('//', '');
    cleanArabic = cleanArabic.replaceAll('narrator', '');
    cleanArabic = cleanArabic.replaceAll('tooltip', '');

    // Fix double spaces and trim
    cleanArabic = cleanArabic.replaceAll(RegExp(r'\s+'), ' ').trim();

    // Apply similar cleaning to English text
    cleanEnglish = cleanEnglish.replaceAll(RegExp(r'<[^>]*>'), '');
    cleanEnglish = cleanEnglish.replaceAll(RegExp(r'\s+'), ' ').trim();

    // Clean chapter title
    cleanChapterTitle = cleanChapterTitle.replaceAll(RegExp(r'<[^>]*>'), '');
    cleanChapterTitle = cleanChapterTitle.replaceAll(RegExp(r'\s+'), ' ').trim();

    return hadith.copyWith(
      arabic: cleanArabic,
      english: cleanEnglish,
      chapterTitle: cleanChapterTitle,
    );
  }

  Future<void> _loadMoreHadiths() async {
    if (_isLoadingMore || _hasReachedEnd) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      _currentPage++;
      final collection = await _hadithService.getCollection(
        widget.collection.name,
        page: _currentPage,
        limit: _pageSize,
      );

      if (mounted) {
        setState(() {
          if (_hadiths != null) {
            _hadiths!.addAll(collection.hadiths.map(_cleanHadithText).toList());
          } else {
            _hadiths = collection.hadiths.map(_cleanHadithText).toList();
          }
          _isLoadingMore = false;
          _hasReachedEnd = collection.hadiths.length < _pageSize;
        });
      }
    } catch (e) {
      print('Error loading more hadiths: $e');
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  void _navigateToHadith(Hadith hadith) {
    // Save the reading position
    _hadithService.saveReadingProgress(widget.collection.name, hadith.number);

    // Navigate to the hadith reader
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HadithReaderScreen(
          collection: widget.collection,
          initialHadithNumber: hadith.number,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
        title: _isSearching
          ? TextField(
              controller: _searchController,
              autofocus: true,
              decoration: InputDecoration(
                hintText: 'Search hadiths...',
                hintStyle: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
                border: InputBorder.none,
              ),
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textPrimary,
              ),
              onChanged: _performSearch,
            )
          : Text(
              widget.collection.title,
              style: AppTextStyles.headlineMedium.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
        centerTitle: !_isSearching,
        actions: [
          IconButton(
            icon: Icon(
              _isSearching ? Icons.close : Icons.search,
              color: AppColors.textPrimary,
            ),
            onPressed: _toggleSearch,
          ),
        ],
        bottom: _isLoading || _isSearching ? null : PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0),
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant.withOpacity(0.3),
              borderRadius: BorderRadius.circular(12.0),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(10.0),
              ),
              labelColor: Colors.white,
              unselectedLabelColor: AppColors.textSecondary,
              labelStyle: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
              unselectedLabelStyle: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              splashFactory: NoSplash.splashFactory,
              overlayColor: MaterialStateProperty.all(Colors.transparent),
              tabs: const [
                Tab(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 12.0),
                    child: Text('Hadiths'),
                  ),
                ),
                Tab(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 12.0),
                    child: Text('Chapters'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      body: _isLoading
        ? const Center(child: LoadingIndicator())
        : _isSearching
          ? _buildSearchResults()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildHadithsTab(),
                _buildChaptersTab(),
              ],
            ),
    );
  }
  Widget _buildHadithsTab() {
    return RefreshIndicator(
      onRefresh: _loadInitialData,
      color: AppColors.primary,
      child: _hadiths == null || _hadiths!.isEmpty
        ? _buildEmptyState()
        : ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(20.0),
            itemCount: _hadiths!.length + (_hasReachedEnd ? 0 : 1),
            itemBuilder: (context, index) {
              if (index == _hadiths!.length) {
                // Loading indicator at the bottom
                return _isLoadingMore
                  ? const Padding(
                      padding: EdgeInsets.symmetric(vertical: 20.0),
                      child: Center(child: CircularProgressIndicator()),
                    )
                  : const SizedBox.shrink();
              }
              return _buildHadithCard(_hadiths![index]);
            },
          ),
    );
  }

  Widget _buildChaptersTab() {
    return RefreshIndicator(
      onRefresh: _loadInitialData,
      color: AppColors.primary,
      child: _chapters == null || _chapters!.isEmpty
        ? _buildEmptyChaptersState()
        : ListView.builder(
            padding: const EdgeInsets.all(20.0),
            itemCount: _chapters!.length,
            itemBuilder: (context, index) {
              return _buildChapterCard(_chapters![index]);
            },
          ),
    );
  }





  Widget _buildHadithCard(Hadith hadith) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: InkWell(
        onTap: () => _navigateToHadith(hadith),
        borderRadius: BorderRadius.circular(16.0),
        child: Container(
          padding: const EdgeInsets.all(20.0),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16.0),
            border: Border.all(
              color: AppColors.surfaceVariant,
              width: 1,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hadith Number
              Text(
                'Hadith ${hadith.number}',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(width: 16.0),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      hadith.chapterTitle.isNotEmpty
                        ? hadith.chapterTitle
                        : 'Actions are judged by intentions',
                      style: AppTextStyles.titleMedium.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8.0),

                    // Narrator
                    Text(
                      'Narrated by ${hadith.reference}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),

              // Islamic Icon
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: const Color(0xFF4A6741), // Dark green from image
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: const Icon(
                  FontAwesomeIcons.mosque,
                  color: Color(0xFFD4AF37), // Gold color
                  size: 24,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            FontAwesomeIcons.bookOpen,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'No hadiths available',
            style: AppTextStyles.titleLarge.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try refreshing or check another collection',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadInitialData,
            icon: const Icon(Icons.refresh),
            label: const Text('Refresh'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChapterCard(HadithChapter chapter) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: InkWell(
        onTap: () => _navigateToChapter(chapter),
        borderRadius: BorderRadius.circular(16.0),
        child: Container(
          padding: const EdgeInsets.all(20.0),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16.0),
            border: Border.all(
              color: AppColors.surfaceVariant,
              width: 1,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Chapter Number
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: Center(
                  child: Text(
                    '${chapter.chapterNumber}',
                    style: AppTextStyles.titleMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16.0),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      chapter.titleEnglish,
                      style: AppTextStyles.titleMedium.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8.0),

                    // Hadith count
                    Text(
                      '${chapter.hadithCount} hadiths',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),

              // Arrow Icon
              const Icon(
                Icons.arrow_forward_ios,
                color: AppColors.textSecondary,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyChaptersState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            FontAwesomeIcons.layerGroup,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'No chapters available',
            style: AppTextStyles.titleLarge.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try refreshing or check another collection',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadInitialData,
            icon: const Icon(Icons.refresh),
            label: const Text('Refresh'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToChapter(HadithChapter chapter) {
    // Navigate to the first hadith in this chapter
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HadithReaderScreen(
          collection: widget.collection,
          initialHadithNumber: chapter.startingHadithNumber,
        ),
      ),
    );
  }

  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (!_isSearching) {
        _searchController.clear();
        _searchResults = null;
      }
    });
  }

  void _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = null;
      });
      return;
    }

    try {
      final results = await _hadithService.searchHadiths(
        query,
        collectionName: widget.collection.name,
        limit: 50,
      );

      if (mounted) {
        setState(() {
          _searchResults = results;
        });
      }
    } catch (e) {
      print('Error searching hadiths: $e');
      if (mounted) {
        setState(() {
          _searchResults = [];
        });
      }
    }
  }

  Widget _buildSearchResults() {
    if (_searchResults == null) {
      return const Center(
        child: Text(
          'Start typing to search hadiths...',
          style: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 16,
          ),
        ),
      );
    }

    if (_searchResults!.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.search_off,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'No results found',
              style: AppTextStyles.titleLarge.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try different keywords or check spelling',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20.0),
      itemCount: _searchResults!.length,
      itemBuilder: (context, index) {
        return _buildHadithCard(_searchResults![index]);
      },
    );
  }

}