import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/features/hadith/models/hadith_models.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_reader_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/services/hadith_service.dart';
import 'package:new_islamic_app_uk/loading_indicator.dart';
import '../../../theme/app_colors.dart';
import '../../../theme/app_text_styles.dart';

class HadithCollectionScreen extends StatefulWidget {
  final HadithCollection collection;

  const HadithCollectionScreen({Key? key, required this.collection}) : super(key: key);

  @override
  State<HadithCollectionScreen> createState() => _HadithCollectionScreenState();
}

class _HadithCollectionScreenState extends State<HadithCollectionScreen> with SingleTickerProviderStateMixin {
  final HadithService _hadithService = HadithService();
  bool _isLoading = true;
  List<Hadith>? _hadiths;
  List<HadithChapter>? _chapters;
  int _currentPage = 1;
  final int _pageSize = 15;
  bool _isLoadingMore = false;
  bool _hasReachedEnd = false;
  late ScrollController _scrollController;

  // Tab controller for switching between hadiths and chapters
  late TabController _tabController;

  // Scroll listener with debouncing
  bool _isLoadingMoreDebounced = false;

  @override
  void initState() {
    super.initState();
    _hadiths = [];
    _chapters = [];
    _tabController = TabController(length: 2, vsync: this);
    _scrollController = ScrollController()..addListener(_scrollListener);
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 500 &&
        !_isLoadingMore &&
        !_hasReachedEnd &&
        !_isLoadingMoreDebounced &&
        _tabController.index == 0) {

      _isLoadingMoreDebounced = true;

      // Use debouncing to avoid multiple rapid calls
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          _loadMoreHadiths();
          _isLoadingMoreDebounced = false;
        }
      });
    }
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
      _hadiths = [];
      _chapters = [];
    });

    try {
      // Load collection data in parallel using Future.wait for better performance
      final results = await Future.wait([
        _hadithService.getCollection(
          widget.collection.name,
          page: _currentPage,
          limit: _pageSize,
        ),
        _hadithService.getChapters(widget.collection.name),
      ]);

      final collection = results[0] as HadithCollection;
      final chapters = results[1] as List<HadithChapter>?;

      // Clean hadith text to ensure no HTML-like tags are displayed
      List<Hadith> cleanedHadiths = [];
      if (collection.hadiths.isNotEmpty) {
        cleanedHadiths = collection.hadiths.map(_cleanHadithText).toList();
      }

      if (mounted) {
        setState(() {
          _hadiths = cleanedHadiths;
          _chapters = chapters ?? [];
          _isLoading = false;
          _hasReachedEnd = collection.hadiths.length < _pageSize;
        });
      }
    } catch (e) {
      print('Error loading initial data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hadiths = [];
          _chapters = [];
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load hadith collection data'),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _loadInitialData,
            ),
          ),
        );
      }
    }
  }

  // Helper function to clean hadith text at the UI level
  Hadith _cleanHadithText(Hadith hadith) {
    String cleanArabic = hadith.arabic;
    String cleanEnglish = hadith.english;
    String cleanChapterTitle = hadith.chapterTitle;

    // Clean tooltip tags
    cleanArabic = cleanArabic.replaceAll(RegExp(r'="\w+"'), '');
    cleanArabic = cleanArabic.replaceAll(RegExp(r'="tooltip[^"]*"'), '');

    // Clean narrator ID tags
    cleanArabic = cleanArabic.replaceAll(RegExp(r'narrator id="[^"]*"'), '');

    // Remove HTML-like tags
    cleanArabic = cleanArabic.replaceAll(RegExp(r'<[^>]*>'), '');

    // Remove square brackets
    cleanArabic = cleanArabic.replaceAll('[', '').replaceAll(']', '');

    // Fix common formatting issues
    cleanArabic = cleanArabic.replaceAll('p>', '');
    cleanArabic = cleanArabic.replaceAll('prematn', '');
    cleanArabic = cleanArabic.replaceAll('matn', '');
    cleanArabic = cleanArabic.replaceAll('//', '');
    cleanArabic = cleanArabic.replaceAll('narrator', '');
    cleanArabic = cleanArabic.replaceAll('tooltip', '');

    // Fix double spaces and trim
    cleanArabic = cleanArabic.replaceAll(RegExp(r'\s+'), ' ').trim();

    // Apply similar cleaning to English text
    cleanEnglish = cleanEnglish.replaceAll(RegExp(r'<[^>]*>'), '');
    cleanEnglish = cleanEnglish.replaceAll(RegExp(r'\s+'), ' ').trim();

    // Clean chapter title
    cleanChapterTitle = cleanChapterTitle.replaceAll(RegExp(r'<[^>]*>'), '');
    cleanChapterTitle = cleanChapterTitle.replaceAll(RegExp(r'\s+'), ' ').trim();

    return hadith.copyWith(
      arabic: cleanArabic,
      english: cleanEnglish,
      chapterTitle: cleanChapterTitle,
    );
  }

  Future<void> _loadMoreHadiths() async {
    if (_isLoadingMore || _hasReachedEnd) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      _currentPage++;
      final collection = await _hadithService.getCollection(
        widget.collection.name,
        page: _currentPage,
        limit: _pageSize,
      );

      if (mounted) {
        setState(() {
          if (_hadiths != null) {
            _hadiths!.addAll(collection.hadiths.map(_cleanHadithText).toList());
          } else {
            _hadiths = collection.hadiths.map(_cleanHadithText).toList();
          }
          _isLoadingMore = false;
          _hasReachedEnd = collection.hadiths.length < _pageSize;
        });
      }
    } catch (e) {
      print('Error loading more hadiths: $e');
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  void _navigateToHadith(Hadith hadith) {
    // Save the reading position
    _hadithService.saveReadingProgress(widget.collection.name, hadith.number);

    // Navigate to the hadith reader
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HadithReaderScreen(
          collection: widget.collection,
          initialHadithNumber: hadith.number,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Hadith Collection',
          style: AppTextStyles.headlineMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: _isLoading
        ? const Center(child: LoadingIndicator())
        : RefreshIndicator(
            onRefresh: _loadInitialData,
            color: AppColors.primary,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Collection Title
                  Text(
                    widget.collection.title,
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const SizedBox(height: 20.0),

                  // Hadiths List
                  if (_hadiths != null && _hadiths!.isNotEmpty)
                    ..._hadiths!.map((hadith) => _buildHadithCard(hadith)).toList()
                  else
                    _buildEmptyState(),

                  // Load More Button
                  if (!_hasReachedEnd && _hadiths != null && _hadiths!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 20.0),
                      child: Center(
                        child: _isLoadingMore
                          ? const CircularProgressIndicator()
                          : ElevatedButton(
                              onPressed: _loadMoreHadiths,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.primary,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24.0,
                                  vertical: 12.0,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12.0),
                                ),
                              ),
                              child: const Text('Load More'),
                            ),
                      ),
                    ),
                ],
              ),
            ),
          ),
    );
  }





  Widget _buildHadithCard(Hadith hadith) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: InkWell(
        onTap: () => _navigateToHadith(hadith),
        borderRadius: BorderRadius.circular(16.0),
        child: Container(
          padding: const EdgeInsets.all(20.0),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16.0),
            border: Border.all(
              color: AppColors.surfaceVariant,
              width: 1,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hadith Number
              Text(
                'Hadith ${hadith.number}',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(width: 16.0),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      hadith.chapterTitle.isNotEmpty
                        ? hadith.chapterTitle
                        : 'Actions are judged by intentions',
                      style: AppTextStyles.titleMedium.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8.0),

                    // Narrator
                    Text(
                      'Narrated by ${hadith.reference}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),

              // Islamic Icon
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: const Color(0xFF4A6741), // Dark green from image
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: const Icon(
                  FontAwesomeIcons.mosque,
                  color: Color(0xFFD4AF37), // Gold color
                  size: 24,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            FontAwesomeIcons.bookOpen,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'No hadiths available',
            style: AppTextStyles.titleLarge.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try refreshing or check another collection',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadInitialData,
            icon: const Icon(Icons.refresh),
            label: const Text('Refresh'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

}