import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/hadith/models/hadith_models.dart';
import 'package:new_islamic_app_uk/features/hadith/services/hadith_service.dart';
import 'package:new_islamic_app_uk/loading_indicator.dart';
import 'package:share_plus/share_plus.dart';

class HadithReaderScreen extends StatefulWidget {
  final HadithCollection collection;
  final int initialHadithNumber;

  const HadithReaderScreen({
    Key? key,
    required this.collection,
    required this.initialHadithNumber,
  }) : super(key: key);

  @override
  State<HadithReaderScreen> createState() => _HadithReaderScreenState();
}

class _HadithReaderScreenState extends State<HadithReaderScreen> {
  final HadithService _hadithService = HadithService();
  bool _isLoading = true;
  int _currentHadithNumber = 0;
  Hadith? _currentHadith;
  double _arabicFontSize = 22;
  double _translationFontSize = 16;
  bool _showTranslation = true;
  
  final Color primaryColor = Color(URLs().colorScheme);
  
  @override
  void initState() {
    super.initState();
    _currentHadithNumber = widget.initialHadithNumber;
    _loadHadith();
  }
  
  Future<void> _loadHadith() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final hadith = await _hadithService.getHadith(
        widget.collection.name,
        _currentHadithNumber,
      );
      
      if (hadith != null) {
        // Additional cleaning at UI level for safety
        final cleanedHadith = _cleanHadithText(hadith);
        
        setState(() {
          _currentHadith = cleanedHadith;
          _isLoading = false;
        });
        
        // Save reading progress
        _hadithService.saveReadingProgress(
          widget.collection.name,
          _currentHadithNumber,
        );
      } else {
        // If hadith not found, try to navigate to the first hadith
        if (_currentHadithNumber != 1) {
          _currentHadithNumber = 1;
          _loadHadith();
        } else {
          // If first hadith also not found, show error
          setState(() {
            _isLoading = false;
          });
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to load hadith'),
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        }
      }
    } catch (e) {
      print('Error loading hadith: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading hadith: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
  
  // Additional layer of cleaning at the UI level
  Hadith _cleanHadithText(Hadith hadith) {
    String cleanArabic = hadith.arabic;
    String cleanEnglish = hadith.english;
    String cleanChapterTitle = hadith.chapterTitle;
    
    // Clean tooltip tags
    cleanArabic = cleanArabic.replaceAll(RegExp(r'="\w+"'), '');
    cleanArabic = cleanArabic.replaceAll(RegExp(r'="tooltip[^"]*"'), '');
    
    // Clean narrator ID tags
    cleanArabic = cleanArabic.replaceAll(RegExp(r'narrator id="[^"]*"'), '');
    
    // Remove HTML-like tags
    cleanArabic = cleanArabic.replaceAll(RegExp(r'<[^>]*>'), '');
    
    // Remove square brackets
    cleanArabic = cleanArabic.replaceAll('[', '').replaceAll(']', '');
    
    // Fix common formatting issues
    cleanArabic = cleanArabic.replaceAll('p>', '');
    cleanArabic = cleanArabic.replaceAll('prematn', '');
    cleanArabic = cleanArabic.replaceAll('matn', '');
    cleanArabic = cleanArabic.replaceAll('//', '');
    cleanArabic = cleanArabic.replaceAll('narrator', '');
    cleanArabic = cleanArabic.replaceAll('tooltip', '');
    
    // Fix double spaces and trim
    cleanArabic = cleanArabic.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    // Apply similar cleaning to English text
    cleanEnglish = cleanEnglish.replaceAll(RegExp(r'<[^>]*>'), '');
    cleanEnglish = cleanEnglish.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    // Clean chapter title
    cleanChapterTitle = cleanChapterTitle.replaceAll(RegExp(r'<[^>]*>'), '');
    cleanChapterTitle = cleanChapterTitle.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    return hadith.copyWith(
      arabic: cleanArabic,
      english: cleanEnglish,
      chapterTitle: cleanChapterTitle,
    );
  }
  
  void _navigateToHadith(int number) {
    if (number < 1 || number > widget.collection.totalHadithCount) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Hadith number out of range'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }
    
    setState(() {
      _currentHadithNumber = number;
    });
    
    _loadHadith();
  }
  
  Future<void> _shareHadith() async {
    if (_currentHadith == null) return;
    
    final text = '''
${_currentHadith!.arabic}

${_currentHadith!.english}

${_currentHadith!.reference}
Source: ${widget.collection.title}
''';
    
    await Share.share(text);
  }
  
  Future<void> _copyHadith() async {
    if (_currentHadith == null) return;
    
    final text = '''
${_currentHadith!.arabic}

${_currentHadith!.english}

${_currentHadith!.reference}
''';
    
    await Clipboard.setData(ClipboardData(text: text));
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Copied to clipboard'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  void _toggleFavorite() {
    // TODO: Implement favorite functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Added to favorites'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  void _increaseFontSize() {
    setState(() {
      _arabicFontSize = _arabicFontSize + 1;
      _translationFontSize = _translationFontSize + 0.5;
    });
  }
  
  void _decreaseFontSize() {
    if (_arabicFontSize <= 16 || _translationFontSize <= 12) return;
    setState(() {
      _arabicFontSize = _arabicFontSize - 1;
      _translationFontSize = _translationFontSize - 0.5;
    });
  }
  
  void _toggleTranslation() {
    setState(() {
      _showTranslation = !_showTranslation;
    });
  }
  
  void _showJumpDialog() {
    final TextEditingController controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Jump to Hadith'),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          decoration: InputDecoration(
            hintText: 'Enter hadith number (1-${widget.collection.totalHadithCount})',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              final number = int.tryParse(controller.text);
              if (number != null) {
                _navigateToHadith(number);
              }
            },
            child: Text('Go'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.collection.title,
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
            if (!_isLoading && _currentHadith != null)
              Text(
                'Hadith ${_currentHadith!.number}',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(FontAwesomeIcons.magnifyingGlass, size: 20, color: Colors.white),
            onPressed: _showJumpDialog,
          ),
          IconButton(
            icon: Icon(FontAwesomeIcons.ellipsisVertical, size: 20, color: Colors.white),
            onPressed: () => _showOptionsBottomSheet(context),
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: LoadingIndicator())
          : _currentHadith == null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        FontAwesomeIcons.circleExclamation,
                        size: 40,
                        color: Colors.grey[500],
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Hadith not found',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[700],
                        ),
                      ),
                      SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: () => _navigateToHadith(1),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: primaryColor,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                        ),
                        child: Text('Go to first hadith'),
                      ),
                    ],
                  ),
                )
              : _buildHadithContent(),
      bottomNavigationBar: _isLoading || _currentHadith == null
          ? null
          : _buildBottomNavigationBar(),
    );
  }
  
  Widget _buildHadithContent() {
    if (_currentHadith == null) return SizedBox.shrink();
    
    return RefreshIndicator(
      onRefresh: _loadHadith,
      color: primaryColor,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Chapter title if available
            if (_currentHadith!.chapterTitle.isNotEmpty)
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(bottom: 20),
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: primaryColor,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  _currentHadith!.chapterTitle,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            
            // Hadith number
            Center(
              child: Container(
                margin: EdgeInsets.only(bottom: 24),
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Text(
                  'Hadith ${_currentHadith!.number}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: primaryColor,
                  ),
                ),
              ),
            ),
            
            // Arabic text
            Card(
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: BorderSide(color: Colors.grey.shade200),
              ),
              child: Padding(
                padding: EdgeInsets.all(24),
                child: Text(
                  _currentHadith!.arabic,
                  style: GoogleFonts.amiri(
                    fontSize: _arabicFontSize,
                    height: 1.8,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.right,
                  textDirection: TextDirection.rtl,
                ),
              ),
            ),
            
            SizedBox(height: 24),
            
            // Translation
            if (_showTranslation)
              Card(
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(color: Colors.grey.shade200),
                ),
                child: Padding(
                  padding: EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (_currentHadith!.narrator.isNotEmpty) ...[
                        Text(
                          'Narrated by: ${_currentHadith!.narrator}',
                          style: GoogleFonts.poppins(
                            fontSize: _translationFontSize - 2,
                            fontWeight: FontWeight.w600,
                            fontStyle: FontStyle.italic,
                            color: Colors.grey[700],
                          ),
                        ),
                        SizedBox(height: 12),
                      ],
                      Text(
                        _currentHadith!.english,
                        style: GoogleFonts.poppins(
                          fontSize: _translationFontSize,
                          height: 1.7,
                          fontWeight: FontWeight.w400,
                          color: Colors.grey[800],
                        ),
                      ),
                      SizedBox(height: 16),
                      Text(
                        _currentHadith!.reference,
                        style: GoogleFonts.poppins(
                          fontSize: _translationFontSize - 2,
                          fontStyle: FontStyle.italic,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            
            SizedBox(height: 24),
            
            // Font size controls
            Card(
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: BorderSide(color: Colors.grey.shade200),
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildControlButton(
                      icon: FontAwesomeIcons.minus,
                      label: 'Smaller',
                      onTap: _decreaseFontSize,
                    ),
                    _buildControlButton(
                      icon: FontAwesomeIcons.plus,
                      label: 'Larger',
                      onTap: _increaseFontSize,
                    ),
                    _buildControlButton(
                      icon: FontAwesomeIcons.language,
                      label: _showTranslation ? 'Hide Translation' : 'Show Translation',
                      onTap: _toggleTranslation,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 16,
                color: primaryColor,
              ),
              SizedBox(height: 6),
              Text(
                label,
                style: GoogleFonts.poppins(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildBottomNavigationBar() {
    final bool isPrevDisabled = _currentHadithNumber <= 1;
    final bool isNextDisabled = _currentHadithNumber >= widget.collection.totalHadithCount;
    
    return Card(
      margin: EdgeInsets.zero,
      elevation: 4,
      child: Container(
        height: 72,
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Navigation buttons
            Row(
              children: [
                _buildNavigationButton(
                  icon: FontAwesomeIcons.angleDoubleLeft,
                  label: 'First',
                  onTap: isPrevDisabled ? null : () => _navigateToHadith(1),
                ),
                SizedBox(width: 16),
                _buildNavigationButton(
                  icon: FontAwesomeIcons.angleLeft,
                  label: 'Prev',
                  onTap: isPrevDisabled ? null : () => _navigateToHadith(_currentHadithNumber - 1),
                ),
                SizedBox(width: 16),
                _buildNavigationButton(
                  icon: FontAwesomeIcons.angleRight,
                  label: 'Next',
                  onTap: isNextDisabled ? null : () => _navigateToHadith(_currentHadithNumber + 1),
                ),
                SizedBox(width: 16),
                _buildNavigationButton(
                  icon: FontAwesomeIcons.angleDoubleRight,
                  label: 'Last',
                  onTap: isNextDisabled ? null : () => _navigateToHadith(widget.collection.totalHadithCount),
                ),
              ],
            ),
            
            // Action buttons
            Row(
              children: [
                IconButton(
                  icon: Icon(
                    FontAwesomeIcons.heart,
                    size: 20,
                    color: Colors.redAccent,
                  ),
                  onPressed: _toggleFavorite,
                ),
                IconButton(
                  icon: Icon(
                    FontAwesomeIcons.shareNodes,
                    size: 20,
                    color: primaryColor,
                  ),
                  onPressed: _shareHadith,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildNavigationButton({
    required IconData icon,
    required String label,
    required VoidCallback? onTap,
  }) {
    final bool isDisabled = onTap == null;
    
    return Tooltip(
      message: label,
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: onTap,
        child: Opacity(
          opacity: isDisabled ? 0.4 : 1.0,
          child: Container(
            padding: EdgeInsets.all(8),
            child: Icon(
              icon,
              size: 20,
              color: isDisabled ? Colors.grey[400] : primaryColor,
            ),
          ),
        ),
      ),
    );
  }
  
  void _showOptionsBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Text(
              'Options',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16),
            _buildOptionTile(
              icon: FontAwesomeIcons.copy,
              title: 'Copy Text',
              onTap: () {
                Navigator.pop(context);
                _copyHadith();
              },
            ),
            _buildOptionTile(
              icon: FontAwesomeIcons.shareNodes,
              title: 'Share Hadith',
              onTap: () {
                Navigator.pop(context);
                _shareHadith();
              },
            ),
            _buildOptionTile(
              icon: FontAwesomeIcons.heart,
              title: 'Add to Favorites',
              onTap: () {
                Navigator.pop(context);
                _toggleFavorite();
              },
            ),
            _buildOptionTile(
              icon: FontAwesomeIcons.list,
              title: 'Jump to Hadith',
              onTap: () {
                Navigator.pop(context);
                _showJumpDialog();
              },
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        size: 18,
        color: primaryColor,
      ),
      title: Text(
        title,
        style: GoogleFonts.poppins(
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }
} 