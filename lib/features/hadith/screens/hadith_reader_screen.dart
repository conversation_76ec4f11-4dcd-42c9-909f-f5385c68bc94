import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/features/hadith/models/hadith_models.dart';
import 'package:new_islamic_app_uk/features/hadith/services/hadith_service.dart';
import 'package:new_islamic_app_uk/loading_indicator.dart';
import 'package:share_plus/share_plus.dart';
import '../../../theme/app_colors.dart';
import '../../../theme/app_text_styles.dart';

class HadithReaderScreen extends StatefulWidget {
  final HadithCollection collection;
  final int initialHadithNumber;

  const HadithReaderScreen({
    Key? key,
    required this.collection,
    required this.initialHadithNumber,
  }) : super(key: key);

  @override
  State<HadithReaderScreen> createState() => _HadithReaderScreenState();
}

class _HadithReaderScreenState extends State<HadithReaderScreen> {
  final HadithService _hadithService = HadithService();
  bool _isLoading = true;
  int _currentHadithNumber = 0;
  Hadith? _currentHadith;
  final double _arabicFontSize = 22;

  @override
  void initState() {
    super.initState();
    _currentHadithNumber = widget.initialHadithNumber;
    _loadHadith();
  }

  Future<void> _loadHadith() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final hadith = await _hadithService.getHadith(
        widget.collection.name,
        _currentHadithNumber,
      );

      if (hadith != null) {
        // Additional cleaning at UI level for safety
        final cleanedHadith = _cleanHadithText(hadith);

        setState(() {
          _currentHadith = cleanedHadith;
          _isLoading = false;
        });

        // Save reading progress
        _hadithService.saveReadingProgress(
          widget.collection.name,
          _currentHadithNumber,
        );
      } else {
        // If hadith not found, try to navigate to the first hadith
        if (_currentHadithNumber != 1) {
          _currentHadithNumber = 1;
          _loadHadith();
        } else {
          // If first hadith also not found, show error
          setState(() {
            _isLoading = false;
          });
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to load hadith'),
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        }
      }
    } catch (e) {
      print('Error loading hadith: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading hadith: $e'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  // Additional layer of cleaning at the UI level
  Hadith _cleanHadithText(Hadith hadith) {
    String cleanArabic = hadith.arabic;
    String cleanEnglish = hadith.english;
    String cleanChapterTitle = hadith.chapterTitle;

    // Clean tooltip tags
    cleanArabic = cleanArabic.replaceAll(RegExp(r'="\w+"'), '');
    cleanArabic = cleanArabic.replaceAll(RegExp(r'="tooltip[^"]*"'), '');

    // Clean narrator ID tags
    cleanArabic = cleanArabic.replaceAll(RegExp(r'narrator id="[^"]*"'), '');

    // Remove HTML-like tags
    cleanArabic = cleanArabic.replaceAll(RegExp(r'<[^>]*>'), '');

    // Remove square brackets
    cleanArabic = cleanArabic.replaceAll('[', '').replaceAll(']', '');

    // Fix common formatting issues
    cleanArabic = cleanArabic.replaceAll('p>', '');
    cleanArabic = cleanArabic.replaceAll('prematn', '');
    cleanArabic = cleanArabic.replaceAll('matn', '');
    cleanArabic = cleanArabic.replaceAll('//', '');
    cleanArabic = cleanArabic.replaceAll('narrator', '');
    cleanArabic = cleanArabic.replaceAll('tooltip', '');

    // Fix double spaces and trim
    cleanArabic = cleanArabic.replaceAll(RegExp(r'\s+'), ' ').trim();

    // Apply similar cleaning to English text
    cleanEnglish = cleanEnglish.replaceAll(RegExp(r'<[^>]*>'), '');
    cleanEnglish = cleanEnglish.replaceAll(RegExp(r'\s+'), ' ').trim();

    // Clean chapter title
    cleanChapterTitle = cleanChapterTitle.replaceAll(RegExp(r'<[^>]*>'), '');
    cleanChapterTitle = cleanChapterTitle.replaceAll(RegExp(r'\s+'), ' ').trim();

    return hadith.copyWith(
      arabic: cleanArabic,
      english: cleanEnglish,
      chapterTitle: cleanChapterTitle,
    );
  }

  void _navigateToHadith(int number) {
    if (number < 1 || number > widget.collection.totalHadithCount) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Hadith number out of range'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    setState(() {
      _currentHadithNumber = number;
    });

    _loadHadith();
  }

  Future<void> _shareHadith() async {
    if (_currentHadith == null) return;

    final text = '''
${_currentHadith!.arabic}

${_currentHadith!.english}

${_currentHadith!.reference}
Source: ${widget.collection.title}
''';

    await Share.share(text);
  }

  Future<void> _copyHadith() async {
    if (_currentHadith == null) return;

    final text = '''
${_currentHadith!.arabic}

${_currentHadith!.english}

${_currentHadith!.reference}
''';

    await Clipboard.setData(ClipboardData(text: text));

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Copied to clipboard'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _toggleFavorite() {
    // TODO: Implement favorite functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Added to favorites'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }



  void _showJumpDialog() {
    final TextEditingController controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Jump to Hadith'),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          decoration: InputDecoration(
            hintText: 'Enter hadith number (1-${widget.collection.totalHadithCount})',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              final number = int.tryParse(controller.text);
              if (number != null) {
                _navigateToHadith(number);
              }
            },
            child: Text('Go'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Hadith Reader',
          style: AppTextStyles.headlineMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(FontAwesomeIcons.magnifyingGlass,
                 size: 20, color: AppColors.textPrimary),
            onPressed: _showJumpDialog,
          ),
          IconButton(
            icon: const Icon(FontAwesomeIcons.ellipsisVertical,
                 size: 20, color: AppColors.textPrimary),
            onPressed: () => _showOptionsBottomSheet(context),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _currentHadith == null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        FontAwesomeIcons.circleExclamation,
                        size: 40,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Hadith not found',
                        style: AppTextStyles.titleLarge.copyWith(
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: () => _navigateToHadith(1),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text('Go to first hadith'),
                      ),
                    ],
                  ),
                )
              : _buildHadithContent(),
      bottomNavigationBar: _isLoading || _currentHadith == null
          ? null
          : _buildBottomNavigationBar(),
    );
  }

  Widget _buildHadithContent() {
    if (_currentHadith == null) return const SizedBox.shrink();

    return RefreshIndicator(
      onRefresh: _loadHadith,
      color: AppColors.primary,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Collection Title
            Text(
              widget.collection.title,
              style: AppTextStyles.headlineSmall.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(height: 16.0),

            // Hadith number
            Text(
              'Hadith ${_currentHadith!.number}',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24.0),

            // Chapter title if available
            if (_currentHadith!.chapterTitle.isNotEmpty) ...[
              Text(
                _currentHadith!.chapterTitle,
                style: AppTextStyles.titleLarge.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 16.0),
            ],

            // Narrator
            if (_currentHadith!.narrator.isNotEmpty) ...[
              Text(
                'Narrated by ${_currentHadith!.narrator}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 24.0),
            ],

            // Translation Section
            Text(
              'Translation',
              style: AppTextStyles.titleLarge.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12.0),
            Text(
              _currentHadith!.english,
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.textPrimary,
                height: 1.6,
              ),
            ),
            const SizedBox(height: 24.0),

            // Arabic text
            if (_currentHadith!.arabic.isNotEmpty) ...[
              Text(
                'Arabic',
                style: AppTextStyles.titleLarge.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12.0),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20.0),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(16.0),
                  border: Border.all(
                    color: AppColors.surfaceVariant,
                    width: 1,
                  ),
                ),
                child: Text(
                  _currentHadith!.arabic,
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontSize: _arabicFontSize,
                    height: 1.8,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.right,
                  textDirection: TextDirection.rtl,
                ),
              ),
              const SizedBox(height: 24.0),
            ],

            // Reference Section
            Text(
              'Reference',
              style: AppTextStyles.titleLarge.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12.0),
            Text(
              _currentHadith!.reference,
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.textPrimary,
                height: 1.6,
              ),
            ),
            const SizedBox(height: 40.0),
          ],
        ),
      ),
    );
  }



  Widget _buildBottomNavigationBar() {
    final bool isPrevDisabled = _currentHadithNumber <= 1;
    final bool isNextDisabled = _currentHadithNumber >= widget.collection.totalHadithCount;

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: BorderSide(
            color: AppColors.surfaceVariant,
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Navigation buttons
          Row(
            children: [
              _buildNavigationButton(
                icon: FontAwesomeIcons.anglesLeft,
                label: 'First',
                onTap: isPrevDisabled ? null : () => _navigateToHadith(1),
              ),
              const SizedBox(width: 16),
              _buildNavigationButton(
                icon: FontAwesomeIcons.angleLeft,
                label: 'Prev',
                onTap: isPrevDisabled ? null : () => _navigateToHadith(_currentHadithNumber - 1),
              ),
              const SizedBox(width: 16),
              _buildNavigationButton(
                icon: FontAwesomeIcons.angleRight,
                label: 'Next',
                onTap: isNextDisabled ? null : () => _navigateToHadith(_currentHadithNumber + 1),
              ),
              const SizedBox(width: 16),
              _buildNavigationButton(
                icon: FontAwesomeIcons.anglesRight,
                label: 'Last',
                onTap: isNextDisabled ? null : () => _navigateToHadith(widget.collection.totalHadithCount),
              ),
            ],
          ),

          // Action buttons
          Row(
            children: [
              IconButton(
                icon: const Icon(
                  FontAwesomeIcons.heart,
                  size: 20,
                  color: Colors.redAccent,
                ),
                onPressed: _toggleFavorite,
              ),
              IconButton(
                icon: const Icon(
                  FontAwesomeIcons.shareNodes,
                  size: 20,
                  color: AppColors.primary,
                ),
                onPressed: _shareHadith,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButton({
    required IconData icon,
    required String label,
    required VoidCallback? onTap,
  }) {
    final bool isDisabled = onTap == null;

    return Tooltip(
      message: label,
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: onTap,
        child: Opacity(
          opacity: isDisabled ? 0.4 : 1.0,
          child: Container(
            padding: const EdgeInsets.all(8),
            child: Icon(
              icon,
              size: 20,
              color: isDisabled ? AppColors.textSecondary : AppColors.primary,
            ),
          ),
        ),
      ),
    );
  }

  void _showOptionsBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: AppColors.surfaceVariant,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Text(
              'Options',
              style: AppTextStyles.titleLarge.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            _buildOptionTile(
              icon: FontAwesomeIcons.copy,
              title: 'Copy Text',
              onTap: () {
                Navigator.pop(context);
                _copyHadith();
              },
            ),
            _buildOptionTile(
              icon: FontAwesomeIcons.shareNodes,
              title: 'Share Hadith',
              onTap: () {
                Navigator.pop(context);
                _shareHadith();
              },
            ),
            _buildOptionTile(
              icon: FontAwesomeIcons.heart,
              title: 'Add to Favorites',
              onTap: () {
                Navigator.pop(context);
                _toggleFavorite();
              },
            ),
            _buildOptionTile(
              icon: FontAwesomeIcons.list,
              title: 'Jump to Hadith',
              onTap: () {
                Navigator.pop(context);
                _showJumpDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        size: 18,
        color: AppColors.primary,
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }
}