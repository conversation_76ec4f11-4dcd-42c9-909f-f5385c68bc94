import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/hadith/models/hadith_models.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_reader_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/services/hadith_service.dart';
import 'package:new_islamic_app_uk/loading_indicator.dart';
import 'package:share_plus/share_plus.dart';

class FavoriteHadithsScreen extends StatefulWidget {
  const FavoriteHadithsScreen({Key? key}) : super(key: key);

  @override
  State<FavoriteHadithsScreen> createState() => _FavoriteHadithsScreenState();
}

class _FavoriteHadithsScreenState extends State<FavoriteHadithsScreen> {
  bool _isLoading = true;
  List<Hadith> _favoriteHadiths = [];
  final Color primaryColor = Color(URLs().colorScheme);
  final HadithService _hadithService = HadithService();

  @override
  void initState() {
    super.initState();
    _loadFavoriteHadiths();
  }

  Future<void> _loadFavoriteHadiths() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // For demo purposes - in a real app these would be loaded from a database or shared preferences
      await Future.delayed(Duration(seconds: 1));
      
      // Example data for demonstration
      final favorites = [
        Hadith(
          id: 'bukhari_1',
          number: 1,
          arabic: 'إِنَّمَا الأَعْمَالُ بِالنِّيَّاتِ، وَإِنَّمَا لِكُلِّ امْرِئٍ مَا نَوَى...',
          english: 'The rewards of deeds depend upon the intentions, and every person will be rewarded according to what they intended...',
          reference: 'Sahih al-Bukhari 1',
          narrator: 'Umar ibn Al-Khattab',
          chapterTitle: 'How the Divine Revelation started',
          chapterNumber: 1,
          isFavorite: true,
        ),
        Hadith(
          id: 'muslim_25',
          number: 25,
          arabic: 'اَلْمُسْلِمُ مَنْ سَلِمَ الْمُسْلِمُوْنَ مِنْ لِسَانِهِ وَيَدِهِ...',
          english: 'A Muslim is the one from whose tongue and hands the Muslims are safe...',
          reference: 'Sahih Muslim 25',
          narrator: 'Abdullah ibn Amr',
          chapterTitle: 'Description of a Muslim',
          chapterNumber: 12,
          isFavorite: true,
        ),
      ];
      
      setState(() {
        _favoriteHadiths = favorites;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading favorite hadiths: $e');
      setState(() {
        _isLoading = false;
      });
      
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load favorite hadiths. Please try again.'),
          behavior: SnackBarBehavior.floating,
          action: SnackBarAction(
            label: 'Retry',
            onPressed: _loadFavoriteHadiths,
          ),
        ),
      );
    }
  }

  void _navigateToHadith(Hadith hadith) {
    // Get collection from collection ID
    final collectionId = hadith.id.split('_')[0];
    final collections = _hadithService.getAllCollections();
    final collection = collections.firstWhere(
      (c) => c.name == collectionId,
      orElse: () => throw Exception('Collection not found'),
    );
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HadithReaderScreen(
          collection: collection,
          initialHadithNumber: hadith.number,
        ),
      ),
    );
  }

  Future<void> _removeFromFavorites(Hadith hadith) async {
    // For demo purposes - in a real app this would update a database or shared preferences
    setState(() {
      _favoriteHadiths.remove(hadith);
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Hadith removed from favorites'),
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'Undo',
          onPressed: () {
            setState(() {
              _favoriteHadiths.add(hadith);
            });
          },
        ),
      ),
    );
  }

  Future<void> _shareHadith(Hadith hadith) async {
    final text = '''
${_getCollectionTitle(hadith.id)}
${hadith.reference}

${hadith.arabic}

${hadith.english}

Shared from Islamic App UK
''';
    
    await Share.share(text);
  }

  String _getCollectionTitle(String id) {
    final collectionId = id.split('_')[0];
    final collections = {
      'bukhari': 'Sahih al-Bukhari',
      'muslim': 'Sahih Muslim',
      'abudawud': 'Sunan Abu Dawud',
      'tirmidhi': 'Jami at-Tirmidhi',
      'nasai': 'Sunan an-Nasa\'i',
      'ibnmajah': 'Sunan Ibn Majah',
      'malik': 'Muwatta Malik',
      'riyadussalihin': 'Riyad as-Salihin',
      'nawawi40': 'An-Nawawi\'s 40 Hadith',
    };
    
    return collections[collectionId] ?? collectionId;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        elevation: 0,
        backgroundColor: primaryColor,
        title: Text(
          'Favorite Hadiths',
          style: GoogleFonts.poppins(
            fontSize: 18, 
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(FontAwesomeIcons.shareNodes, size: 20, color: Colors.white),
            onPressed: _favoriteHadiths.isEmpty 
                ? null 
                : () {
                    final text = _favoriteHadiths.map((h) => 
                      '${h.reference}\n${h.english}\n\n').join('');
                    Share.share('My Favorite Hadiths:\n\n$text\nShared from Islamic App UK');
                  },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadFavoriteHadiths,
        color: primaryColor,
        child: _isLoading
            ? Center(child: LoadingIndicator())
            : _favoriteHadiths.isEmpty
                ? _buildEmptyState()
                : _buildFavoritesList(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FontAwesomeIcons.heart,
            size: 64,
            color: Colors.grey[300],
          ),
          SizedBox(height: 24),
          Text(
            'No Favorite Hadiths',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'Your favorite hadiths will appear here. Browse hadiths and mark them as favorites to add them to this list.',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: Icon(FontAwesomeIcons.bookOpen, size: 16),
            label: Text('Browse Hadiths'),
            style: ElevatedButton.styleFrom(
              backgroundColor: primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoritesList() {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: _favoriteHadiths.length,
      itemBuilder: (context, index) {
        final hadith = _favoriteHadiths[index];
        return Dismissible(
          key: Key(hadith.id),
          direction: DismissDirection.endToStart,
          background: Container(
            alignment: Alignment.centerRight,
            padding: EdgeInsets.only(right: 20),
            decoration: BoxDecoration(
              color: Colors.red[400],
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              FontAwesomeIcons.trashCan,
              color: Colors.white,
            ),
          ),
          onDismissed: (direction) {
            _removeFromFavorites(hadith);
          },
          child: Card(
            margin: EdgeInsets.only(bottom: 16),
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(color: Colors.grey.shade200),
            ),
            child: InkWell(
              borderRadius: BorderRadius.circular(16),
              onTap: () => _navigateToHadith(hadith),
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Collection and reference
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            color: primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            _getCollectionTitle(hadith.id),
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: primaryColor,
                            ),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text(
                          '• ${hadith.reference}',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        Spacer(),
                        IconButton(
                          icon: Icon(
                            FontAwesomeIcons.shareNodes,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          onPressed: () => _shareHadith(hadith),
                          constraints: BoxConstraints(),
                          padding: EdgeInsets.all(8),
                          splashRadius: 24,
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    
                    // Arabic text preview
                    Text(
                      hadith.arabic,
                      style: GoogleFonts.amiri(
                        fontSize: 18,
                        height: 1.5,
                      ),
                      textDirection: TextDirection.rtl,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 16),
                    
                    // English text preview
                    Text(
                      hadith.english,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        height: 1.5,
                        color: Colors.grey[800],
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    SizedBox(height: 12),
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton.icon(
                        onPressed: () => _navigateToHadith(hadith),
                        icon: Text(
                          'Read Full',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: primaryColor,
                          ),
                        ),
                        label: Icon(
                          FontAwesomeIcons.angleRight,
                          size: 14,
                          color: primaryColor,
                        ),
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
} 