import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../theme/app_theme.dart';
import '../../../widgets/common/modern_card.dart';
import '../../../widgets/common/modern_search_bar.dart';

class ModernHadithHomeScreen extends StatefulWidget {
  const ModernHadithHomeScreen({Key? key}) : super(key: key);

  @override
  State<ModernHadithHomeScreen> createState() => _ModernHadithHomeScreenState();
}

class _ModernHadithHomeScreenState extends State<ModernHadithHomeScreen> {
  final List<Map<String, dynamic>> _categories = [
    {
      'name': 'Faith',
      'icon': FontAwesomeIcons.heart,
      'color': Color(0xFFE91E63),
      'count': 45,
    },
    {
      'name': 'Prayer',
      'icon': FontAwesomeIcons.handsPraying,
      'color': Color(0xFF4CAF50),
      'count': 38,
    },
    {
      'name': 'Charity',
      'icon': FontAwesomeIcons.handHoldingHeart,
      'color': Color(0xFF2196F3),
      'count': 22,
    },
    {
      'name': 'Fasting',
      'icon': FontAwesomeIcons.moon,
      'color': Color(0xFF9C27B0),
      'count': 18,
    },
    {
      'name': 'Pilgrimage',
      'icon': FontAwesomeIcons.kaaba,
      'color': Color(0xFF607D8B),
      'count': 15,
    },
    {
      'name': 'Knowledge',
      'icon': FontAwesomeIcons.graduationCap,
      'color': Color(0xFFFF9800),
      'count': 32,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search bar
            ModernSearchBar(
              hintText: 'Search Hadith',
              margin: const EdgeInsets.only(bottom: 24),
            ),
            
            // Daily Hadith
            _buildDailyHadith(),
            
            const SizedBox(height: 32),
            
            // Categories
            Text(
              'Categories',
              style: AppTheme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 16),
            
            GridView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.2,
              ),
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                return CategoryCard(
                  title: category['name'],
                  subtitle: '${category['count']} hadiths',
                  icon: category['icon'],
                  iconColor: category['color'],
                  backgroundColor: Colors.white,
                  margin: EdgeInsets.zero,
                  onTap: () => _navigateToCategory(category),
                );
              },
            ),
            
            const SizedBox(height: 100),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(
          Icons.arrow_back_ios,
          color: AppTheme.textPrimary,
        ),
      ),
      title: Text(
        'Hadith',
        style: AppTheme.textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        IconButton(
          onPressed: () {
            // Search functionality
          },
          icon: const Icon(
            Icons.search,
            color: AppTheme.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildDailyHadith() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Daily Hadith',
          style: AppTheme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 16),
        
        GradientCard(
          gradient: AppTheme.tealGradient,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      FontAwesomeIcons.mosque,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Hadith of the Day',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '"The best of you are those who have the best character."',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                        height: 1.6,
                      ),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    Text(
                      'Sahih Bukhari',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.share_outlined,
                            color: Colors.white,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Share',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.bookmark_outline,
                            color: Colors.white,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Save',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _navigateToCategory(Map<String, dynamic> category) {
    // Navigate to hadith category screen
    print('Navigate to hadith category: ${category['name']}');
  }
}
