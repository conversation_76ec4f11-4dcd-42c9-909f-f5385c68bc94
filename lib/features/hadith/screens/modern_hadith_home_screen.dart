import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../theme/app_colors.dart';
import '../../../theme/app_text_styles.dart';

class ModernHadithHomeScreen extends StatefulWidget {
  const ModernHadithHomeScreen({super.key});

  @override
  State<ModernHadithHomeScreen> createState() => _ModernHadithHomeScreenState();
}

class _ModernHadithHomeScreenState extends State<ModernHadithHomeScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  final List<Map<String, dynamic>> _hadithCollections = [
    {
      'name': '<PERSON><PERSON><PERSON> <PERSON><PERSON>',
      'description': 'The most authentic collection of hadith',
      'count': '7563 hadiths',
      'icon': FontAwesomeIcons.book,
    },
    {
      'name': '<PERSON><PERSON><PERSON>',
      'description': 'Second most authentic collection',
      'count': '7190 hadiths',
      'icon': FontAwesomeIcons.bookOpen,
    },
    {
      'name': '<PERSON><PERSON>',
      'description': 'Collection focusing on legal matters',
      'count': '5274 hadiths',
      'icon': FontAwesomeIcons.scroll,
    },
    {
      'name': 'Jami\' at-Tirmidhi',
      'description': 'Comprehensive hadith collection',
      'count': '3956 hadiths',
      'icon': FontAwesomeIcons.bookBookmark,
    },
    {
      'name': 'Sunan an-Nasa\'i',
      'description': 'Collection of authentic traditions',
      'count': '5761 hadiths',
      'icon': FontAwesomeIcons.fileLines,
    },
  ];

  final List<Map<String, dynamic>> _dailyHadiths = [
    {
      'text': 'The best of people are those who benefit others.',
      'narrator': 'Prophet Muhammad (PBUH)',
      'source': 'Sahih al-Bukhari',
      'isFavorite': false,
    },
    {
      'text': 'Kindness is a mark of faith, and whoever is not kind has no faith.',
      'narrator': 'Prophet Muhammad (PBUH)',
      'source': 'Sahih Muslim',
      'isFavorite': true,
    },
    {
      'text': 'The believer is not one who eats his fill while his neighbor goes hungry.',
      'narrator': 'Prophet Muhammad (PBUH)',
      'source': 'Sunan Abu Dawood',
      'isFavorite': false,
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: CustomScrollView(
        slivers: [
          // Modern App Bar
          SliverAppBar(
            expandedHeight: 200.0,
            floating: false,
            pinned: true,
            backgroundColor: AppColors.primary,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () => Navigator.pop(context),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.search, color: Colors.white),
                onPressed: () {
                  // Search functionality
                },
              ),
              IconButton(
                icon: const Icon(Icons.bookmark, color: Colors.white),
                onPressed: () {
                  // Bookmarks
                },
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                'Hadith Collections',
                style: AppTextStyles.headlineMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primary,
                      AppColors.primary.withOpacity(0.8),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      right: -50,
                      top: 50,
                      child: Icon(
                        FontAwesomeIcons.scroll,
                        size: 120,
                        color: Colors.white.withOpacity(0.1),
                      ),
                    ),
                    Positioned(
                      left: 20,
                      bottom: 80,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Authentic Sayings',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Colors.white.withOpacity(0.9),
                            ),
                          ),
                          Text(
                            'of Prophet Muhammad (ﷺ)',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Colors.white.withOpacity(0.9),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Tab Bar
          SliverPersistentHeader(
            pinned: true,
            delegate: _SliverTabBarDelegate(
              TabBar(
                controller: _tabController,
                indicatorColor: AppColors.primary,
                indicatorWeight: 3.0,
                labelColor: AppColors.primary,
                unselectedLabelColor: AppColors.textSecondary,
                labelStyle: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                unselectedLabelStyle: AppTextStyles.titleMedium,
                tabs: const [
                  Tab(text: 'Collections'),
                  Tab(text: 'Daily'),
                  Tab(text: 'Favorites'),
                ],
              ),
            ),
          ),

          // Tab Content
          SliverFillRemaining(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildCollectionsList(),
                _buildDailyHadiths(),
                _buildFavoriteHadiths(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCollectionsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(20.0),
      itemCount: _hadithCollections.length,
      itemBuilder: (context, index) {
        final collection = _hadithCollections[index];
        final colors = AppColors.categoryColors[index % AppColors.categoryColors.length];

        return Container(
          margin: const EdgeInsets.only(bottom: 16.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.0),
            boxShadow: [
              BoxShadow(
                color: colors.withOpacity(0.2),
                blurRadius: 12.0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20.0),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    colors.withOpacity(0.1),
                    colors.withOpacity(0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    // Navigate to collection
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Row(
                      children: [
                        // Collection Icon with gradient background
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [colors, colors.withOpacity(0.7)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(16.0),
                            boxShadow: [
                              BoxShadow(
                                color: colors.withOpacity(0.3),
                                blurRadius: 8.0,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            collection['icon'],
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: 16.0),

                        // Collection Info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                collection['name'],
                                style: AppTextStyles.titleLarge.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                              const SizedBox(height: 6.0),
                              Text(
                                collection['description'],
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              const SizedBox(height: 8.0),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12.0,
                                  vertical: 4.0,
                                ),
                                decoration: BoxDecoration(
                                  color: colors.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12.0),
                                  border: Border.all(
                                    color: colors.withOpacity(0.3),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  collection['count'],
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: colors,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Arrow Icon
                        Container(
                          padding: const EdgeInsets.all(8.0),
                          decoration: BoxDecoration(
                            color: colors.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: Icon(
                            Icons.arrow_forward_ios,
                            color: colors,
                            size: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDailyHadiths() {
    return ListView.builder(
      padding: const EdgeInsets.all(20.0),
      itemCount: _dailyHadiths.length,
      itemBuilder: (context, index) {
        final hadith = _dailyHadiths[index];
        final gradientColors = [
          AppColors.primary.withOpacity(0.1),
          AppColors.secondary.withOpacity(0.1),
          AppColors.accent.withOpacity(0.1),
        ];
        final currentGradient = gradientColors[index % gradientColors.length];

        return Container(
          margin: const EdgeInsets.only(bottom: 24.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.0),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withOpacity(0.1),
                blurRadius: 15.0,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20.0),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    currentGradient,
                    Colors.white,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with badge and actions
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12.0,
                            vertical: 6.0,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [AppColors.primary, AppColors.primary.withOpacity(0.8)],
                            ),
                            borderRadius: BorderRadius.circular(20.0),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                FontAwesomeIcons.star,
                                color: Colors.white,
                                size: 12,
                              ),
                              const SizedBox(width: 6.0),
                              Text(
                                'Daily Hadith',
                                style: AppTextStyles.labelSmall.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              hadith['isFavorite'] = !hadith['isFavorite'];
                            });
                          },
                          icon: Container(
                            padding: const EdgeInsets.all(8.0),
                            decoration: BoxDecoration(
                              color: hadith['isFavorite']
                                  ? Colors.red.withOpacity(0.1)
                                  : AppColors.surfaceVariant,
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            child: Icon(
                              hadith['isFavorite']
                                  ? Icons.favorite
                                  : Icons.favorite_border,
                              color: hadith['isFavorite']
                                  ? Colors.red
                                  : AppColors.textTertiary,
                              size: 18,
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            // Share hadith
                          },
                          icon: Container(
                            padding: const EdgeInsets.all(8.0),
                            decoration: BoxDecoration(
                              color: AppColors.surfaceVariant,
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            child: const Icon(
                              Icons.share,
                              color: AppColors.textTertiary,
                              size: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20.0),

                    // Quote Icon
                    Container(
                      padding: const EdgeInsets.all(12.0),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: Icon(
                        FontAwesomeIcons.quoteLeft,
                        color: AppColors.primary,
                        size: 20,
                      ),
                    ),
                    const SizedBox(height: 16.0),

                    // Hadith Text
                    Text(
                      hadith['text'],
                      style: AppTextStyles.titleMedium.copyWith(
                        color: AppColors.textPrimary,
                        height: 1.6,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 20.0),

                    // Attribution Section
                    Container(
                      padding: const EdgeInsets.all(16.0),
                      decoration: BoxDecoration(
                        color: AppColors.surfaceVariant,
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 4,
                                height: 24,
                                decoration: BoxDecoration(
                                  color: AppColors.primary,
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),
                              const SizedBox(width: 12.0),
                              Expanded(
                                child: Text(
                                  hadith['narrator'],
                                  style: AppTextStyles.bodyMedium.copyWith(
                                    color: AppColors.textPrimary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8.0),
                          Row(
                            children: [
                              Icon(
                                FontAwesomeIcons.book,
                                size: 14,
                                color: AppColors.textSecondary,
                              ),
                              const SizedBox(width: 8.0),
                              Text(
                                hadith['source'],
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFavoriteHadiths() {
    final favoriteHadiths = _dailyHadiths.where((h) => h['isFavorite'] == true).toList();

    if (favoriteHadiths.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FontAwesomeIcons.heart,
              size: 48,
              color: AppColors.textTertiary,
            ),
            const SizedBox(height: 16.0),
            Text(
              'No Favorite Hadiths',
              style: AppTextStyles.headlineSmall.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8.0),
            Text(
              'Mark hadiths as favorites to see them here',
              textAlign: TextAlign.center,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20.0),
      itemCount: favoriteHadiths.length,
      itemBuilder: (context, index) {
        final hadith = favoriteHadiths[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 16.0),
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: AppColors.cardBackground,
            borderRadius: BorderRadius.circular(12.0),
            boxShadow: [
              BoxShadow(
                color: AppColors.cardShadow,
                blurRadius: 4.0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '"${hadith['text']}"',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textPrimary,
                  fontStyle: FontStyle.italic,
                ),
              ),
              const SizedBox(height: 8.0),
              Text(
                '- ${hadith['narrator']}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _SliverTabBarDelegate(this.tabBar);

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: AppColors.background,
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverTabBarDelegate oldDelegate) {
    return false;
  }
}
