import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/features/hadith/models/hadith_models.dart';
import '../../../theme/app_colors.dart';
import '../../../theme/app_text_styles.dart';

class HadithCollectionCard extends StatelessWidget {
  final HadithCollection collection;
  final VoidCallback onTap;
  final int colorIndex;

  const HadithCollectionCard({
    Key? key,
    required this.collection,
    required this.onTap,
    required this.colorIndex,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20.0),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(16.0),
          border: Border.all(
            color: AppColors.surfaceVariant,
            width: 1,
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Collection Number
            Text(
              'Collection ${colorIndex + 1}',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 16.0),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    collection.title,
                    style: AppTextStyles.titleMedium.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8.0),

                  // Hadith count
                  Text(
                    '${collection.totalHadithCount} hadiths',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),

            // Islamic Icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: const Color(0xFF4A6741), // Dark green from image
                borderRadius: BorderRadius.circular(12.0),
              ),
              child: const Icon(
                FontAwesomeIcons.mosque,
                color: Color(0xFFD4AF37), // Gold color
                size: 24,
              ),
            ),
          ],
        ),
      ),
    );
  }
}