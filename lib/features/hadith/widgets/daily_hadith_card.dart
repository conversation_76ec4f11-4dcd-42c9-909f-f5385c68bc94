import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/hadith/models/hadith_models.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_reader_screen.dart';
import 'package:share_plus/share_plus.dart';
import '../../../theme/app_colors.dart';
import '../../../theme/app_text_styles.dart';

class DailyHadithCard extends StatelessWidget {
  final DailyHadith dailyHadith;

  const DailyHadithCard({Key? key, required this.dailyHadith}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Color primaryColor = Color(URLs().colorScheme);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 15,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Modern gradient header with Islamic pattern
          Container(
            height: 90,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  primaryColor,
                  primaryColor.withOpacity(0.8),
                ],
              ),
            ),
            child: Stack(
              children: [
                // Pattern overlay
                Opacity(
                  opacity: 0.1,
                  child: Container(
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage('assets/bg.jpeg'),
                        fit: BoxFit.cover,
                        colorFilter: ColorFilter.mode(
                          Colors.white.withOpacity(0.3),
                          BlendMode.overlay,
                        ),
                      ),
                    ),
                  ),
                ),
                // Header content
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Left decoration quote icon
                      Container(
                        width: 44,
                        height: 44,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.15),
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Icon(
                            FontAwesomeIcons.quoteLeft,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                      SizedBox(width: 14),
                      // Title and date
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Hadith of the Day',
                              style: GoogleFonts.poppins(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            SizedBox(height: 2),
                            Text(
                              _getFormattedDate(),
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.white.withOpacity(0.9),
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Decorative star
                      Transform.rotate(
                        angle: 0.1,
                        child: Icon(
                          FontAwesomeIcons.solidStar,
                          size: 18,
                          color: Colors.amber,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Hadith content with improved typography
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Collection badge
                Container(
                  margin: EdgeInsets.only(bottom: 16),
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: primaryColor.withOpacity(0.08),
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(
                      color: primaryColor.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    _getCollectionDisplayName(dailyHadith.collectionName),
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: primaryColor,
                    ),
                  ),
                ),

                // Arabic Text with more elegant styling
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.grey[100]!,
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        dailyHadith.hadith.arabic,
                        style: GoogleFonts.amiri(
                          fontSize: 20,
                          height: 1.7,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.right,
                        textDirection: TextDirection.rtl,
                        maxLines: 4,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (dailyHadith.hadith.arabic.length > 200)
                        Align(
                          alignment: Alignment.bottomLeft,
                          child: Container(
                            margin: EdgeInsets.only(top: 8),
                            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              '... المزيد',
                              style: GoogleFonts.amiri(
                                fontSize: 12,
                                color: Colors.grey[700],
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                SizedBox(height: 16),

                // English Text with better readability
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Text with custom quote indicator
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          margin: EdgeInsets.only(top: 4, right: 10),
                          width: 4,
                          height: 48,
                          decoration: BoxDecoration(
                            color: primaryColor.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            dailyHadith.hadith.english,
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              height: 1.6,
                              color: Colors.grey[800],
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 12),

                    // Reference information
                    Row(
                      children: [
                        Icon(
                          FontAwesomeIcons.circleInfo,
                          size: 12,
                          color: Colors.grey[500],
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            dailyHadith.hadith.reference,
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontStyle: FontStyle.italic,
                              color: Colors.grey[600],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),

                    if (dailyHadith.hadith.narrator.isNotEmpty) ...[
                      SizedBox(height: 6),
                      Row(
                        children: [
                          Icon(
                            FontAwesomeIcons.userPen,
                            size: 12,
                            color: Colors.grey[500],
                          ),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Narrator: ${dailyHadith.hadith.narrator}',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // Modern action buttons
          Container(
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
              border: Border(
                top: BorderSide(
                  color: Colors.grey[200]!,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                // Share button
                _buildActionButton(
                  icon: FontAwesomeIcons.shareNodes,
                  label: 'Share',
                  color: Colors.blue[600]!,
                  onPressed: () => _shareHadith(context),
                ),
                SizedBox(width: 12),
                // Read full button
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _readFull(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                    icon: Icon(
                      FontAwesomeIcons.bookOpen,
                      size: 14,
                    ),
                    label: Text(
                      'Read Full Hadith',
                      style: GoogleFonts.poppins(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper widget for action buttons
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return TextButton.icon(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        foregroundColor: color,
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: color.withOpacity(0.2)),
        ),
        backgroundColor: color.withOpacity(0.05),
      ),
      icon: Icon(icon, size: 14),
      label: Text(
        label,
        style: GoogleFonts.poppins(
          fontSize: 13,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  // Get current date in formatted string
  String _getFormattedDate() {
    final now = DateTime.now();
    final months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${now.day} ${months[now.month - 1]}, ${now.year}';
  }

  String _getCollectionDisplayName(String name) {
    switch (name) {
      case 'bukhari':
        return 'Sahih al-Bukhari';
      case 'muslim':
        return 'Sahih Muslim';
      case 'nasai':
        return 'Sunan an-Nasa\'i';
      case 'abudawud':
        return 'Sunan Abu Dawud';
      case 'tirmidhi':
        return 'Jami` at-Tirmidhi';
      case 'ibnmajah':
        return 'Sunan Ibn Majah';
      case 'malik':
        return 'Muwatta Imam Malik';
      case 'riyadussalihin':
        return 'Riyad as-Salihin';
      default:
        return name;
    }
  }

  Future<void> _shareHadith(BuildContext context) async {
    final text = '''
${dailyHadith.hadith.arabic}

${dailyHadith.hadith.english}

${dailyHadith.hadith.reference}
Source: ${_getCollectionDisplayName(dailyHadith.collectionName)}
''';

    await Share.share(text);
  }

  void _readFull(BuildContext context) {
    try {
      // Create a temporary collection for navigation
      final collection = HadithCollection(
        name: dailyHadith.collectionName,
        title: _getCollectionDisplayName(dailyHadith.collectionName),
        shortIntro: '',
        totalHadithCount: 10000, // A large enough number to accommodate most collections
        hadiths: [dailyHadith.hadith], // Include the current hadith
      );

      // Navigate to reader
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => HadithReaderScreen(
            collection: collection,
            initialHadithNumber: dailyHadith.hadith.number,
          ),
        ),
      );
    } catch (e) {
      print('Error navigating to full hadith: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Could not open the full hadith. Please try again.'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
}