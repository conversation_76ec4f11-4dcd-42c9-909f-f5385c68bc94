/// Model representing a single hadith
class Hadith {
  final String id;
  final int number;
  final String arabic;
  final String english;
  final String reference;
  final String grade;
  final String narrator;
  final String chapterTitle;
  final int chapterNumber;
  final bool isFavorite;
  final List<String> tags;

  Hadith({
    required this.id,
    required this.number,
    required this.arabic,
    required this.english,
    required this.reference,
    this.grade = '',
    this.narrator = '',
    this.chapterTitle = '',
    this.chapterNumber = 0,
    this.isFavorite = false,
    this.tags = const [],
  });

  Hadith copyWith({
    String? id,
    int? number,
    String? arabic,
    String? english,
    String? reference,
    String? grade,
    String? narrator,
    String? chapterTitle,
    int? chapterNumber,
    bool? isFavorite,
    List<String>? tags,
  }) {
    return Hadith(
      id: id ?? this.id,
      number: number ?? this.number,
      arabic: arabic ?? this.arabic,
      english: english ?? this.english,
      reference: reference ?? this.reference,
      grade: grade ?? this.grade,
      narrator: narrator ?? this.narrator,
      chapterTitle: chapterTitle ?? this.chapterTitle,
      chapterNumber: chapterNumber ?? this.chapterNumber,
      isFavorite: isFavorite ?? this.isFavorite,
      tags: tags ?? this.tags,
    );
  }
}

/// Model representing a collection of hadiths (e.g., Sahih al-Bukhari)
class HadithCollection {
  final String name; // Unique identifier (e.g., 'bukhari')
  final String title; // Display name (e.g., 'Sahih al-Bukhari')
  final String shortIntro; // Brief description
  final List<Hadith> hadiths; // The actual hadiths in this collection
  final int totalHadithCount; // Total number of hadiths in the collection
  final String? image; // Optional image for the collection
  final bool isDownloaded; // Whether the collection is downloaded

  HadithCollection({
    required this.name,
    required this.title,
    required this.shortIntro,
    required this.hadiths,
    required this.totalHadithCount,
    this.image,
    this.isDownloaded = false,
  });

  HadithCollection copyWith({
    String? name,
    String? title,
    String? shortIntro,
    List<Hadith>? hadiths,
    int? totalHadithCount,
    String? image,
    bool? isDownloaded,
  }) {
    return HadithCollection(
      name: name ?? this.name,
      title: title ?? this.title,
      shortIntro: shortIntro ?? this.shortIntro,
      hadiths: hadiths ?? this.hadiths,
      totalHadithCount: totalHadithCount ?? this.totalHadithCount,
      image: image ?? this.image,
      isDownloaded: isDownloaded ?? this.isDownloaded,
    );
  }
}

/// Model for storing the last reading position
class HadithReadingProgress {
  final String collectionName;
  final int lastReadHadith;
  final DateTime lastReadTime;

  HadithReadingProgress({
    required this.collectionName,
    required this.lastReadHadith,
    required this.lastReadTime,
  });

  Map<String, dynamic> toJson() {
    return {
      'collectionName': collectionName,
      'lastReadHadith': lastReadHadith,
      'lastReadTime': lastReadTime.toIso8601String(),
    };
  }

  factory HadithReadingProgress.fromJson(Map<String, dynamic> json) {
    return HadithReadingProgress(
      collectionName: json['collectionName'],
      lastReadHadith: json['lastReadHadith'],
      lastReadTime: DateTime.parse(json['lastReadTime']),
    );
  }
}

/// Model for category/chapter in a hadith collection
class HadithChapter {
  final int chapterNumber;
  final String titleArabic;
  final String titleEnglish;
  final int hadithCount;
  final int startingHadithNumber;

  HadithChapter({
    required this.chapterNumber,
    required this.titleArabic,
    required this.titleEnglish,
    required this.hadithCount,
    required this.startingHadithNumber,
  });
}

/// Model for daily hadith
class DailyHadith {
  final Hadith hadith;
  final DateTime date;
  final String collectionName;

  DailyHadith({
    required this.hadith,
    required this.date,
    required this.collectionName,
  });
} 