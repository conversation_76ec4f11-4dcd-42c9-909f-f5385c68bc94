import 'package:flutter/material.dart';
import 'package:new_islamic_app_uk/features/hadith/models/hadith_models.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/favorite_hadiths_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_collection_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_home_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_reader_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_search_screen.dart';
import 'package:new_islamic_app_uk/features/hadith/services/hadith_service.dart';

/// This class provides access to the Hadith feature module.
class HadithModule {
  // Main entry point screen
  static Widget getHomeScreen() {
    return const HadithHomeScreen();
  }
  
  // Routes for navigation
  static final Map<String, WidgetBuilder> routes = {
    '/hadith': (context) => const HadithHomeScreen(),
    '/hadith/favorites': (context) => const FavoriteHadithsScreen(),
    '/hadith/search': (context) => const HadithSearchScreen(),
  };
  
  // Navigation helpers
  static void navigateToHome(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const HadithHomeScreen()),
    );
  }
  
  static void navigateToSearch(BuildContext context, {String? query}) {
    Navigator.push(
      context, 
      MaterialPageRoute(
        builder: (context) => HadithSearchScreen(
          initialQuery: query,
        )
      )
    );
  }
  
  static void navigateToFavorites(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const FavoriteHadithsScreen()),
    );
  }
  
  static void navigateToCollection(BuildContext context, HadithCollection collection) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HadithCollectionScreen(collection: collection),
      ),
    );
  }
  
  static void navigateToReader(BuildContext context, HadithCollection collection, int hadithNumber) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HadithReaderScreen(
          collection: collection,
          initialHadithNumber: hadithNumber,
        ),
      ),
    );
  }
} 