import 'dart:convert';
import 'dart:math' as Math;
import 'package:hadith/hadith.dart' as hadith_pkg;
import 'package:new_islamic_app_uk/features/hadith/models/hadith_models.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for fetching and managing hadith data
class HadithService {
  // Cache collections to avoid repeated fetching
  final Map<String, HadithCollection> _collectionsCache = {};
  
  // Define the major hadith collections
  final List<Map<String, dynamic>> _availableCollections = [
    {
      'name': 'bukhari',
      'title': '<PERSON><PERSON><PERSON> <PERSON><PERSON>',
      'shortIntro': 'Most authentic collection of hadiths compiled by <PERSON>',
      'totalHadithCount': 7563,
      'image': 'assets/images/bukhari.png',
    },
    {
      'name': 'muslim',
      'title': '<PERSON><PERSON><PERSON>',
      'shortIntro': 'Second most authentic hadith collection, compiled by <PERSON>',
      'totalHadithCount': 7563,
      'image': 'assets/images/muslim.png',
    },
    {
      'name': 'nasai',
      'title': '<PERSON><PERSON>\'i',
      'shortIntro': 'One of the six major hadith collections',
      'totalHadithCount': 5761,
      'image': 'assets/images/nasai.png',
    },
    {
      'name': 'abudawud',
      'title': '<PERSON>an Abu Dawud',
      'shortIntro': 'Collection focused on legal matters by Abu Dawud',
      'totalHadithCount': 5274,
      'image': 'assets/images/abudawud.png',
    },
    {
      'name': 'tirmidhi',
      'title': 'Jami` at-Tirmidhi',
      'shortIntro': 'Collection with focus on legal and ritual matters',
      'totalHadithCount': 3956,
      'image': 'assets/images/tirmidhi.png',
    },
    {
      'name': 'ibnmajah',
      'title': 'Sunan Ibn Majah',
      'shortIntro': 'One of the six canonical hadith collections in Sunni Islam',
      'totalHadithCount': 4341,
      'image': 'assets/images/ibnmajah.png',
    },
    {
      'name': 'malik',
      'title': 'Muwatta Imam Malik',
      'shortIntro': 'Earliest surviving Sunni hadith collection, compiled by Imam Malik',
      'totalHadithCount': 1720,
      'image': 'assets/images/malik.png',
    },
    {
      'name': 'riyadussalihin',
      'title': 'Riyad as-Salihin',
      'shortIntro': 'Garden of the Righteous, compiled by Imam an-Nawawi',
      'totalHadithCount': 1896,
      'image': 'assets/images/riyadussalihin.png',
    },
  ];

  /// Clean the hadith text from HTML-like tags and markup
  String cleanHadithText(String text) {
    if (text.isEmpty) return text;
    
    String cleaned = text;
    
    // Remove narrator ID tags with more comprehensive patterns
    cleaned = cleaned.replaceAll(RegExp(r'narrator id="[^"]*"'), '');
    cleaned = cleaned.replaceAll(RegExp(r'narrator/'), '');
    cleaned = cleaned.replaceAll('narrator', '');
    
    // Remove tooltip tags with more comprehensive patterns
    cleaned = cleaned.replaceAll(RegExp(r'="tooltip[^"]*"'), '');
    cleaned = cleaned.replaceAll(RegExp(r'="[^"]*"'), '');
    cleaned = cleaned.replaceAll('tooltip', '');
    
    // Remove all attribute patterns
    cleaned = cleaned.replaceAll(RegExp(r'\w+="[^"]*"'), '');
    
    // Remove HTML-like tags (including self-closing)
    cleaned = cleaned.replaceAll(RegExp(r'<[^>]*>'), '');
    cleaned = cleaned.replaceAll(RegExp(r'<[^>]*/>'), '');
    
    // Remove square and curly brackets and their content
    cleaned = cleaned.replaceAll(RegExp(r'\[[^\]]*\]'), '');
    cleaned = cleaned.replaceAll(RegExp(r'\{[^\}]*\}'), '');
    
    // Remove remaining brackets
    cleaned = cleaned.replaceAll('[', '').replaceAll(']', '');
    cleaned = cleaned.replaceAll('{', '').replaceAll('}', '');
    
    // Fix common formatting issues
    cleaned = cleaned.replaceAll('p>', '');
    cleaned = cleaned.replaceAll('prematn', '');
    cleaned = cleaned.replaceAll('matn', '');
    cleaned = cleaned.replaceAll('//', '');
    
    // Handle trailing slashes and specific patterns
    cleaned = cleaned.replaceAll('/', ' ');
    cleaned = cleaned.replaceAll('\\', ' ');
    
    // Fix double spaces and trim
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    return cleaned;
  }

  /// Get all available hadith collections
  List<HadithCollection> getAllCollections() {
    final collections = _availableCollections.map((collectionData) {
      return HadithCollection(
        name: collectionData['name'],
        title: collectionData['title'],
        shortIntro: collectionData['shortIntro'],
        hadiths: [], // Initially empty, load when needed
        totalHadithCount: collectionData['totalHadithCount'],
        image: collectionData['image'],
      );
    }).toList();
    
    return collections;
  }

  /// Get a specific hadith collection with its hadiths
  Future<HadithCollection> getCollection(String collectionName, {int limit = 10, int page = 1}) async {
    // Return from cache if available
    if (_collectionsCache.containsKey('${collectionName}_${page}_$limit')) {
      return _collectionsCache['${collectionName}_${page}_$limit']!;
    }

    try {
      // Find the collection metadata
      final collectionMetadata = _availableCollections.firstWhere(
        (c) => c['name'] == collectionName,
        orElse: () => throw Exception('Collection not found: $collectionName'),
      );

      // Calculate range to fetch
      final startIndex = (page - 1) * limit + 1;
      final endIndex = startIndex + limit - 1;

      // Fetch hadiths from the package
      final List<Hadith> hadiths = [];
      
      for (int i = startIndex; i <= endIndex; i++) {
        try {
          // Get collection enum value
          final collection = _getCollectionEnum(collectionName);
          
          // Get hadith data by index
          final bookNumber = 1; // Default to book 1 for simplicity
          final hadithObj = await hadith_pkg.getHadith(collection, bookNumber, i);
          
          if (hadithObj != null) {
            // Get hadith data in English and Arabic
            final hadithEn = await hadith_pkg.getHadithData(collection, bookNumber, i, hadith_pkg.Languages.en);
            final hadithAr = await hadith_pkg.getHadithData(collection, bookNumber, i, hadith_pkg.Languages.ar);
            
            hadiths.add(Hadith(
              id: '${collectionName}_$i',
              number: i,
              arabic: cleanHadithText(hadithAr?.body ?? ''),
              english: cleanHadithText(hadithEn?.body ?? ''),
              reference: '${collectionMetadata['title']}, Book $bookNumber, Hadith $i',
              grade: '',  // Grade not directly available in package
              narrator: '',  // Narrator not directly available in package
              chapterTitle: hadithEn?.chapterTitle ?? '',
              chapterNumber: bookNumber,
            ));
          }
        } catch (e) {
          // Skip this hadith if there's an error
          print('Error fetching hadith $i from $collectionName: $e');
        }

        // Break if we've reached the end of the collection
        if (i >= collectionMetadata['totalHadithCount']) break;
      }

      // Create collection
      final collection = HadithCollection(
        name: collectionName,
        title: collectionMetadata['title'],
        shortIntro: collectionMetadata['shortIntro'],
        hadiths: hadiths,
        totalHadithCount: collectionMetadata['totalHadithCount'],
        image: collectionMetadata['image'],
      );

      // Cache the result
      _collectionsCache['${collectionName}_${page}_$limit'] = collection;
      
      return collection;
    } catch (e) {
      print('Error fetching collection $collectionName: $e');
      throw Exception('Failed to load hadith collection: $e');
    }
  }

  /// Get a single hadith by its number from a collection
  Future<Hadith?> getHadith(String collectionName, int hadithNumber) async {
    try {
      // Make sure hadithNumber is within reasonable bounds
      final collectionMetadata = _availableCollections.firstWhere(
        (c) => c['name'] == collectionName,
        orElse: () => {'title': collectionName.toUpperCase(), 'totalHadithCount': 10000},
      );
      
      // Get collection enum value
      final collection = _getCollectionEnum(collectionName);
      
      // For many collections, book 1 is a safe default
      int bookNumber = 1;
      
      // Get the list of books
      List<dynamic> books = [];
      try {
        books = await hadith_pkg.getBooks(collection);
      } catch (e) {
        print('Error fetching books for collection $collectionName: $e');
        // Continue with default book number
      }
      
      // Verify the hadith number is within range for this collection
      bool isInRange = true;
      
      if (books.isNotEmpty) {
        try {
          final hadiths = await hadith_pkg.getHadiths(collection, bookNumber);
          
          // Check if hadithNumber is in range
          isInRange = hadithNumber > 0 && hadithNumber <= hadiths.length;
          
          if (!isInRange) {
            print('Hadith number $hadithNumber is out of range for $collectionName book $bookNumber (max: ${hadiths.length})');
            return null;
          }
        } catch (e) {
          print('Error checking hadith range: $e');
          // Continue with the attempt, but be conservative
          isInRange = hadithNumber > 0 && hadithNumber <= 100; // Conservative default
        }
      } else {
        // If books couldn't be fetched, use metadata as a fallback
        final totalCount = collectionMetadata['totalHadithCount'] as int;
        isInRange = hadithNumber > 0 && hadithNumber <= totalCount * 0.3; // Very conservative 30% limit
      }
      
      if (!isInRange) {
        print('Hadith number $hadithNumber is likely out of range for $collectionName');
        return null;
      }
      
      // Get hadith data in English and Arabic
      final hadithEn = await hadith_pkg.getHadithData(collection, bookNumber, hadithNumber, hadith_pkg.Languages.en);
      
      // If English data is not available, return null
      if (hadithEn == null || hadithEn.body.isEmpty) {
        print('No hadith data found for $collectionName, number $hadithNumber');
        return null;
      }
      
      // Try to get Arabic version, with fallback to empty string if not available
      String arabicText = '';
      try {
        final hadithAr = await hadith_pkg.getHadithData(collection, bookNumber, hadithNumber, hadith_pkg.Languages.ar);
        arabicText = hadithAr?.body ?? '';
      } catch (e) {
        print('Could not fetch Arabic text for hadith $hadithNumber: $e');
        // Continue with empty Arabic text
      }
      
      return Hadith(
        id: '${collectionName}_$hadithNumber',
        number: hadithNumber,
        arabic: cleanHadithText(arabicText),
        english: cleanHadithText(hadithEn.body),
        reference: '${collectionMetadata['title']}, Book $bookNumber, Hadith $hadithNumber',
        grade: '',  // Grade not directly available in package
        narrator: '',  // Narrator not directly available in package
        chapterTitle: hadithEn.chapterTitle ?? '',
        chapterNumber: bookNumber,
      );
    } catch (e) {
      print('Error fetching hadith $hadithNumber from $collectionName: $e');
      return null;
    }
  }

  /// Search for hadiths by keyword
  Future<List<Hadith>> searchHadiths(String query, {String? collectionName, int limit = 20}) async {
    List<Hadith> results = [];
    Set<String> uniqueIds = {}; // Track unique hadith IDs to prevent duplicates
    
    try {
      // Trim and normalize query
      final String normalizedQuery = query.trim().toLowerCase();
      if (normalizedQuery.isEmpty) {
        return [];
      }
      
      // Get collection enum value if provided
      hadith_pkg.Collections? collection;
      if (collectionName != null) {
        collection = _getCollectionEnum(collectionName);
      }
      
      // Process each collection in turn, or just the specified one
      final collections = collection != null 
        ? [collection] 
        : hadith_pkg.Collections.values;
      
      // Only process major collections to avoid errors
      final validCollections = collections.where((col) {
        final colName = col.toString().split('.').last;
        return _availableCollections.any((c) => c['name'] == colName);
      }).toList();
      
      for (final col in validCollections) {
        // Get collection name
        final colName = col.toString().split('.').last;
        
        // Skip processing if we've reached the limit
        if (results.length >= limit) break;
        
        try {
          // Get collection metadata
          final collectionMetadata = _availableCollections.firstWhere(
            (c) => c['name'] == colName,
            orElse: () => {'title': colName.toUpperCase(), 'totalHadithCount': 100},
          );
          
          // For performance, limit search to first 100 hadiths in the collection (or less for smaller collections)
          final searchLimit = 100;
          final sampleSize = Math.min(searchLimit, collectionMetadata['totalHadithCount'] as int);
          
          // Use a single book for search (book 1 is usually a good sample)
          final bookNumber = 1;
          
          // First check if we can get hadiths for this book
          List<dynamic> hadiths = [];
          try {
            hadiths = await hadith_pkg.getHadiths(col, bookNumber);
          } catch (e) {
            print('Error getting hadiths for collection $colName, book $bookNumber: $e');
            continue; // Skip to next collection if we can't get hadiths
          }
          
          // Safety check
          if (hadiths.isEmpty) {
            continue;
          }
          
          // For each hadith in our sample
          for (int i = 1; i <= Math.min(sampleSize, hadiths.length); i++) {
            if (results.length >= limit) break;
            
            try {
              // Get the full hadith text in English first to check for match
              final hadithEn = await hadith_pkg.getHadithData(
                col,
                bookNumber,
                i,
                hadith_pkg.Languages.en
              );
              
              // Skip if no data or doesn't match query
              if (hadithEn == null || hadithEn.body.isEmpty) {
                continue;
              }
              
              // Check if this hadith matches the query
              final hadithText = hadithEn.body.toLowerCase();
              final chapterTitle = (hadithEn.chapterTitle ?? '').toLowerCase();
              
              if (hadithText.contains(normalizedQuery) || chapterTitle.contains(normalizedQuery)) {
                // Create unique ID for this hadith
                final hadithId = '${colName}_${bookNumber}_$i';
                
                // Skip if we've already added this hadith
                if (uniqueIds.contains(hadithId)) {
                  continue;
                }
                
                // Get the Arabic version (optional)
                String arabicText = '';
                try {
                  final hadithAr = await hadith_pkg.getHadithData(
                    col, 
                    bookNumber,
                    i,
                    hadith_pkg.Languages.ar
                  );
                  arabicText = hadithAr?.body ?? '';
                } catch (e) {
                  print('Error getting Arabic text for hadith $i in $colName: $e');
                  // Continue with empty Arabic text
                }
                
                // Get collection title for reference
                final collectionTitle = collectionMetadata['title'];
                
                // Add to results
                results.add(Hadith(
                  id: hadithId,
                  number: i,
                  arabic: cleanHadithText(arabicText),
                  english: cleanHadithText(hadithEn.body),
                  reference: '$collectionTitle, Book $bookNumber, Hadith $i',
                  grade: '',  // Grade not directly available in package
                  narrator: '',  // Narrator not directly available in package
                  chapterTitle: cleanHadithText(hadithEn.chapterTitle ?? ''),
                  chapterNumber: bookNumber,
                ));
                
                // Track added ID
                uniqueIds.add(hadithId);
              }
            } catch (e) {
              print('Error processing hadith $i in $colName: $e');
              // Continue to next hadith
            }
          }
        } catch (e) {
          print('Error searching in collection $colName: $e');
          // Continue to next collection
        }
      }
      
      return results;
    } catch (e) {
      print('Error searching hadiths: $e');
      return [];
    }
  }

  /// Get chapters in a collection
  Future<List<HadithChapter>> getChapters(String collectionName) async {
    try {
      // Get collection enum value
      final collection = _getCollectionEnum(collectionName);
      
      // Get books (chapters) from the collection
      final books = await hadith_pkg.getBooks(collection);
      
      // Convert to our model
      return books.map((book) {
        // Get book properties safely
        final bookNumber = book.bookNumber is int ? book.bookNumber as int : 0;
        final hadithCount = book.numberOfHadith is int ? book.numberOfHadith as int : 0;
        
        // For chapter title, access the first hadith to get its chapter title
        String bookName = '';
        try {
          // Get a basic identifier for the book
          bookName = "Book $bookNumber";
        } catch (e) {
          print('Error getting book name: $e');
        }
        
        final hadithStartNumber = 1; // Default value
        
        return HadithChapter(
          chapterNumber: bookNumber,
          titleArabic: bookName,
          titleEnglish: bookName,
          hadithCount: hadithCount,
          startingHadithNumber: hadithStartNumber,
        );
      }).toList();
    } catch (e) {
      print('Error fetching chapters from $collectionName: $e');
      return [];
    }
  }

  /// Save reading progress
  Future<void> saveReadingProgress(String collectionName, int hadithNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progress = HadithReadingProgress(
        collectionName: collectionName,
        lastReadHadith: hadithNumber,
        lastReadTime: DateTime.now(),
      );
      
      await prefs.setString('hadith_reading_progress', jsonEncode(progress.toJson()));
    } catch (e) {
      print('Error saving reading progress: $e');
    }
  }

  /// Get reading progress
  Future<HadithReadingProgress?> getReadingProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? progressJson = prefs.getString('hadith_reading_progress');
      
      if (progressJson != null) {
        return HadithReadingProgress.fromJson(jsonDecode(progressJson));
      }
      return null;
    } catch (e) {
      print('Error getting reading progress: $e');
      return null;
    }
  }

  /// Get a random hadith for daily hadith feature
  Future<DailyHadith> getDailyHadith() async {
    try {
      // Choose a random collection (biasing toward reliable collections)
      final collections = [
        'bukhari', 'bukhari', 'muslim', 'muslim', 'tirmidhi', 'abudawud', 'nasai', 'ibnmajah'
      ];
      final collectionIndex = DateTime.now().day % collections.length;
      final collectionName = collections[collectionIndex];
      
      // Get collection metadata
      final collection = _availableCollections.firstWhere(
        (c) => c['name'] == collectionName,
        orElse: () => {'name': 'bukhari', 'title': 'Sahih al-Bukhari', 'totalHadithCount': 7000},
      );
      
      // Get collection enum
      final collectionEnum = _getCollectionEnum(collectionName);
      
      // We'll use a safe approach with known valid hadith numbers for each collection
      Map<String, List<int>> safeHadithNumbers = {
        'bukhari': [1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 13, 15, 16, 20, 25, 30, 40, 50],
        'muslim': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15, 20, 25, 30],
        'tirmidhi': [1, 2, 3, 4, 5, 10, 15, 20],
        'abudawud': [1, 2, 3, 4, 5, 10, 20, 30],
        'nasai': [1, 2, 3, 4, 5, 10, 20],
        'ibnmajah': [1, 2, 3, 4, 5, 10],
        'malik': [1, 2, 3, 4, 5],
      };
      
      // Use a list of safe hadith numbers for the selected collection, or default list
      List<int> hadithOptions = safeHadithNumbers[collectionName] ?? [1, 2, 3, 4, 5];
      
      // Try to fetch a random hadith from the safe options
      List<int> shuffledOptions = List.from(hadithOptions)..shuffle();
      Hadith? hadith;
      
      // Try each number in turn until we get a valid hadith
      for (final number in shuffledOptions) {
        try {
          hadith = await getHadith(collectionName, number);
          if (hadith != null) {
            print('Successfully loaded hadith #$number from $collectionName');
            break;
          }
        } catch (e) {
          print('Error fetching hadith $number from $collectionName: $e');
          // Continue to next option
        }
      }
      
      // If we found a valid hadith, return it
      if (hadith != null) {
        return DailyHadith(
          hadith: hadith,
          date: DateTime.now(),
          collectionName: collectionName,
        );
      }
      
      // Try a fallback hadith from Bukhari if we couldn't get one from our chosen collection
      if (collectionName != 'bukhari') {
        try {
          hadith = await getHadith('bukhari', 1);
          if (hadith != null) {
            return DailyHadith(
              hadith: hadith,
              date: DateTime.now(),
              collectionName: 'bukhari',
            );
          }
        } catch (e) {
          print('Error fetching fallback hadith from bukhari: $e');
        }
      }
      
      // Return a hardcoded fallback if all else fails
      return _getFallbackHadith(collectionName);
    } catch (e) {
      print('Error getting daily hadith: $e');
      return _getFallbackHadith('bukhari');
    }
  }
  
  // Helper method to create a fallback hadith
  DailyHadith _getFallbackHadith(String collectionName) {
    return DailyHadith(
      hadith: Hadith(
        id: 'fallback',
        number: 1,
        arabic: 'إنما الأعمال بالنيات',
        english: 'Actions are judged by intentions.',
        reference: 'Sahih al-Bukhari, Book 1, Hadith 1',
      ),
      date: DateTime.now(),
      collectionName: collectionName,
    );
  }
  
  /// Helper method to convert string collection name to enum
  hadith_pkg.Collections _getCollectionEnum(String name) {
    switch (name) {
      case 'bukhari':
        return hadith_pkg.Collections.bukhari;
      case 'muslim':
        return hadith_pkg.Collections.muslim;
      case 'nasai':
        return hadith_pkg.Collections.nasai;
      case 'abudawud':
        return hadith_pkg.Collections.abudawud;
      case 'tirmidhi':
        return hadith_pkg.Collections.tirmidhi;
      case 'ibnmajah':
        return hadith_pkg.Collections.ibnmajah;
      default:
        // Default to Bukhari if not found
        return hadith_pkg.Collections.bukhari;
    }
  }
} 