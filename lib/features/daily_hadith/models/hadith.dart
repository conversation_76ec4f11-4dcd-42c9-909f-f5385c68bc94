import 'package:flutter/foundation.dart';

class Hadith {
  final String id;
  final String text;
  final String translation;
  final String explanation;
  final String reference;
  final String narrator;
  final List<String> tags;
  final DateTime date;
  final bool isFavorite;

  Hadith({
    required this.id,
    required this.text,
    required this.translation,
    required this.explanation,
    required this.reference,
    required this.narrator,
    this.tags = const [],
    required this.date,
    this.isFavorite = false,
  });

  factory Hadith.fromJson(Map<String, dynamic> json) {
    return Hadith(
      id: json['id'] as String,
      text: json['text'] as String,
      translation: json['translation'] as String,
      explanation: json['explanation'] as String,
      reference: json['reference'] as String,
      narrator: json['narrator'] as String,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ?? [],
      date: DateTime.parse(json['date'] as String),
      isFavorite: json['isFavorite'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'translation': translation,
      'explanation': explanation,
      'reference': reference,
      'narrator': narrator,
      'tags': tags,
      'date': date.toIso8601String(),
      'isFavorite': isFavorite,
    };
  }

  Hadith copyWith({
    String? id,
    String? text,
    String? translation,
    String? explanation,
    String? reference,
    String? narrator,
    List<String>? tags,
    DateTime? date,
    bool? isFavorite,
  }) {
    return Hadith(
      id: id ?? this.id,
      text: text ?? this.text,
      translation: translation ?? this.translation,
      explanation: explanation ?? this.explanation,
      reference: reference ?? this.reference,
      narrator: narrator ?? this.narrator,
      tags: tags ?? this.tags,
      date: date ?? this.date,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Hadith && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
} 