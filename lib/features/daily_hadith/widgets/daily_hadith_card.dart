import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/daily_hadith/models/hadith.dart';
import 'package:new_islamic_app_uk/features/daily_hadith/screens/hadith_detail_screen.dart';
import 'package:new_islamic_app_uk/features/daily_hadith/services/hadith_service.dart';

class DailyHadithCard extends StatefulWidget {
  const DailyHadithCard({Key? key}) : super(key: key);

  @override
  State<DailyHadithCard> createState() => _DailyHadithCardState();
}

class _DailyHadithCardState extends State<DailyHadithCard> {
  final HadithService _hadithService = HadithService();
  bool _isLoading = true;
  Hadith? _dailyHadith;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _loadDailyHadith();
  }

  Future<void> _loadDailyHadith() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final hadith = await _hadithService.getDailyHadith();
      setState(() {
        _dailyHadith = hadith;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleFavorite() async {
    if (_dailyHadith == null) return;

    final updatedHadith = await _hadithService.toggleFavorite(_dailyHadith!);
    setState(() {
      _dailyHadith = updatedHadith;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            if (_dailyHadith != null) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => HadithDetailScreen(hadith: _dailyHadith!),
                ),
              );
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: _isLoading
                ? _buildLoadingState()
                : _dailyHadith == null
                    ? _buildErrorState()
                    : _buildHadithContent(),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(
            color: Color(URLs().colorScheme),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading daily hadith...',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline,
            size: 40,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Unable to load hadith',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: _loadDailyHadith,
            child: const Text('Try Again'),
            style: TextButton.styleFrom(
              foregroundColor: Color(URLs().colorScheme),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHadithContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Color(URLs().colorScheme).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.auto_stories,
                color: Color(URLs().colorScheme),
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Daily Hadith',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ),
            IconButton(
              icon: Icon(
                _dailyHadith!.isFavorite
                    ? Icons.favorite
                    : Icons.favorite_border,
                color: _dailyHadith!.isFavorite
                    ? Colors.red
                    : Colors.grey[400],
                size: 22,
              ),
              onPressed: _toggleFavorite,
              tooltip: 'Add to favorites',
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Text(
          _dailyHadith!.text,
          textAlign: TextAlign.right,
          style: GoogleFonts.scheherazadeNew(
            fontSize: 22,
            fontWeight: FontWeight.w600,
            color: Colors.grey[800],
            height: 1.5,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          _dailyHadith!.translation,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontStyle: FontStyle.italic,
            color: Colors.grey[700],
            height: 1.5,
          ),
        ),
        const SizedBox(height: 12),
        AnimatedCrossFade(
          firstChild: const SizedBox(height: 0),
          secondChild: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Divider(),
              const SizedBox(height: 8),
              Text(
                'Explanation:',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 4),
              LayoutBuilder(
                builder: (context, constraints) {
                  return Container(
                    width: constraints.maxWidth,
                    child: Text(
                      _dailyHadith!.explanation,
                      style: GoogleFonts.poppins(
                        fontSize: 13,
                        color: Colors.grey[700],
                        height: 1.5,
                      ),
                      overflow: TextOverflow.visible,
                    ),
                  );
                },
              ),
              const SizedBox(height: 12),
            ],
          ),
          crossFadeState: _isExpanded
              ? CrossFadeState.showSecond
              : CrossFadeState.showFirst,
          duration: const Duration(milliseconds: 300),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Text(
                'Narrator: ${_dailyHadith!.narrator}',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
              child: Text(
                _isExpanded ? 'Show Less' : 'Read More',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              style: TextButton.styleFrom(
                foregroundColor: Color(URLs().colorScheme),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: const Size(0, 0),
              ),
            ),
          ],
        ),
        if (_isExpanded) ...[
          const SizedBox(height: 8),
          Text(
            'Reference: ${_dailyHadith!.reference}',
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              'Tap to view full details',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[400],
              size: 12,
            ),
          ],
        ),
      ],
    );
  }
} 