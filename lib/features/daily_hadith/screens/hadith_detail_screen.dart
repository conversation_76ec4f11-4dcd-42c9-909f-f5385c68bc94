import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/daily_hadith/models/hadith.dart';
import 'package:new_islamic_app_uk/features/daily_hadith/services/hadith_service.dart';
import 'package:share_plus/share_plus.dart';

class HadithDetailScreen extends StatefulWidget {
  final Hadith hadith;

  const HadithDetailScreen({
    Key? key,
    required this.hadith,
  }) : super(key: key);

  @override
  State<HadithDetailScreen> createState() => _HadithDetailScreenState();
}

class _HadithDetailScreenState extends State<HadithDetailScreen> {
  final HadithService _hadithService = HadithService();
  late Hadith _hadith;
  bool _isTextCopied = false;

  @override
  void initState() {
    super.initState();
    _hadith = widget.hadith;
  }

  Future<void> _toggleFavorite() async {
    final updatedHadith = await _hadithService.toggleFavorite(_hadith);
    setState(() {
      _hadith = updatedHadith;
    });
  }

  void _copyHadithToClipboard() {
    final String textToCopy = 
        '${_hadith.text}\n\n'
        '${_hadith.translation}\n\n'
        'Narrator: ${_hadith.narrator}\n'
        'Reference: ${_hadith.reference}';
    
    Clipboard.setData(ClipboardData(text: textToCopy));
    
    setState(() {
      _isTextCopied = true;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Hadith copied to clipboard'),
        backgroundColor: Color(URLs().colorScheme),
        duration: Duration(seconds: 2),
      ),
    );
    
    Future.delayed(Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isTextCopied = false;
        });
      }
    });
  }

  void _shareHadith() {
    final String textToShare = 
        '${_hadith.text}\n\n'
        '${_hadith.translation}\n\n'
        'Narrator: ${_hadith.narrator}\n'
        'Reference: ${_hadith.reference}\n\n'
        'Shared from Masjid Sunnah App';
    
    Share.share(textToShare);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'Hadith',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Color(URLs().colorScheme),
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Color(URLs().colorScheme)),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _hadith.isFavorite ? Icons.favorite : Icons.favorite_border,
              color: _hadith.isFavorite ? Colors.red : Color(URLs().colorScheme),
            ),
            onPressed: _toggleFavorite,
            tooltip: _hadith.isFavorite ? 'Remove from favorites' : 'Add to favorites',
          ),
          IconButton(
            icon: Icon(
              _isTextCopied ? Icons.check : Icons.content_copy,
              color: Color(URLs().colorScheme),
            ),
            onPressed: _copyHadithToClipboard,
            tooltip: 'Copy hadith',
          ),
          IconButton(
            icon: Icon(
              Icons.share,
              color: Color(URLs().colorScheme),
            ),
            onPressed: _shareHadith,
            tooltip: 'Share hadith',
          ),
        ],
      ),
      body: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHadithCard(),
            SizedBox(height: 24),
            _buildExplanationCard(),
            SizedBox(height: 24),
            _buildReferenceCard(),
            SizedBox(height: 24),
            _buildTagsCard(),
            SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildHadithCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Color(URLs().colorScheme).withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Color(URLs().colorScheme).withOpacity(0.1),
                width: 1,
              ),
            ),
            child: Text(
              _hadith.text,
              textAlign: TextAlign.center,
              style: GoogleFonts.scheherazadeNew(
                fontSize: 28,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
                height: 1.6,
              ),
            ),
          ),
          SizedBox(height: 20),
          Text(
            _hadith.translation,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontStyle: FontStyle.italic,
              color: Colors.grey[700],
              height: 1.6,
            ),
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Icon(
                FontAwesomeIcons.userPen,
                size: 16,
                color: Color(URLs().colorScheme),
              ),
              SizedBox(width: 8),
              Text(
                'Narrator:',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  _hadith.narrator,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExplanationCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.circleInfo,
                size: 16,
                color: Color(URLs().colorScheme),
              ),
              SizedBox(width: 8),
              Text(
                'Explanation',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Text(
            _hadith.explanation,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReferenceCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.book,
                size: 16,
                color: Color(URLs().colorScheme),
              ),
              SizedBox(width: 8),
              Text(
                'Reference',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.grey[300]!,
                width: 1,
              ),
            ),
            child: Text(
              _hadith.reference,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
          ),
          SizedBox(height: 16),
          Text(
            'Date: ${_hadith.date.day}/${_hadith.date.month}/${_hadith.date.year}',
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTagsCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.tags,
                size: 16,
                color: Color(URLs().colorScheme),
              ),
              SizedBox(width: 8),
              Text(
                'Tags',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _hadith.tags.map((tag) {
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Color(URLs().colorScheme).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Color(URLs().colorScheme).withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Text(
                  '#$tag',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Color(URLs().colorScheme),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
} 