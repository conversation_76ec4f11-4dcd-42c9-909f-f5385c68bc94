import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/daily_hadith/models/hadith.dart';
import 'package:new_islamic_app_uk/features/daily_hadith/screens/hadith_detail_screen.dart';
import 'package:new_islamic_app_uk/features/daily_hadith/services/hadith_service.dart';

class HadithCollectionScreen extends StatefulWidget {
  const HadithCollectionScreen({Key? key}) : super(key: key);

  @override
  State<HadithCollectionScreen> createState() => _HadithCollectionScreenState();
}

class _HadithCollectionScreenState extends State<HadithCollectionScreen> with SingleTickerProviderStateMixin {
  final HadithService _hadithService = HadithService();
  final TextEditingController _searchController = TextEditingController();
  late TabController _tabController;
  
  bool _isLoading = true;
  List<Hadith> _recentHadiths = [];
  List<Hadith> _favoriteHadiths = [];
  List<Hadith> _searchResults = [];
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final recentHadiths = await _hadithService.getRecentHadiths();
      final favoriteHadiths = await _hadithService.getFavoriteHadiths();

      setState(() {
        _recentHadiths = recentHadiths;
        _favoriteHadiths = favoriteHadiths;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _searchHadiths(String query) async {
    if (query.isEmpty) {
      setState(() {
        _isSearching = false;
        _searchResults = [];
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      final results = await _hadithService.searchHadiths(query);
      setState(() {
        _searchResults = results;
      });
    } catch (e) {
      // Handle error
    }
  }

  Future<void> _toggleFavorite(Hadith hadith) async {
    final updatedHadith = await _hadithService.toggleFavorite(hadith);
    
    setState(() {
      // Update in recent hadiths list
      final recentIndex = _recentHadiths.indexWhere((h) => h.id == hadith.id);
      if (recentIndex != -1) {
        _recentHadiths[recentIndex] = updatedHadith;
      }
      
      // Update in search results
      final searchIndex = _searchResults.indexWhere((h) => h.id == hadith.id);
      if (searchIndex != -1) {
        _searchResults[searchIndex] = updatedHadith;
      }
      
      // Update favorites list
      if (updatedHadith.isFavorite) {
        if (!_favoriteHadiths.contains(updatedHadith)) {
          _favoriteHadiths.add(updatedHadith);
        }
      } else {
        _favoriteHadiths.removeWhere((h) => h.id == hadith.id);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: _isSearching
            ? TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search hadiths...',
                  hintStyle: GoogleFonts.poppins(
                    fontSize: 16,
                    color: Colors.grey[400],
                  ),
                  border: InputBorder.none,
                ),
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  color: Colors.grey[800],
                ),
                onChanged: _searchHadiths,
                autofocus: true,
              )
            : Text(
                'Hadith Collection',
                style: GoogleFonts.poppins(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Color(URLs().colorScheme),
                ),
              ),
        leading: _isSearching
            ? IconButton(
                icon: Icon(Icons.arrow_back, color: Color(URLs().colorScheme)),
                onPressed: () {
                  setState(() {
                    _isSearching = false;
                    _searchController.clear();
                    _searchResults = [];
                  });
                },
              )
            : IconButton(
                icon: Icon(Icons.arrow_back, color: Color(URLs().colorScheme)),
                onPressed: () => Navigator.pop(context),
              ),
        actions: [
          if (!_isSearching)
            IconButton(
              icon: Icon(Icons.search, color: Color(URLs().colorScheme)),
              onPressed: () {
                setState(() {
                  _isSearching = true;
                });
              },
            ),
          if (_isSearching && _searchController.text.isNotEmpty)
            IconButton(
              icon: Icon(Icons.clear, color: Color(URLs().colorScheme)),
              onPressed: () {
                _searchController.clear();
                _searchHadiths('');
              },
            ),
        ],
        bottom: _isSearching
            ? null
            : TabBar(
                controller: _tabController,
                labelColor: Color(URLs().colorScheme),
                unselectedLabelColor: Colors.grey[600],
                indicatorColor: Color(URLs().colorScheme),
                tabs: [
                  Tab(text: 'Recent'),
                  Tab(text: 'Favorites'),
                ],
              ),
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: Color(URLs().colorScheme),
              ),
            )
          : _isSearching
              ? _buildSearchResults()
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildRecentHadithsList(),
                    _buildFavoriteHadithsList(),
                  ],
                ),
    );
  }

  Widget _buildSearchResults() {
    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FontAwesomeIcons.magnifyingGlass,
              size: 48,
              color: Colors.grey[300],
            ),
            SizedBox(height: 16),
            Text(
              _searchController.text.isEmpty
                  ? 'Type to search for hadiths'
                  : 'No results found for "${_searchController.text}"',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        return _buildHadithListItem(_searchResults[index]);
      },
    );
  }

  Widget _buildRecentHadithsList() {
    if (_recentHadiths.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FontAwesomeIcons.clockRotateLeft,
              size: 48,
              color: Colors.grey[300],
            ),
            SizedBox(height: 16),
            Text(
              'No recent hadiths',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      color: Color(URLs().colorScheme),
      child: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: _recentHadiths.length,
        itemBuilder: (context, index) {
          return _buildHadithListItem(_recentHadiths[index]);
        },
      ),
    );
  }

  Widget _buildFavoriteHadithsList() {
    if (_favoriteHadiths.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FontAwesomeIcons.heart,
              size: 48,
              color: Colors.grey[300],
            ),
            SizedBox(height: 16),
            Text(
              'No favorite hadiths yet',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Tap the heart icon to add hadiths to favorites',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: _favoriteHadiths.length,
      itemBuilder: (context, index) {
        return _buildHadithListItem(_favoriteHadiths[index]);
      },
    );
  }

  Widget _buildHadithListItem(Hadith hadith) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => HadithDetailScreen(hadith: hadith),
            ),
          ).then((_) => _loadData());
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Narrator: ${hadith.narrator}',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Color(URLs().colorScheme),
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'Reference: ${hadith.reference}',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      hadith.isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: hadith.isFavorite ? Colors.red : Colors.grey[400],
                    ),
                    onPressed: () => _toggleFavorite(hadith),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Color(URLs().colorScheme).withOpacity(0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Color(URLs().colorScheme).withOpacity(0.1),
                    width: 1,
                  ),
                ),
                child: Text(
                  hadith.text,
                  textAlign: TextAlign.right,
                  style: GoogleFonts.scheherazadeNew(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[800],
                    height: 1.6,
                  ),
                ),
              ),
              SizedBox(height: 12),
              Text(
                hadith.translation,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: Colors.grey[700],
                  height: 1.5,
                ),
              ),
              SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: hadith.tags.map((tag) {
                  return Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      color: Color(URLs().colorScheme).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '#$tag',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Color(URLs().colorScheme),
                      ),
                    ),
                  );
                }).toList(),
              ),
              SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    'View details',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Color(URLs().colorScheme),
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 12,
                    color: Color(URLs().colorScheme),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
} 