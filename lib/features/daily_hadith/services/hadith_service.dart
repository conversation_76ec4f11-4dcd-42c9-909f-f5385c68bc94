import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:new_islamic_app_uk/features/daily_hadith/models/hadith.dart';

class HadithService {
  static final HadithService _instance = HadithService._internal();
  
  factory HadithService() {
    return _instance;
  }
  
  HadithService._internal();
  
  final Dio _dio = Dio();
  final String _prefsKey = 'daily_hadith_data';
  final String _favoritesKey = 'favorite_hadiths';
  
  // Sample hadiths for offline use
  final List<Map<String, dynamic>> _sampleHadiths = [
    {
      'id': '1',
      'text': 'إنما الأعمال بالنيات وإنما لكل امرئ ما نوى',
      'translation': 'Actions are judged by intentions, and each person will be rewarded according to their intentions.',
      'explanation': 'This hadith emphasizes the importance of intention in Islam. It teaches that the value of our actions is determined by the intentions behind them. A seemingly good deed done with bad intentions loses its value, while even small acts done with pure intentions are greatly rewarded.',
      'reference': '<PERSON><PERSON><PERSON> 1, <PERSON><PERSON><PERSON> 1907',
      'narrator': '<PERSON><PERSON>',
      'tags': ['intention', 'deeds', 'reward'],
      'date': DateTime.now().toIso8601String(),
      'isFavorite': false,
    },
    {
      'id': '2',
      'text': 'من حسن إسلام المرء تركه ما لا يعنيه',
      'translation': 'Part of the perfection of one\'s Islam is leaving that which does not concern him.',
      'explanation': 'This hadith teaches Muslims to focus on what is beneficial and relevant to them, and to avoid interfering in matters that don\'t concern them. It encourages mindfulness and purposeful living.',
      'reference': 'Sunan al-Tirmidhi 2317',
      'narrator': 'Abu Hurayrah',
      'tags': ['focus', 'mindfulness', 'character'],
      'date': DateTime.now().subtract(Duration(days: 1)).toIso8601String(),
      'isFavorite': false,
    },
    {
      'id': '3',
      'text': 'لا يؤمن أحدكم حتى يحب لأخيه ما يحب لنفسه',
      'translation': 'None of you truly believes until he loves for his brother what he loves for himself.',
      'explanation': 'This hadith establishes a fundamental principle of Islamic ethics: empathy and love for others. It teaches that true faith is reflected in how we treat others and whether we wish for them the same good we desire for ourselves.',
      'reference': 'Sahih al-Bukhari 13, Sahih Muslim 45',
      'narrator': 'Anas ibn Malik',
      'tags': ['brotherhood', 'love', 'faith'],
      'date': DateTime.now().subtract(Duration(days: 2)).toIso8601String(),
      'isFavorite': false,
    },
    {
      'id': '4',
      'text': 'الدين النصيحة',
      'translation': 'The religion is sincerity/advice.',
      'explanation': 'This concise hadith emphasizes that at its core, Islam is about sincerity and good counsel. This includes sincerity to Allah, to His Book, to His Messenger, to the leaders of the Muslims, and to the common Muslims.',
      'reference': 'Sahih Muslim 55',
      'narrator': 'Tamim al-Dari',
      'tags': ['sincerity', 'advice', 'religion'],
      'date': DateTime.now().subtract(Duration(days: 3)).toIso8601String(),
      'isFavorite': false,
    },
    {
      'id': '5',
      'text': 'من كان يؤمن بالله واليوم الآخر فليقل خيرا أو ليصمت',
      'translation': 'Whoever believes in Allah and the Last Day, let him speak good or remain silent.',
      'explanation': 'This hadith teaches the importance of guarding our speech. It reminds believers that our words have consequences, and that speaking good or remaining silent is a sign of true faith.',
      'reference': 'Sahih al-Bukhari 6018, Sahih Muslim 47',
      'narrator': 'Abu Hurayrah',
      'tags': ['speech', 'silence', 'faith'],
      'date': DateTime.now().subtract(Duration(days: 4)).toIso8601String(),
      'isFavorite': false,
    },
  ];
  
  // Get today's hadith
  Future<Hadith> getDailyHadith() async {
    try {
      // First try to fetch from API (in a real app)
      // final response = await _dio.get('https://api.example.com/daily-hadith');
      // if (response.statusCode == 200) {
      //   final hadithData = response.data;
      //   final hadith = Hadith.fromJson(hadithData);
      //   _saveDailyHadith(hadith);
      //   return hadith;
      // }
      
      // If API fails or in this demo, use cached or sample data
      final prefs = await SharedPreferences.getInstance();
      final String? cachedData = prefs.getString(_prefsKey);
      
      if (cachedData != null) {
        final Map<String, dynamic> hadithData = json.decode(cachedData);
        final DateTime savedDate = DateTime.parse(hadithData['date']);
        final DateTime today = DateTime.now();
        
        // If the cached hadith is from today, use it
        if (savedDate.year == today.year && 
            savedDate.month == today.month && 
            savedDate.day == today.day) {
          return Hadith.fromJson(hadithData);
        }
      }
      
      // Otherwise, get a new hadith from sample data
      final int dayOfYear = DateTime.now().difference(DateTime(DateTime.now().year, 1, 1)).inDays;
      final int hadithIndex = dayOfYear % _sampleHadiths.length;
      
      final hadith = Hadith.fromJson(_sampleHadiths[hadithIndex]);
      
      // Update the date to today
      final updatedHadith = hadith.copyWith(date: DateTime.now());
      
      // Save to cache
      _saveDailyHadith(updatedHadith);
      
      return updatedHadith;
    } catch (e) {
      debugPrint('Error getting daily hadith: $e');
      
      // Fallback to first sample hadith if everything fails
      return Hadith.fromJson(_sampleHadiths[0]);
    }
  }
  
  // Save hadith to local storage
  Future<void> _saveDailyHadith(Hadith hadith) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_prefsKey, json.encode(hadith.toJson()));
    } catch (e) {
      debugPrint('Error saving daily hadith: $e');
    }
  }
  
  // Get favorite hadiths
  Future<List<Hadith>> getFavoriteHadiths() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? favoritesJson = prefs.getString(_favoritesKey);
      
      if (favoritesJson == null) {
        return [];
      }
      
      final List<dynamic> favoritesData = json.decode(favoritesJson);
      return favoritesData.map((data) => Hadith.fromJson(data)).toList();
    } catch (e) {
      debugPrint('Error getting favorite hadiths: $e');
      return [];
    }
  }
  
  // Toggle favorite status of a hadith
  Future<Hadith> toggleFavorite(Hadith hadith) async {
    try {
      final List<Hadith> favorites = await getFavoriteHadiths();
      final updatedHadith = hadith.copyWith(isFavorite: !hadith.isFavorite);
      
      if (updatedHadith.isFavorite) {
        // Add to favorites if not already there
        if (!favorites.contains(hadith)) {
          favorites.add(updatedHadith);
        }
      } else {
        // Remove from favorites
        favorites.removeWhere((h) => h.id == hadith.id);
      }
      
      // Save updated favorites list
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_favoritesKey, json.encode(favorites.map((h) => h.toJson()).toList()));
      
      // If this is today's hadith, update the cached daily hadith
      final String? cachedData = prefs.getString(_prefsKey);
      if (cachedData != null) {
        final Map<String, dynamic> cachedHadith = json.decode(cachedData);
        if (cachedHadith['id'] == hadith.id) {
          await _saveDailyHadith(updatedHadith);
        }
      }
      
      return updatedHadith;
    } catch (e) {
      debugPrint('Error toggling favorite status: $e');
      return hadith;
    }
  }
  
  // Get hadith history (last 7 days)
  Future<List<Hadith>> getRecentHadiths({int limit = 7}) async {
    try {
      // In a real app, you would fetch from an API
      // For this demo, we'll use sample data
      final List<Hadith> recentHadiths = [];
      
      for (int i = 0; i < limit && i < _sampleHadiths.length; i++) {
        final hadith = Hadith.fromJson(_sampleHadiths[i]);
        final updatedHadith = hadith.copyWith(
          date: DateTime.now().subtract(Duration(days: i)),
        );
        recentHadiths.add(updatedHadith);
      }
      
      return recentHadiths;
    } catch (e) {
      debugPrint('Error getting recent hadiths: $e');
      return [];
    }
  }
  
  // Search hadiths by text or tags
  Future<List<Hadith>> searchHadiths(String query) async {
    try {
      // In a real app, you would search via an API
      // For this demo, we'll search through sample data
      if (query.isEmpty) {
        return [];
      }
      
      final List<Hadith> results = [];
      final String lowercaseQuery = query.toLowerCase();
      
      for (final hadithData in _sampleHadiths) {
        final hadith = Hadith.fromJson(hadithData);
        
        if (hadith.text.toLowerCase().contains(lowercaseQuery) ||
            hadith.translation.toLowerCase().contains(lowercaseQuery) ||
            hadith.narrator.toLowerCase().contains(lowercaseQuery) ||
            hadith.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery))) {
          results.add(hadith);
        }
      }
      
      return results;
    } catch (e) {
      debugPrint('Error searching hadiths: $e');
      return [];
    }
  }
} 