import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../../URLs.dart';
import '../controllers/dua_journal_controller.dart';
import '../models/dua_entry.dart';
import '../widgets/category_selector.dart';
import '../widgets/status_selector.dart';
import '../widgets/tag_input.dart';
import 'components/answer_notes_section.dart';
import 'components/dua_detail_header.dart';
import 'components/reminder_section.dart';
import 'components/scripture_reference_section.dart';

class DuaJournalDetailScreen extends StatefulWidget {
  final String entryId;
  
  const DuaJournalDetailScreen({
    Key? key,
    required this.entryId,
  }) : super(key: key);

  @override
  _DuaJournalDetailScreenState createState() => _DuaJournalDetailScreenState();
}

class _DuaJournalDetailScreenState extends State<DuaJournalDetailScreen> {
  bool _isEditing = false;
  bool _isLoading = false;
  late DuaEntry _entry;
  
  // Controllers for editing
  final TextEditingController _duaTextController = TextEditingController();
  final TextEditingController _reflectionController = TextEditingController();
  final TextEditingController _answerNotesController = TextEditingController();
  final TextEditingController _scriptureReferenceController = TextEditingController();
  
  // State for editing
  late DuaStatus _selectedStatus;
  late DuaCategory _selectedCategory;
  late List<String> _selectedTags;
  late int _reminderFrequency;
  
  @override
  void initState() {
    super.initState();
    _loadEntry();
  }
  
  @override
  void dispose() {
    _duaTextController.dispose();
    _reflectionController.dispose();
    _answerNotesController.dispose();
    _scriptureReferenceController.dispose();
    super.dispose();
  }
  
  void _loadEntry() {
    final controller = Provider.of<DuaJournalController>(context, listen: false);
    final entry = controller.entries.firstWhere((e) => e.id == widget.entryId);
    
    setState(() {
      _entry = entry;
      _initializeControllers();
    });
  }
  
  void _initializeControllers() {
    _duaTextController.text = _entry.duaText;
    _reflectionController.text = _entry.reflection;
    _answerNotesController.text = _entry.answerNotes;
    _scriptureReferenceController.text = _entry.scriptureReference;
    
    _selectedStatus = _entry.status;
    _selectedCategory = _entry.category;
    _selectedTags = List.from(_entry.tags);
    _reminderFrequency = _entry.reminderFrequency;
  }
  
  void _toggleEdit() {
    setState(() {
      _isEditing = !_isEditing;
      if (!_isEditing) {
        _initializeControllers();
      }
    });
  }
  
  void _saveChanges() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final controller = Provider.of<DuaJournalController>(context, listen: false);
      
      await controller.updateEntry(
        id: _entry.id,
        duaText: _duaTextController.text,
        reflection: _reflectionController.text,
        status: _selectedStatus,
        category: _selectedCategory,
        tags: _selectedTags,
        answerNotes: _answerNotesController.text,
        reminderFrequency: _reminderFrequency,
        scriptureReference: _scriptureReferenceController.text,
      );
      
      // Refresh entry
      _loadEntry();
      
      setState(() {
        _isLoading = false;
        _isEditing = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Dua updated successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update dua: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  void _toggleFavorite() async {
    final controller = Provider.of<DuaJournalController>(context, listen: false);
    await controller.toggleFavorite(_entry.id);
    _loadEntry();
  }
  
  void _updateStatus(DuaStatus status) async {
    final controller = Provider.of<DuaJournalController>(context, listen: false);
    await controller.updateStatus(_entry.id, status);
    _loadEntry();
  }
  
  void _deleteDua() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Delete Dua',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Are you sure you want to delete this dua? This action cannot be undone.',
          style: GoogleFonts.poppins(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'Delete',
              style: GoogleFonts.poppins(
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });
      
      try {
        final controller = Provider.of<DuaJournalController>(context, listen: false);
        await controller.deleteEntry(_entry.id);
        
        Navigator.pop(context);
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete dua: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  void _shareDua() {
    final text = '''
${_entry.duaText}

${_entry.reflection.isNotEmpty ? 'Reflection: ${_entry.reflection}\n' : ''}
${_entry.scriptureReference.isNotEmpty ? 'Reference: ${_entry.scriptureReference}\n' : ''}
${_entry.tags.isNotEmpty ? 'Tags: ${_entry.tags.join(', ')}\n' : ''}
''';
    
    Share.share(text, subject: 'Dua from my Journal');
  }
  
  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Scaffold(
      backgroundColor: Color(urls.lightGray),
      appBar: AppBar(
        title: Text(
          _isEditing ? 'Edit Dua' : 'Dua Details',
          style: GoogleFonts.poppins(
            fontSize: 20.0,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Color(urls.primaryColor),
        elevation: 0,
        actions: [
          // Edit/Save button
          _isLoading
              ? Center(
                  child: SizedBox(
                    width: 20.0,
                    height: 20.0,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      strokeWidth: 2.0,
                    ),
                  ),
                )
              : _isEditing
                  ? TextButton(
                      onPressed: _saveChanges,
                      child: Text(
                        'Save',
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 16.0,
                        ),
                      ),
                    )
                  : IconButton(
                      icon: Icon(Icons.edit),
                      onPressed: _toggleEdit,
                    ),
          
          // More options
          if (!_isEditing)
            PopupMenuButton<String>(
              icon: Icon(Icons.more_vert),
              onSelected: (value) {
                switch (value) {
                  case 'share':
                    _shareDua();
                    break;
                  case 'copy':
                    Clipboard.setData(ClipboardData(text: _entry.duaText));
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Dua copied to clipboard'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                    break;
                  case 'delete':
                    _deleteDua();
                    break;
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'share',
                  child: Row(
                    children: [
                      Icon(Icons.share, color: Color(urls.textPrimary)),
                      SizedBox(width: 8.0),
                      Text(
                        'Share',
                        style: GoogleFonts.poppins(),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'copy',
                  child: Row(
                    children: [
                      Icon(Icons.content_copy, color: Color(urls.textPrimary)),
                      SizedBox(width: 8.0),
                      Text(
                        'Copy',
                        style: GoogleFonts.poppins(),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red),
                      SizedBox(width: 8.0),
                      Text(
                        'Delete',
                        style: GoogleFonts.poppins(
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  Color(urls.primaryColor),
                ),
              ),
            )
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with status, category, and date
                  DuaDetailHeader(
                    entry: _entry,
                    isEditing: _isEditing,
                    onStatusChange: _updateStatus,
                    onFavoriteToggle: _toggleFavorite,
                  ),
                  SizedBox(height: 24.0),
                  
                  // Dua text
                  Text(
                    'Dua',
                    style: GoogleFonts.poppins(
                      fontSize: 18.0,
                      fontWeight: FontWeight.w600,
                      color: Color(urls.textPrimary),
                    ),
                  ),
                  SizedBox(height: 8.0),
                  _isEditing
                      ? TextFormField(
                          controller: _duaTextController,
                          decoration: InputDecoration(
                            hintText: 'Write your dua here...',
                            hintStyle: GoogleFonts.poppins(
                              color: Color(urls.textSecondary),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                              borderSide: BorderSide(
                                color: Color(urls.mediumGray),
                              ),
                            ),
                          ),
                          style: GoogleFonts.poppins(
                            fontSize: 16.0,
                          ),
                          maxLines: 5,
                        )
                      : Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(16.0),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 4.0,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Text(
                            _entry.duaText,
                            style: GoogleFonts.poppins(
                              fontSize: 16.0,
                              color: Color(urls.textPrimary),
                            ),
                          ),
                        ),
                  SizedBox(height: 24.0),
                  
                  // Reflection
                  Text(
                    'Reflection',
                    style: GoogleFonts.poppins(
                      fontSize: 18.0,
                      fontWeight: FontWeight.w600,
                      color: Color(urls.textPrimary),
                    ),
                  ),
                  SizedBox(height: 8.0),
                  _isEditing
                      ? TextFormField(
                          controller: _reflectionController,
                          decoration: InputDecoration(
                            hintText: 'Add your thoughts, feelings, or context...',
                            hintStyle: GoogleFonts.poppins(
                              color: Color(urls.textSecondary),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                              borderSide: BorderSide(
                                color: Color(urls.mediumGray),
                              ),
                            ),
                          ),
                          style: GoogleFonts.poppins(
                            fontSize: 16.0,
                          ),
                          maxLines: 3,
                        )
                      : Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(16.0),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 4.0,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Text(
                            _entry.reflection.isEmpty
                                ? 'No reflection added'
                                : _entry.reflection,
                            style: GoogleFonts.poppins(
                              fontSize: 16.0,
                              color: _entry.reflection.isEmpty
                                  ? Color(urls.textSecondary)
                                  : Color(urls.textPrimary),
                              fontStyle: _entry.reflection.isEmpty
                                  ? FontStyle.italic
                                  : FontStyle.normal,
                            ),
                          ),
                        ),
                  SizedBox(height: 24.0),
                  
                  // Answer notes section (if status is answered or in progress)
                  if (_isEditing || _entry.status == DuaStatus.answered || _entry.status == DuaStatus.inProgress)
                    AnswerNotesSection(
                      entry: _entry,
                      isEditing: _isEditing,
                      controller: _answerNotesController,
                    ),
                  
                  // Scripture reference section
                  ScriptureReferenceSection(
                    entry: _entry,
                    isEditing: _isEditing,
                    controller: _scriptureReferenceController,
                  ),
                  
                  // Status selector (if editing)
                  if (_isEditing) ...[
                    Text(
                      'Status',
                      style: GoogleFonts.poppins(
                        fontSize: 18.0,
                        fontWeight: FontWeight.w600,
                        color: Color(urls.textPrimary),
                      ),
                    ),
                    SizedBox(height: 8.0),
                    StatusSelector(
                      selectedStatus: _selectedStatus,
                      onStatusSelected: (status) {
                        setState(() {
                          _selectedStatus = status;
                        });
                      },
                    ),
                    SizedBox(height: 24.0),
                  ],
                  
                  // Category selector (if editing)
                  if (_isEditing) ...[
                    Text(
                      'Category',
                      style: GoogleFonts.poppins(
                        fontSize: 18.0,
                        fontWeight: FontWeight.w600,
                        color: Color(urls.textPrimary),
                      ),
                    ),
                    SizedBox(height: 8.0),
                    CategorySelector(
                      selectedCategory: _selectedCategory,
                      onCategorySelected: (category) {
                        setState(() {
                          _selectedCategory = category;
                        });
                      },
                    ),
                    SizedBox(height: 24.0),
                  ],
                  
                  // Tags
                  Text(
                    'Tags',
                    style: GoogleFonts.poppins(
                      fontSize: 18.0,
                      fontWeight: FontWeight.w600,
                      color: Color(urls.textPrimary),
                    ),
                  ),
                  SizedBox(height: 8.0),
                  _isEditing
                      ? TagInput(
                          tags: _selectedTags,
                          onTagsChanged: (tags) {
                            setState(() {
                              _selectedTags = tags;
                            });
                          },
                          suggestedTags: Provider.of<DuaJournalController>(context, listen: false).getAllTags(),
                        )
                      : _entry.tags.isEmpty
                          ? Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(16.0),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4.0,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Text(
                                'No tags added',
                                style: GoogleFonts.poppins(
                                  fontSize: 16.0,
                                  color: Color(urls.textSecondary),
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            )
                          : Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(16.0),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4.0,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Wrap(
                                spacing: 8.0,
                                runSpacing: 8.0,
                                children: _entry.tags.map((tag) {
                                  return Container(
                                    padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
                                    decoration: BoxDecoration(
                                      color: Color(urls.primaryColor).withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(urls.defaultBorderRadius / 2),
                                      border: Border.all(
                                        color: Color(urls.primaryColor).withOpacity(0.3),
                                        width: 1.0,
                                      ),
                                    ),
                                    child: Text(
                                      tag,
                                      style: GoogleFonts.poppins(
                                        color: Color(urls.primaryColor),
                                        fontSize: 14.0,
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),
                  SizedBox(height: 24.0),
                  
                  // Reminder section
                  ReminderSection(
                    entry: _entry,
                    isEditing: _isEditing,
                    reminderFrequency: _reminderFrequency,
                    onReminderChanged: (value) {
                      setState(() {
                        _reminderFrequency = value;
                      });
                    },
                  ),
                  
                  SizedBox(height: 40.0),
                ],
              ),
            ),
    );
  }
} 