import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/dua_journal/screens/dua_journal_statistics_screen.dart';
import 'package:new_islamic_app_uk/features/dua_journal/widgets/category_selector.dart';
import 'package:new_islamic_app_uk/features/dua_journal/widgets/dua_entry_card.dart';
import 'package:new_islamic_app_uk/features/dua_journal/widgets/status_selector.dart';
import 'package:provider/provider.dart';
import '../../../theme/app_colors.dart';
import '../../../theme/app_text_styles.dart';
import '../controllers/dua_journal_controller.dart';
import '../models/dua_entry.dart';
import 'dua_journal_entry_screen.dart';
import 'dua_journal_detail_screen.dart';

class DuaJournalListScreen extends StatefulWidget {
  const DuaJournalListScreen({Key? key}) : super(key: key);

  @override
  State<DuaJournalListScreen> createState() => _DuaJournalListScreenState();
}

class _DuaJournalListScreenState extends State<DuaJournalListScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Initialize the controller
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<DuaJournalController>(context, listen: false).initialize();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    final controller = Provider.of<DuaJournalController>(context);

    return Scaffold(
      backgroundColor: Color(urls.lightGray),
      appBar: AppBar(
        title: _isSearching
            ? TextField(
                controller: _searchController,
                autofocus: true,
                decoration: InputDecoration(
                  hintText: 'Search duas...',
                  hintStyle: GoogleFonts.poppins(
                    color: Colors.white70,
                    fontSize: 16.0,
                  ),
                  border: InputBorder.none,
                ),
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: 16.0,
                ),
                onChanged: (value) {
                  controller.setSearchQuery(value);
                },
              )
            : Text(
                'Dua Journal',
                style: GoogleFonts.poppins(
                  fontSize: 20.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
        backgroundColor: Color(urls.primaryColor),
        elevation: 0,
        actions: [
          // Search button
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                  controller.setSearchQuery('');
                }
              });
            },
          ),

          // Statistics button
          IconButton(
            icon: Icon(Icons.bar_chart),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => DuaJournalStatisticsScreen(),
                ),
              );
            },
          ),

          // Filter button
          PopupMenuButton<String>(
            icon: Icon(Icons.filter_list),
            onSelected: (value) {
              if (value == 'clear_filters') {
                controller.clearFilters();
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'clear_filters',
                child: Text(
                  'Clear Filters',
                  style: GoogleFonts.poppins(),
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          indicatorWeight: 3.0,
          labelStyle: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
            fontSize: 14.0,
          ),
          unselectedLabelStyle: GoogleFonts.poppins(
            fontWeight: FontWeight.w500,
            fontSize: 14.0,
          ),
          tabs: [
            Tab(text: 'All Duas'),
            Tab(text: 'Favorites'),
          ],
        ),
      ),
      body: controller.isLoading
          ? Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  Color(urls.primaryColor),
                ),
              ),
            )
          : Column(
              children: [
                // Category selector
                CategorySelector(
                  selectedCategory: controller.selectedCategory,
                  onCategorySelected: (category) {
                    controller.setCategoryFilter(category);
                  },
                ),

                // Status selector
                if (controller.selectedStatus != null)
                  StatusSelector(
                    selectedStatus: controller.selectedStatus!,
                    onStatusSelected: (status) {
                      controller.setStatusFilter(status);
                    },
                  ),

                // Filter chips
                if (controller.selectedCategory != null || controller.selectedStatus != null || controller.searchQuery.isNotEmpty)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                    child: Wrap(
                      spacing: 8.0,
                      runSpacing: 8.0,
                      children: [
                        if (controller.selectedCategory != null)
                          Chip(
                            label: Text(
                              'Category: ${_getCategoryText(controller.selectedCategory!)}',
                              style: GoogleFonts.poppins(
                                fontSize: 12.0,
                              ),
                            ),
                            deleteIcon: Icon(Icons.close, size: 16.0),
                            onDeleted: () {
                              controller.setCategoryFilter(null);
                            },
                          ),
                        if (controller.selectedStatus != null)
                          Chip(
                            label: Text(
                              'Status: ${_getStatusText(controller.selectedStatus!)}',
                              style: GoogleFonts.poppins(
                                fontSize: 12.0,
                              ),
                            ),
                            deleteIcon: Icon(Icons.close, size: 16.0),
                            onDeleted: () {
                              controller.setStatusFilter(null);
                            },
                          ),
                        if (controller.searchQuery.isNotEmpty)
                          Chip(
                            label: Text(
                              'Search: ${controller.searchQuery}',
                              style: GoogleFonts.poppins(
                                fontSize: 12.0,
                              ),
                            ),
                            deleteIcon: Icon(Icons.close, size: 16.0),
                            onDeleted: () {
                              setState(() {
                                _searchController.clear();
                                _isSearching = false;
                                controller.setSearchQuery('');
                              });
                            },
                          ),
                      ],
                    ),
                  ),

                // Tab content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // All duas tab
                      _buildEntriesList(controller.filteredEntries, controller),

                      // Favorites tab
                      _buildEntriesList(controller.favoriteEntries, controller),
                    ],
                  ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => DuaJournalEntryScreen(),
            ),
          );
        },
        backgroundColor: Color(urls.primaryColor),
        child: Icon(Icons.add),
      ),
    );
  }

  Widget _buildEntriesList(List<DuaEntry> entries, DuaJournalController controller) {
    final urls = URLs();

    if (entries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.book,
              size: 64.0,
              color: Color(urls.textSecondary).withOpacity(0.5),
            ),
            SizedBox(height: 16.0),
            Text(
              'No duas found',
              style: GoogleFonts.poppins(
                fontSize: 18.0,
                fontWeight: FontWeight.w500,
                color: Color(urls.textSecondary),
              ),
            ),
            SizedBox(height: 8.0),
            Text(
              'Start adding your duas and reflections',
              style: GoogleFonts.poppins(
                fontSize: 14.0,
                color: Color(urls.textSecondary),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.only(bottom: 80.0),
      itemCount: entries.length,
      itemBuilder: (context, index) {
        final entry = entries[index];

        return DuaEntryCard(
          entry: entry,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => DuaJournalDetailScreen(entryId: entry.id),
              ),
            );
          },
          onFavoriteToggle: () {
            controller.toggleFavorite(entry.id);
          },
          onStatusChange: (status) {
            controller.updateStatus(entry.id, status);
          },
        );
      },
    );
  }

  String _getCategoryText(DuaCategory category) {
    switch (category) {
      case DuaCategory.personal:
        return 'Personal';
      case DuaCategory.family:
        return 'Family';
      case DuaCategory.health:
        return 'Health';
      case DuaCategory.career:
        return 'Career';
      case DuaCategory.education:
        return 'Education';
      case DuaCategory.spiritual:
        return 'Spiritual';
      case DuaCategory.community:
        return 'Community';
      case DuaCategory.financial:
        return 'Financial';
      case DuaCategory.travel:
        return 'Travel';
      case DuaCategory.other:
        return 'Other';
    }
  }

  String _getStatusText(DuaStatus status) {
    switch (status) {
      case DuaStatus.pending:
        return 'Pending';
      case DuaStatus.inProgress:
        return 'In Progress';
      case DuaStatus.answered:
        return 'Answered';
      case DuaStatus.reflection:
        return 'Reflection';
    }
  }
}