import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../URLs.dart';
import '../controllers/dua_journal_controller.dart';
import '../models/dua_entry.dart';
import 'components/statistics_card.dart';
import 'components/category_pie_chart.dart';
import 'components/status_bar_chart.dart';

class DuaJournalStatisticsScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    final controller = Provider.of<DuaJournalController>(context);
    final statistics = controller.getStatistics();
    
    return Scaffold(
      backgroundColor: Color(urls.lightGray),
      appBar: AppBar(
        title: Text(
          'Dua Statistics',
          style: GoogleFonts.poppins(
            fontSize: 20.0,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Color(urls.primaryColor),
        elevation: 0,
      ),
      body: controller.isLoading
          ? Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  Color(urls.primaryColor),
                ),
              ),
            )
          : controller.entries.isEmpty
              ? _buildEmptyState(context, urls)
              : SingleChildScrollView(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Summary cards
                      _buildSummaryCards(statistics, urls),
                      SizedBox(height: 24.0),
                      
                      // Status distribution
                      Text(
                        'Status Distribution',
                        style: GoogleFonts.poppins(
                          fontSize: 18.0,
                          fontWeight: FontWeight.w600,
                          color: Color(urls.textPrimary),
                        ),
                      ),
                      SizedBox(height: 16.0),
                      StatusBarChart(statistics: statistics),
                      SizedBox(height: 24.0),
                      
                      // Category distribution
                      Text(
                        'Category Distribution',
                        style: GoogleFonts.poppins(
                          fontSize: 18.0,
                          fontWeight: FontWeight.w600,
                          color: Color(urls.textPrimary),
                        ),
                      ),
                      SizedBox(height: 16.0),
                      CategoryPieChart(statistics: statistics),
                      SizedBox(height: 24.0),
                      
                      // Most used tags
                      Text(
                        'Most Used Tags',
                        style: GoogleFonts.poppins(
                          fontSize: 18.0,
                          fontWeight: FontWeight.w600,
                          color: Color(urls.textPrimary),
                        ),
                      ),
                      SizedBox(height: 16.0),
                      _buildTagsCloud(controller, urls),
                      SizedBox(height: 24.0),
                      
                      // Answered vs Pending
                      if (statistics['totalEntries'] > 0)
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(16.0),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10.0,
                                offset: Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Answered vs Pending',
                                style: GoogleFonts.poppins(
                                  fontSize: 16.0,
                                  fontWeight: FontWeight.w600,
                                  color: Color(urls.textPrimary),
                                ),
                              ),
                              SizedBox(height: 16.0),
                              LinearProgressIndicator(
                                value: statistics['answeredEntries'] / statistics['totalEntries'],
                                backgroundColor: Color(urls.lightGray),
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Color(urls.successColor),
                                ),
                                minHeight: 10.0,
                              ),
                              SizedBox(height: 8.0),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    '${statistics['answeredPercentage']}% Answered',
                                    style: GoogleFonts.poppins(
                                      fontSize: 14.0,
                                      color: Color(urls.successColor),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    '${statistics['answeredEntries']} of ${statistics['totalEntries']}',
                                    style: GoogleFonts.poppins(
                                      fontSize: 14.0,
                                      color: Color(urls.textSecondary),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      SizedBox(height: 40.0),
                    ],
                  ),
                ),
    );
  }
  
  Widget _buildEmptyState(BuildContext context, URLs urls) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bar_chart,
            size: 64.0,
            color: Color(urls.textSecondary).withOpacity(0.5),
          ),
          SizedBox(height: 16.0),
          Text(
            'No statistics available',
            style: GoogleFonts.poppins(
              fontSize: 18.0,
              fontWeight: FontWeight.w500,
              color: Color(urls.textSecondary),
            ),
          ),
          SizedBox(height: 8.0),
          Text(
            'Start adding duas to see statistics',
            style: GoogleFonts.poppins(
              fontSize: 14.0,
              color: Color(urls.textSecondary),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.0),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: Icon(Icons.add),
            label: Text(
              'Add Dua',
              style: GoogleFonts.poppins(),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(urls.primaryColor),
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSummaryCards(Map<String, dynamic> statistics, URLs urls) {
    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16.0,
      mainAxisSpacing: 16.0,
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      children: [
        StatisticsCard(
          title: 'Total Duas',
          value: '${statistics['totalEntries']}',
          icon: Icons.book,
          color: Color(urls.primaryColor),
          urls: urls,
        ),
        StatisticsCard(
          title: 'Answered',
          value: '${statistics['answeredEntries']}',
          icon: Icons.check_circle,
          color: Color(urls.successColor),
          urls: urls,
        ),
        StatisticsCard(
          title: 'In Progress',
          value: '${statistics['inProgressEntries']}',
          icon: Icons.autorenew,
          color: Color(urls.infoColor),
          urls: urls,
        ),
        StatisticsCard(
          title: 'Pending',
          value: '${statistics['pendingEntries']}',
          icon: Icons.hourglass_empty,
          color: Color(urls.warningColor),
          urls: urls,
        ),
      ],
    );
  }
  
  Widget _buildTagsCloud(DuaJournalController controller, URLs urls) {
    final allTags = controller.getAllTags();
    
    if (allTags.isEmpty) {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10.0,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Text(
          'No tags used yet',
          style: GoogleFonts.poppins(
            fontSize: 14.0,
            color: Color(urls.textSecondary),
            fontStyle: FontStyle.italic,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.0,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Wrap(
        spacing: 8.0,
        runSpacing: 8.0,
        children: allTags.map((tag) {
          // Count occurrences of this tag
          int count = 0;
          for (final entry in controller.entries) {
            if (entry.tags.contains(tag)) {
              count++;
            }
          }
          
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
            decoration: BoxDecoration(
              color: Color(urls.primaryColor).withOpacity(0.1),
              borderRadius: BorderRadius.circular(urls.defaultBorderRadius / 2),
              border: Border.all(
                color: Color(urls.primaryColor).withOpacity(0.3),
                width: 1.0,
              ),
            ),
            child: Text(
              '$tag ($count)',
              style: GoogleFonts.poppins(
                color: Color(urls.primaryColor),
                fontSize: 14.0,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
} 