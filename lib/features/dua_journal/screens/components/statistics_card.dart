import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../URLs.dart';

class StatisticsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final URLs urls;
  
  const StatisticsCard({
    Key? key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    required this.urls,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.0,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 28.0,
            ),
          ),
          SizedBox(height: 12.0),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 24.0,
              fontWeight: FontWeight.w600,
              color: Color(urls.textPrimary),
            ),
          ),
          SizedBox(height: 4.0),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 14.0,
              color: Color(urls.textSecondary),
            ),
          ),
        ],
      ),
    );
  }
} 