import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../URLs.dart';
import '../../models/dua_entry.dart';

class DuaDetailHeader extends StatelessWidget {
  final DuaEntry entry;
  final bool isEditing;
  final Function(DuaStatus) onStatusChange;
  final Function() onFavoriteToggle;
  
  const DuaDetailHeader({
    Key? key,
    required this.entry,
    required this.isEditing,
    required this.onStatusChange,
    required this.onFavoriteToggle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    if (isEditing) {
      return SizedBox.shrink();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Status and category
        Row(
          children: [
            // Status chip
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
              decoration: BoxDecoration(
                color: entry.getStatusColor(),
                borderRadius: BorderRadius.circular(urls.defaultBorderRadius / 2),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    entry.getStatusIcon(),
                    color: Colors.white,
                    size: 16.0,
                  ),
                  SizedBox(width: 4.0),
                  Text(
                    entry.getStatusText(),
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      fontSize: 14.0,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 8.0),
            
            // Category chip
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
              decoration: BoxDecoration(
                color: entry.getCategoryColor(),
                borderRadius: BorderRadius.circular(urls.defaultBorderRadius / 2),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    entry.getCategoryIcon(),
                    color: Colors.white,
                    size: 16.0,
                  ),
                  SizedBox(width: 4.0),
                  Text(
                    entry.getCategoryText(),
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      fontSize: 14.0,
                    ),
                  ),
                ],
              ),
            ),
            
            Spacer(),
            
            // Favorite button
            IconButton(
              icon: Icon(
                entry.isFavorite ? Icons.favorite : Icons.favorite_border,
                color: entry.isFavorite ? Color(urls.errorColor) : Color(urls.textSecondary),
                size: 24.0,
              ),
              onPressed: onFavoriteToggle,
            ),
          ],
        ),
        SizedBox(height: 16.0),
        
        // Date information
        Container(
          padding: EdgeInsets.all(12.0),
          decoration: BoxDecoration(
            color: Color(urls.lightGray),
            borderRadius: BorderRadius.circular(urls.defaultBorderRadius / 2),
            border: Border.all(
              color: Color(urls.mediumGray),
              width: 1.0,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16.0,
                    color: Color(urls.textSecondary),
                  ),
                  SizedBox(width: 8.0),
                  Text(
                    'Created: ${entry.formattedCreatedDate}',
                    style: GoogleFonts.poppins(
                      fontSize: 14.0,
                      color: Color(urls.textSecondary),
                    ),
                  ),
                ],
              ),
              
              if (entry.status == DuaStatus.answered && entry.answeredDate != null) ...[
                SizedBox(height: 4.0),
                Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      size: 16.0,
                      color: Color(urls.successColor),
                    ),
                    SizedBox(width: 8.0),
                    Text(
                      'Answered: ${entry.formattedAnsweredDate}',
                      style: GoogleFonts.poppins(
                        fontSize: 14.0,
                        color: Color(urls.successColor),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
        
        // Status change button
        if (!isEditing)
          Padding(
            padding: EdgeInsets.only(top: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                OutlinedButton.icon(
                  onPressed: () {
                    _showStatusChangeDialog(context);
                  },
                  icon: Icon(Icons.update),
                  label: Text(
                    'Change Status',
                    style: GoogleFonts.poppins(),
                  ),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Color(urls.primaryColor),
                    side: BorderSide(color: Color(urls.primaryColor)),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(urls.defaultBorderRadius / 2),
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
  
  void _showStatusChangeDialog(BuildContext context) {
    final urls = URLs();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Change Status',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: DuaStatus.values.map((status) {
            final isCurrentStatus = status == entry.status;
            
            return ListTile(
              leading: Icon(
                _getStatusIcon(status),
                color: _getStatusColor(status, urls),
              ),
              title: Text(
                _getStatusText(status),
                style: GoogleFonts.poppins(),
              ),
              selected: isCurrentStatus,
              selectedTileColor: isCurrentStatus ? Color(urls.lightGray) : null,
              onTap: isCurrentStatus
                  ? null
                  : () {
                      Navigator.pop(context);
                      onStatusChange(status);
                    },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(),
            ),
          ),
        ],
      ),
    );
  }
  
  Color _getStatusColor(DuaStatus status, URLs urls) {
    switch (status) {
      case DuaStatus.pending:
        return Color(urls.warningColor);
      case DuaStatus.inProgress:
        return Color(urls.infoColor);
      case DuaStatus.answered:
        return Color(urls.successColor);
      case DuaStatus.reflection:
        return Color(0xFF9C27B0); // Purple
    }
  }
  
  IconData _getStatusIcon(DuaStatus status) {
    switch (status) {
      case DuaStatus.pending:
        return Icons.hourglass_empty;
      case DuaStatus.inProgress:
        return Icons.autorenew;
      case DuaStatus.answered:
        return Icons.check_circle;
      case DuaStatus.reflection:
        return Icons.self_improvement;
    }
  }
  
  String _getStatusText(DuaStatus status) {
    switch (status) {
      case DuaStatus.pending:
        return 'Pending';
      case DuaStatus.inProgress:
        return 'In Progress';
      case DuaStatus.answered:
        return 'Answered';
      case DuaStatus.reflection:
        return 'Reflection';
    }
  }
} 