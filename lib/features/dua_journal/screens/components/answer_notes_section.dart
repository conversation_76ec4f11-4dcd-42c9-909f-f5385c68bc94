import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../URLs.dart';
import '../../models/dua_entry.dart';

class AnswerNotesSection extends StatelessWidget {
  final DuaEntry entry;
  final bool isEditing;
  final TextEditingController controller;
  
  const AnswerNotesSection({
    Key? key,
    required this.entry,
    required this.isEditing,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Answer Notes',
          style: GoogleFonts.poppins(
            fontSize: 18.0,
            fontWeight: FontWeight.w600,
            color: Color(urls.textPrimary),
          ),
        ),
        SizedBox(height: 8.0),
        isEditing
            ? TextFormField(
                controller: controller,
                decoration: InputDecoration(
                  hintText: 'Add notes about how this dua was answered...',
                  hintStyle: GoogleFonts.poppins(
                    color: Color(urls.textSecondary),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                    borderSide: BorderSide(
                      color: Color(urls.mediumGray),
                    ),
                  ),
                ),
                style: GoogleFonts.poppins(
                  fontSize: 16.0,
                ),
                maxLines: 3,
              )
            : Container(
                width: double.infinity,
                padding: EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 4.0,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (entry.status == DuaStatus.answered)
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                        margin: EdgeInsets.only(bottom: 8.0),
                        decoration: BoxDecoration(
                          color: Color(urls.successColor).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(urls.defaultBorderRadius / 2),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: Color(urls.successColor),
                              size: 16.0,
                            ),
                            SizedBox(width: 4.0),
                            Text(
                              'Answered',
                              style: GoogleFonts.poppins(
                                color: Color(urls.successColor),
                                fontWeight: FontWeight.w500,
                                fontSize: 12.0,
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (entry.status == DuaStatus.inProgress)
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                        margin: EdgeInsets.only(bottom: 8.0),
                        decoration: BoxDecoration(
                          color: Color(urls.infoColor).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(urls.defaultBorderRadius / 2),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.autorenew,
                              color: Color(urls.infoColor),
                              size: 16.0,
                            ),
                            SizedBox(width: 4.0),
                            Text(
                              'In Progress',
                              style: GoogleFonts.poppins(
                                color: Color(urls.infoColor),
                                fontWeight: FontWeight.w500,
                                fontSize: 12.0,
                              ),
                            ),
                          ],
                        ),
                      ),
                    Text(
                      entry.answerNotes.isEmpty
                          ? 'No answer notes added'
                          : entry.answerNotes,
                      style: GoogleFonts.poppins(
                        fontSize: 16.0,
                        color: entry.answerNotes.isEmpty
                            ? Color(urls.textSecondary)
                            : Color(urls.textPrimary),
                        fontStyle: entry.answerNotes.isEmpty
                            ? FontStyle.italic
                            : FontStyle.normal,
                      ),
                    ),
                  ],
                ),
              ),
        SizedBox(height: 24.0),
      ],
    );
  }
} 