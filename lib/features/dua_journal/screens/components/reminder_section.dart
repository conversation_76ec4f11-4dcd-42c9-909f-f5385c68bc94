import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../URLs.dart';
import '../../models/dua_entry.dart';

class ReminderSection extends StatelessWidget {
  final DuaEntry entry;
  final bool isEditing;
  final int reminderFrequency;
  final Function(int) onReminderChanged;
  
  const ReminderSection({
    Key? key,
    required this.entry,
    required this.isEditing,
    required this.reminderFrequency,
    required this.onReminderChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Reminder',
          style: GoogleFonts.poppins(
            fontSize: 18.0,
            fontWeight: FontWeight.w600,
            color: Color(urls.textPrimary),
          ),
        ),
        SizedBox(height: 8.0),
        isEditing
            ? Container(
                padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                  border: Border.all(
                    color: Color(urls.mediumGray),
                  ),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<int>(
                    value: reminderFrequency,
                    isExpanded: true,
                    icon: Icon(Icons.arrow_drop_down),
                    style: GoogleFonts.poppins(
                      color: Color(urls.textPrimary),
                      fontSize: 16.0,
                    ),
                    onChanged: (value) {
                      if (value != null) {
                        onReminderChanged(value);
                      }
                    },
                    items: [
                      DropdownMenuItem(
                        value: 0,
                        child: Text('No reminder'),
                      ),
                      DropdownMenuItem(
                        value: 1,
                        child: Text('Daily'),
                      ),
                      DropdownMenuItem(
                        value: 7,
                        child: Text('Weekly'),
                      ),
                      DropdownMenuItem(
                        value: 30,
                        child: Text('Monthly'),
                      ),
                    ],
                  ),
                ),
              )
            : Container(
                width: double.infinity,
                padding: EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 4.0,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Icon(
                      _getReminderIcon(),
                      color: entry.reminderFrequency > 0
                          ? Color(urls.primaryColor)
                          : Color(urls.textSecondary),
                      size: 20.0,
                    ),
                    SizedBox(width: 12.0),
                    Text(
                      _getReminderText(),
                      style: GoogleFonts.poppins(
                        fontSize: 16.0,
                        color: entry.reminderFrequency > 0
                            ? Color(urls.textPrimary)
                            : Color(urls.textSecondary),
                        fontStyle: entry.reminderFrequency > 0
                            ? FontStyle.normal
                            : FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
      ],
    );
  }
  
  IconData _getReminderIcon() {
    if (entry.reminderFrequency == 0) {
      return Icons.notifications_off;
    } else if (entry.reminderFrequency == 1) {
      return Icons.notifications_active;
    } else if (entry.reminderFrequency == 7) {
      return Icons.calendar_today;
    } else {
      return Icons.event;
    }
  }
  
  String _getReminderText() {
    switch (entry.reminderFrequency) {
      case 0:
        return 'No reminder set';
      case 1:
        return 'Daily reminder';
      case 7:
        return 'Weekly reminder';
      case 30:
        return 'Monthly reminder';
      default:
        return 'Custom reminder';
    }
  }
} 