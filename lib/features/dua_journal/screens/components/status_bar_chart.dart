import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../URLs.dart';
import '../../models/dua_entry.dart';

class StatusBarChart extends StatelessWidget {
  final Map<String, dynamic> statistics;
  
  const StatusBarChart({
    Key? key,
    required this.statistics,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    // Get status counts
    final pendingCount = statistics['pendingEntries'] ?? 0;
    final inProgressCount = statistics['inProgressEntries'] ?? 0;
    final answeredCount = statistics['answeredEntries'] ?? 0;
    final reflectionCount = statistics['reflectionEntries'] ?? 0;
    
    // Calculate max value for scaling
    final maxCount = [pendingCount, inProgressCount, answeredCount, reflectionCount]
        .reduce((value, element) => value > element ? value : element);
    
    if (maxCount == 0) {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10.0,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Center(
          child: Text(
            'No status data available',
            style: GoogleFonts.poppins(
              fontSize: 14.0,
              color: Color(urls.textSecondary),
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.0,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Bar chart
          Container(
            height: 200,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                _buildBar(
                  'Pending',
                  pendingCount,
                  maxCount,
                  Color(urls.warningColor),
                  urls,
                ),
                _buildBar(
                  'In Progress',
                  inProgressCount,
                  maxCount,
                  Color(urls.infoColor),
                  urls,
                ),
                _buildBar(
                  'Answered',
                  answeredCount,
                  maxCount,
                  Color(urls.successColor),
                  urls,
                ),
                _buildBar(
                  'Reflection',
                  reflectionCount,
                  maxCount,
                  Color(0xFF9C27B0), // Purple
                  urls,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildBar(
    String label,
    int value,
    int maxValue,
    Color color,
    URLs urls,
  ) {
    // Calculate height percentage (max height is 160)
    final double heightPercentage = maxValue > 0 ? (value / maxValue) : 0;
    final double barHeight = heightPercentage * 160;
    
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // Value label
        Text(
          '$value',
          style: GoogleFonts.poppins(
            fontSize: 16.0,
            fontWeight: FontWeight.w600,
            color: Color(urls.textPrimary),
          ),
        ),
        SizedBox(height: 8.0),
        
        // Bar
        Container(
          width: 40.0,
          height: barHeight > 0 ? barHeight : 2.0, // Minimum height for visibility
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(urls.defaultBorderRadius / 2),
            ),
          ),
        ),
        SizedBox(height: 8.0),
        
        // Status label
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 12.0,
            color: Color(urls.textSecondary),
          ),
        ),
      ],
    );
  }
} 