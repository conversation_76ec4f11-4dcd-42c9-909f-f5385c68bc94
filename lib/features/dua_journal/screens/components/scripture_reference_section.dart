import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../URLs.dart';
import '../../models/dua_entry.dart';

class ScriptureReferenceSection extends StatelessWidget {
  final DuaEntry entry;
  final bool isEditing;
  final TextEditingController controller;
  
  const ScriptureReferenceSection({
    Key? key,
    required this.entry,
    required this.isEditing,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Scripture Reference',
          style: GoogleFonts.poppins(
            fontSize: 18.0,
            fontWeight: FontWeight.w600,
            color: Color(urls.textPrimary),
          ),
        ),
        SizedBox(height: 8.0),
        isEditing
            ? TextFormField(
                controller: controller,
                decoration: InputDecoration(
                  hintText: 'e.g., Quran 2:186 or Hadith reference',
                  hintStyle: GoogleFonts.poppins(
                    color: Color(urls.textSecondary),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                    borderSide: BorderSide(
                      color: Color(urls.mediumGray),
                    ),
                  ),
                ),
                style: GoogleFonts.poppins(
                  fontSize: 16.0,
                ),
              )
            : Container(
                width: double.infinity,
                padding: EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 4.0,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.menu_book,
                      color: Color(urls.primaryColor),
                      size: 20.0,
                    ),
                    SizedBox(width: 12.0),
                    Expanded(
                      child: Text(
                        entry.scriptureReference.isEmpty
                            ? 'No scripture reference added'
                            : entry.scriptureReference,
                        style: GoogleFonts.poppins(
                          fontSize: 16.0,
                          color: entry.scriptureReference.isEmpty
                              ? Color(urls.textSecondary)
                              : Color(urls.textPrimary),
                          fontStyle: entry.scriptureReference.isEmpty
                              ? FontStyle.italic
                              : FontStyle.normal,
                        ),
                      ),
                    ),
                    if (entry.scriptureReference.isNotEmpty)
                      IconButton(
                        icon: Icon(
                          Icons.open_in_new,
                          color: Color(urls.primaryColor),
                          size: 20.0,
                        ),
                        onPressed: () {
                          // Open Quran or Hadith reference in app
                          // This would be implemented based on app's navigation
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Opening reference in Quran/Hadith section'),
                              duration: Duration(seconds: 2),
                            ),
                          );
                        },
                      ),
                  ],
                ),
              ),
        SizedBox(height: 24.0),
      ],
    );
  }
} 