import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../URLs.dart';
import '../../models/dua_entry.dart';

class Category<PERSON>ie<PERSON>hart extends StatelessWidget {
  final Map<String, dynamic> statistics;
  
  const CategoryPieChart({
    Key? key,
    required this.statistics,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    final Map<DuaCategory, int> entriesByCategory = 
        Map<DuaCategory, int>.from(statistics['entriesByCategory'] ?? {});
    
    // Filter out categories with 0 entries
    final nonZeroCategories = entriesByCategory.entries
        .where((entry) => entry.value > 0)
        .toList();
    
    if (nonZeroCategories.isEmpty) {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10.0,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Center(
          child: Text(
            'No category data available',
            style: GoogleFonts.poppins(
              fontSize: 14.0,
              color: Color(urls.textSecondary),
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.0,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Simple pie chart representation using circular segments
          Container(
            height: 200,
            child: CustomPaint(
              size: Size(200, 200),
              painter: PieChartPainter(
                categories: nonZeroCategories.map((e) => e.key).toList(),
                values: nonZeroCategories.map((e) => e.value).toList(),
              ),
            ),
          ),
          SizedBox(height: 16.0),
          
          // Legend
          Wrap(
            spacing: 16.0,
            runSpacing: 8.0,
            alignment: WrapAlignment.center,
            children: nonZeroCategories.map((entry) {
              final category = entry.key;
              final count = entry.value;
              final total = statistics['totalEntries'] ?? 1;
              final percentage = (count / total * 100).toStringAsFixed(1);
              
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 16.0,
                    height: 16.0,
                    decoration: BoxDecoration(
                      color: _getCategoryColor(category),
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: 8.0),
                  Text(
                    '${_getCategoryText(category)}: $percentage%',
                    style: GoogleFonts.poppins(
                      fontSize: 12.0,
                      color: Color(urls.textPrimary),
                    ),
                  ),
                ],
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
  
  String _getCategoryText(DuaCategory category) {
    switch (category) {
      case DuaCategory.personal:
        return 'Personal';
      case DuaCategory.family:
        return 'Family';
      case DuaCategory.health:
        return 'Health';
      case DuaCategory.career:
        return 'Career';
      case DuaCategory.education:
        return 'Education';
      case DuaCategory.spiritual:
        return 'Spiritual';
      case DuaCategory.community:
        return 'Community';
      case DuaCategory.financial:
        return 'Financial';
      case DuaCategory.travel:
        return 'Travel';
      case DuaCategory.other:
        return 'Other';
    }
  }
  
  Color _getCategoryColor(DuaCategory category) {
    switch (category) {
      case DuaCategory.personal:
        return Color(0xFF1A237E); // Deep Indigo
      case DuaCategory.family:
        return Color(0xFF00BFA5); // Teal
      case DuaCategory.health:
        return Color(0xFF00C853); // Green
      case DuaCategory.career:
        return Color(0xFF6200EA); // Deep Purple
      case DuaCategory.education:
        return Color(0xFF2979FF); // Blue
      case DuaCategory.spiritual:
        return Color(0xFFFFD700); // Gold
      case DuaCategory.community:
        return Color(0xFFFF6D00); // Orange
      case DuaCategory.financial:
        return Color(0xFF00B0FF); // Light Blue
      case DuaCategory.travel:
        return Color(0xFF3D5AFE); // Indigo
      case DuaCategory.other:
        return Color(0xFF757575); // Gray
    }
  }
}

class PieChartPainter extends CustomPainter {
  final List<DuaCategory> categories;
  final List<int> values;
  
  PieChartPainter({
    required this.categories,
    required this.values,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    
    // Calculate total value
    final total = values.fold<int>(0, (sum, value) => sum + value);
    
    // Draw pie segments
    double startAngle = -90 * (3.14159 / 180); // Start from top (in radians)
    
    for (int i = 0; i < categories.length; i++) {
      final sweepAngle = (values[i] / total) * 2 * 3.14159;
      
      final paint = Paint()
        ..color = _getCategoryColor(categories[i])
        ..style = PaintingStyle.fill;
      
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );
      
      startAngle += sweepAngle;
    }
    
    // Draw center circle (optional, for donut chart effect)
    canvas.drawCircle(
      center,
      radius * 0.5,
      Paint()..color = Colors.white,
    );
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
  
  Color _getCategoryColor(DuaCategory category) {
    switch (category) {
      case DuaCategory.personal:
        return Color(0xFF1A237E); // Deep Indigo
      case DuaCategory.family:
        return Color(0xFF00BFA5); // Teal
      case DuaCategory.health:
        return Color(0xFF00C853); // Green
      case DuaCategory.career:
        return Color(0xFF6200EA); // Deep Purple
      case DuaCategory.education:
        return Color(0xFF2979FF); // Blue
      case DuaCategory.spiritual:
        return Color(0xFFFFD700); // Gold
      case DuaCategory.community:
        return Color(0xFFFF6D00); // Orange
      case DuaCategory.financial:
        return Color(0xFF00B0FF); // Light Blue
      case DuaCategory.travel:
        return Color(0xFF3D5AFE); // Indigo
      case DuaCategory.other:
        return Color(0xFF757575); // Gray
    }
  }
} 