import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../../URLs.dart';
import '../controllers/dua_journal_controller.dart';
import '../models/dua_entry.dart';
import '../models/dua_suggestion.dart';
import '../widgets/category_selector.dart';
import '../widgets/status_selector.dart';
import '../widgets/tag_input.dart';
import '../widgets/dua_suggestion_card.dart';

class DuaJournalEntryScreen extends StatefulWidget {
  @override
  _DuaJournalEntryScreenState createState() => _DuaJournalEntryScreenState();
}

class _DuaJournalEntryScreenState extends State<DuaJournalEntryScreen> {
  final _formKey = GlobalKey<FormState>();
  
  final TextEditingController _duaTextController = TextEditingController();
  final TextEditingController _reflectionController = TextEditingController();
  final TextEditingController _scriptureReferenceController = TextEditingController();
  
  DuaStatus _selectedStatus = DuaStatus.pending;
  DuaCategory _selectedCategory = DuaCategory.personal;
  List<String> _selectedTags = [];
  int _reminderFrequency = 0;
  
  bool _isLoading = false;
  bool _showSuggestions = false;
  
  @override
  void dispose() {
    _duaTextController.dispose();
    _reflectionController.dispose();
    _scriptureReferenceController.dispose();
    super.dispose();
  }
  
  void _saveEntry() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });
      
      try {
        await Provider.of<DuaJournalController>(context, listen: false).addEntry(
          duaText: _duaTextController.text,
          reflection: _reflectionController.text,
          status: _selectedStatus,
          category: _selectedCategory,
          tags: _selectedTags,
          reminderFrequency: _reminderFrequency,
          scriptureReference: _scriptureReferenceController.text,
        );
        
        Navigator.pop(context);
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save dua: $e'),
            backgroundColor: Colors.red,
          ),
        );
        
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  
  void _useSuggestion(DuaSuggestion suggestion) {
    setState(() {
      _duaTextController.text = suggestion.translation;
      _scriptureReferenceController.text = '${suggestion.source}: ${suggestion.reference}';
      _selectedCategory = suggestion.category;
      _selectedTags = List.from(suggestion.tags);
      _showSuggestions = false;
    });
  }
  
  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    final controller = Provider.of<DuaJournalController>(context, listen: false);
    
    return Scaffold(
      backgroundColor: Color(urls.lightGray),
      appBar: AppBar(
        title: Text(
          'New Dua Entry',
          style: GoogleFonts.poppins(
            fontSize: 20.0,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Color(urls.primaryColor),
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveEntry,
            child: Text(
              'Save',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 16.0,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Color(urls.primaryColor),
                    ),
                  ),
                  SizedBox(height: 16.0),
                  Text(
                    'Saving your dua entry...',
                    style: GoogleFonts.poppins(
                      fontSize: 16.0,
                      color: Color(urls.textSecondary),
                    ),
                  ),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Date display
                    Text(
                      'Today, ${DateTime.now().toString().substring(0, 10)}',
                      style: GoogleFonts.poppins(
                        fontSize: 16.0,
                        color: Color(urls.textSecondary),
                      ),
                    ),
                    SizedBox(height: 24.0),
                    
                    // Dua text input
                    Text(
                      'Your Dua',
                      style: GoogleFonts.poppins(
                        fontSize: 16.0,
                        fontWeight: FontWeight.w600,
                        color: Color(urls.textPrimary),
                      ),
                    ),
                    SizedBox(height: 8.0),
                    TextFormField(
                      controller: _duaTextController,
                      decoration: InputDecoration(
                        hintText: 'Write your dua here...',
                        hintStyle: GoogleFonts.poppins(
                          color: Color(urls.textSecondary),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                          borderSide: BorderSide(
                            color: Color(urls.mediumGray),
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                          borderSide: BorderSide(
                            color: Color(urls.mediumGray),
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                          borderSide: BorderSide(
                            color: Color(urls.primaryColor),
                          ),
                        ),
                      ),
                      style: GoogleFonts.poppins(
                        fontSize: 16.0,
                      ),
                      maxLines: 5,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your dua';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 16.0),
                    
                    // Get inspiration button
                    Center(
                      child: TextButton.icon(
                        onPressed: () {
                          setState(() {
                            _showSuggestions = !_showSuggestions;
                          });
                        },
                        icon: Icon(
                          _showSuggestions ? Icons.expand_less : Icons.lightbulb_outline,
                          color: Color(urls.primaryColor),
                        ),
                        label: Text(
                          _showSuggestions ? 'Hide Suggestions' : 'Get Inspiration',
                          style: GoogleFonts.poppins(
                            color: Color(urls.primaryColor),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    
                    // Dua suggestions
                    if (_showSuggestions) ...[
                      SizedBox(height: 8.0),
                      Container(
                        height: 300,
                        child: ListView(
                          scrollDirection: Axis.horizontal,
                          children: [
                            ...sampleDuaSuggestions.map((suggestion) {
                              return Container(
                                width: MediaQuery.of(context).size.width * 0.8,
                                child: DuaSuggestionCard(
                                  suggestion: suggestion,
                                  onUse: _useSuggestion,
                                ),
                              );
                            }).toList(),
                          ],
                        ),
                      ),
                      SizedBox(height: 16.0),
                    ],
                    
                    // Reflection input
                    Text(
                      'Reflection (Optional)',
                      style: GoogleFonts.poppins(
                        fontSize: 16.0,
                        fontWeight: FontWeight.w600,
                        color: Color(urls.textPrimary),
                      ),
                    ),
                    SizedBox(height: 8.0),
                    TextFormField(
                      controller: _reflectionController,
                      decoration: InputDecoration(
                        hintText: 'Add your thoughts, feelings, or context...',
                        hintStyle: GoogleFonts.poppins(
                          color: Color(urls.textSecondary),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                          borderSide: BorderSide(
                            color: Color(urls.mediumGray),
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                          borderSide: BorderSide(
                            color: Color(urls.mediumGray),
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                          borderSide: BorderSide(
                            color: Color(urls.primaryColor),
                          ),
                        ),
                      ),
                      style: GoogleFonts.poppins(
                        fontSize: 16.0,
                      ),
                      maxLines: 3,
                    ),
                    SizedBox(height: 24.0),
                    
                    // Scripture reference input
                    Text(
                      'Scripture Reference (Optional)',
                      style: GoogleFonts.poppins(
                        fontSize: 16.0,
                        fontWeight: FontWeight.w600,
                        color: Color(urls.textPrimary),
                      ),
                    ),
                    SizedBox(height: 8.0),
                    TextFormField(
                      controller: _scriptureReferenceController,
                      decoration: InputDecoration(
                        hintText: 'e.g., Quran 2:186 or Hadith reference',
                        hintStyle: GoogleFonts.poppins(
                          color: Color(urls.textSecondary),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                          borderSide: BorderSide(
                            color: Color(urls.mediumGray),
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                          borderSide: BorderSide(
                            color: Color(urls.mediumGray),
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                          borderSide: BorderSide(
                            color: Color(urls.primaryColor),
                          ),
                        ),
                      ),
                      style: GoogleFonts.poppins(
                        fontSize: 16.0,
                      ),
                    ),
                    SizedBox(height: 24.0),
                    
                    // Status selector
                    Text(
                      'Status',
                      style: GoogleFonts.poppins(
                        fontSize: 16.0,
                        fontWeight: FontWeight.w600,
                        color: Color(urls.textPrimary),
                      ),
                    ),
                    SizedBox(height: 8.0),
                    StatusSelector(
                      selectedStatus: _selectedStatus,
                      onStatusSelected: (status) {
                        setState(() {
                          _selectedStatus = status;
                        });
                      },
                    ),
                    SizedBox(height: 24.0),
                    
                    // Category selector
                    Text(
                      'Category',
                      style: GoogleFonts.poppins(
                        fontSize: 16.0,
                        fontWeight: FontWeight.w600,
                        color: Color(urls.textPrimary),
                      ),
                    ),
                    SizedBox(height: 8.0),
                    CategorySelector(
                      selectedCategory: _selectedCategory,
                      onCategorySelected: (category) {
                        setState(() {
                          _selectedCategory = category;
                        });
                      },
                    ),
                    SizedBox(height: 24.0),
                    
                    // Tags input
                    Text(
                      'Tags (Optional)',
                      style: GoogleFonts.poppins(
                        fontSize: 16.0,
                        fontWeight: FontWeight.w600,
                        color: Color(urls.textPrimary),
                      ),
                    ),
                    SizedBox(height: 8.0),
                    TagInput(
                      tags: _selectedTags,
                      onTagsChanged: (tags) {
                        setState(() {
                          _selectedTags = tags;
                        });
                      },
                      suggestedTags: controller.getAllTags(),
                    ),
                    SizedBox(height: 24.0),
                    
                    // Reminder frequency
                    Text(
                      'Reminder Frequency (Optional)',
                      style: GoogleFonts.poppins(
                        fontSize: 16.0,
                        fontWeight: FontWeight.w600,
                        color: Color(urls.textPrimary),
                      ),
                    ),
                    SizedBox(height: 8.0),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                        border: Border.all(
                          color: Color(urls.mediumGray),
                        ),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<int>(
                          value: _reminderFrequency,
                          isExpanded: true,
                          icon: Icon(Icons.arrow_drop_down),
                          style: GoogleFonts.poppins(
                            color: Color(urls.textPrimary),
                            fontSize: 16.0,
                          ),
                          onChanged: (value) {
                            setState(() {
                              _reminderFrequency = value!;
                            });
                          },
                          items: [
                            DropdownMenuItem(
                              value: 0,
                              child: Text('No reminder'),
                            ),
                            DropdownMenuItem(
                              value: 1,
                              child: Text('Daily'),
                            ),
                            DropdownMenuItem(
                              value: 7,
                              child: Text('Weekly'),
                            ),
                            DropdownMenuItem(
                              value: 30,
                              child: Text('Monthly'),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 40.0),
                  ],
                ),
              ),
            ),
    );
  }
} 