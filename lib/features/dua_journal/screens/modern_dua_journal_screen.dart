import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../theme/app_colors.dart';
import '../../../theme/app_text_styles.dart';

class ModernDuaJournalScreen extends StatefulWidget {
  const ModernDuaJournalScreen({super.key});

  @override
  State<ModernDuaJournalScreen> createState() => _ModernDuaJournalScreenState();
}

class _ModernDuaJournalScreenState extends State<ModernDuaJournalScreen> {
  final List<Map<String, dynamic>> _duaEntries = [
    {
      'title': 'Istikhara Dua',
      'description': 'For seeking guidance in making important decisions',
      'isFavorite': false,
    },
    {
      'title': 'Protection Dua',
      'description': 'For protection from harm and evil',
      'isFavorite': false,
    },
    {
      'title': 'Forgiveness Dua',
      'description': 'For seeking forgiveness from Allah',
      'isFavorite': false,
    },
    {
      'title': 'Knowledge Dua',
      'description': 'For seeking knowledge and understanding',
      'isFavorite': false,
    },
    {
      'title': 'Blessings Dua',
      'description': 'For seeking blessings and prosperity',
      'isFavorite': false,
    },
  ];

  void _toggleFavorite(int index) {
    setState(() {
      _duaEntries[index]['isFavorite'] = !_duaEntries[index]['isFavorite'];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Dua Journal',
          style: AppTextStyles.headlineMedium.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // My Duas Section
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: Text(
              'My Duas',
              style: AppTextStyles.headlineSmall.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          // Duas List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              itemCount: _duaEntries.length,
              itemBuilder: (context, index) {
                final dua = _duaEntries[index];
                return Container(
                  margin: const EdgeInsets.only(bottom: 16.0),
                  padding: const EdgeInsets.all(20.0),
                  decoration: BoxDecoration(
                    color: AppColors.cardBackground,
                    borderRadius: BorderRadius.circular(16.0),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.cardShadow,
                        blurRadius: 8.0,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      // Bookmark Icon
                      GestureDetector(
                        onTap: () => _toggleFavorite(index),
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: AppColors.surface,
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: Icon(
                            dua['isFavorite'] 
                                ? Icons.bookmark 
                                : Icons.bookmark_border,
                            color: AppColors.primary,
                            size: 20,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16.0),
                      
                      // Dua Content
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              dua['title'],
                              style: AppTextStyles.bodyLarge.copyWith(
                                fontWeight: FontWeight.w600,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            const SizedBox(height: 4.0),
                            Text(
                              dua['description'],
                              style: AppTextStyles.bodyMedium.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      
      // New Dua Button
      floatingActionButton: Container(
        margin: const EdgeInsets.only(bottom: 20.0),
        child: FloatingActionButton.extended(
          onPressed: () {
            // Navigate to add new dua screen
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Add new dua functionality coming soon!'),
                behavior: SnackBarBehavior.floating,
              ),
            );
          },
          backgroundColor: AppColors.primary,
          icon: const Icon(Icons.add, color: Colors.white),
          label: Text(
            'New Dua',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }
}
