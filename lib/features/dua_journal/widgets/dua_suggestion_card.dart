import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../URLs.dart';
import '../models/dua_suggestion.dart';
import '../models/dua_entry.dart';

class DuaSuggestionCard extends StatelessWidget {
  final DuaSuggestion suggestion;
  final Function(DuaSuggestion) onUse;
  
  const DuaSuggestionCard({
    Key? key,
    required this.suggestion,
    required this.onUse,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.0,
            offset: Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: Color(urls.secondaryColor).withOpacity(0.3),
          width: 1.0,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
            decoration: BoxDecoration(
              color: Color(urls.secondaryColor).withOpacity(0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(urls.defaultBorderRadius - 1),
                topRight: Radius.circular(urls.defaultBorderRadius - 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Source and reference
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        suggestion.source,
                        style: GoogleFonts.poppins(
                          color: Color(urls.primaryColor),
                          fontWeight: FontWeight.w600,
                          fontSize: 14.0,
                        ),
                      ),
                      Text(
                        suggestion.reference,
                        style: GoogleFonts.poppins(
                          color: Color(urls.textSecondary),
                          fontSize: 12.0,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Category
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
                  decoration: BoxDecoration(
                    color: Color(urls.primaryColor).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(urls.defaultBorderRadius / 2),
                    border: Border.all(
                      color: Color(urls.primaryColor).withOpacity(0.3),
                      width: 1.0,
                    ),
                  ),
                  child: Text(
                    _getCategoryText(suggestion.category),
                    style: GoogleFonts.poppins(
                      color: Color(urls.primaryColor),
                      fontWeight: FontWeight.w500,
                      fontSize: 12.0,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Arabic text
          Padding(
            padding: EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
            child: Text(
              suggestion.arabicText,
              style: GoogleFonts.scheherazadeNew(
                color: Color(urls.textPrimary),
                fontWeight: FontWeight.w500,
                fontSize: 24.0,
              ),
              textAlign: TextAlign.right,
              textDirection: TextDirection.rtl,
            ),
          ),
          
          // Transliteration
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Text(
              suggestion.transliteration,
              style: GoogleFonts.poppins(
                color: Color(urls.textSecondary),
                fontStyle: FontStyle.italic,
                fontSize: 14.0,
              ),
            ),
          ),
          
          // Translation
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Text(
              suggestion.translation,
              style: GoogleFonts.poppins(
                color: Color(urls.textPrimary),
                fontSize: 16.0,
              ),
            ),
          ),
          
          // Benefit
          if (suggestion.benefit.isNotEmpty)
            Padding(
              padding: EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 16.0),
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: Color(urls.lightGray),
                  borderRadius: BorderRadius.circular(urls.defaultBorderRadius / 2),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Benefit:',
                      style: GoogleFonts.poppins(
                        color: Color(urls.textPrimary),
                        fontWeight: FontWeight.w600,
                        fontSize: 14.0,
                      ),
                    ),
                    SizedBox(height: 4.0),
                    Text(
                      suggestion.benefit,
                      style: GoogleFonts.poppins(
                        color: Color(urls.textSecondary),
                        fontSize: 14.0,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          
          // Actions
          Padding(
            padding: EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Tags
                Expanded(
                  child: Wrap(
                    spacing: 8.0,
                    runSpacing: 8.0,
                    children: suggestion.tags.map((tag) {
                      return Container(
                        padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                        decoration: BoxDecoration(
                          color: Color(urls.lightGray),
                          borderRadius: BorderRadius.circular(urls.defaultBorderRadius / 2),
                        ),
                        child: Text(
                          tag,
                          style: GoogleFonts.poppins(
                            color: Color(urls.textSecondary),
                            fontSize: 12.0,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
                
                // Action buttons
                Row(
                  children: [
                    // Copy button
                    IconButton(
                      icon: Icon(
                        Icons.content_copy,
                        color: Color(urls.textSecondary),
                        size: 20.0,
                      ),
                      onPressed: () {
                        Clipboard.setData(ClipboardData(
                          text: '${suggestion.arabicText}\n\n${suggestion.transliteration}\n\n${suggestion.translation}',
                        ));
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Dua copied to clipboard'),
                            duration: Duration(seconds: 2),
                          ),
                        );
                      },
                      tooltip: 'Copy to clipboard',
                    ),
                    
                    // Use button
                    ElevatedButton.icon(
                      onPressed: () => onUse(suggestion),
                      icon: Icon(
                        Icons.add_circle_outline,
                        size: 18.0,
                      ),
                      label: Text(
                        'Use',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Color(urls.primaryColor),
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(urls.defaultBorderRadius / 2),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  String _getCategoryText(DuaCategory category) {
    switch (category) {
      case DuaCategory.personal:
        return 'Personal';
      case DuaCategory.family:
        return 'Family';
      case DuaCategory.health:
        return 'Health';
      case DuaCategory.career:
        return 'Career';
      case DuaCategory.education:
        return 'Education';
      case DuaCategory.spiritual:
        return 'Spiritual';
      case DuaCategory.community:
        return 'Community';
      case DuaCategory.financial:
        return 'Financial';
      case DuaCategory.travel:
        return 'Travel';
      case DuaCategory.other:
        return 'Other';
    }
  }
} 