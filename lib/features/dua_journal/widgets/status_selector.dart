import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../URLs.dart';
import '../models/dua_entry.dart';

class StatusSelector extends StatelessWidget {
  final DuaStatus selectedStatus;
  final Function(DuaStatus) onStatusSelected;
  
  const StatusSelector({
    Key? key,
    required this.selectedStatus,
    required this.onStatusSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Container(
      height: 60,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        children: DuaStatus.values.map((status) {
          final isSelected = selectedStatus == status;
          final statusColor = _getStatusColor(status, urls);
          
          return GestureDetector(
            onTap: () => onStatusSelected(status),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              margin: EdgeInsets.only(right: 12.0),
              decoration: BoxDecoration(
                color: isSelected ? statusColor : Colors.transparent,
                borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                border: Border.all(
                  color: isSelected ? statusColor : Color(urls.mediumGray),
                  width: 1.5,
                ),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: statusColor.withOpacity(0.3),
                          blurRadius: 4,
                          offset: Offset(0, 2),
                        )
                      ]
                    : [],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _getStatusIcon(status),
                    color: isSelected ? Colors.white : statusColor,
                    size: 18,
                  ),
                  SizedBox(width: 8),
                  Text(
                    _getStatusText(status),
                    style: GoogleFonts.poppins(
                      color: isSelected ? Colors.white : Color(urls.textPrimary),
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
  
  Color _getStatusColor(DuaStatus status, URLs urls) {
    switch (status) {
      case DuaStatus.pending:
        return Color(urls.warningColor);
      case DuaStatus.inProgress:
        return Color(urls.infoColor);
      case DuaStatus.answered:
        return Color(urls.successColor);
      case DuaStatus.reflection:
        return Color(0xFF9C27B0); // Purple
    }
  }
  
  IconData _getStatusIcon(DuaStatus status) {
    switch (status) {
      case DuaStatus.pending:
        return Icons.hourglass_empty;
      case DuaStatus.inProgress:
        return Icons.autorenew;
      case DuaStatus.answered:
        return Icons.check_circle;
      case DuaStatus.reflection:
        return Icons.self_improvement;
    }
  }
  
  String _getStatusText(DuaStatus status) {
    switch (status) {
      case DuaStatus.pending:
        return 'Pending';
      case DuaStatus.inProgress:
        return 'In Progress';
      case DuaStatus.answered:
        return 'Answered';
      case DuaStatus.reflection:
        return 'Reflection';
    }
  }
} 