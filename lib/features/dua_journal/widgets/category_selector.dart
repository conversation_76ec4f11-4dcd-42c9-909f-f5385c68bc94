import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../URLs.dart';
import '../models/dua_entry.dart';

class CategorySelector extends StatelessWidget {
  final DuaCategory? selectedCategory;
  final Function(DuaCategory) onCategorySelected;
  
  const CategorySelector({
    Key? key,
    this.selectedCategory,
    required this.onCategorySelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Container(
      height: 120,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        children: DuaCategory.values.map((category) {
          final isSelected = selectedCategory == category;
          
          return GestureDetector(
            onTap: () => onCategorySelected(category),
            child: Container(
              width: 100,
              margin: EdgeInsets.only(right: 12.0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isSelected 
                    ? [Color(urls.primaryColor), Color(urls.accentColor)]
                    : [Color(urls.lightGray), Color(urls.mediumGray)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: Color(urls.primaryColor).withOpacity(0.3),
                          blurRadius: 8,
                          offset: Offset(0, 3),
                        )
                      ]
                    : [],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _getCategoryIcon(category),
                    color: isSelected ? Colors.white : Color(urls.textSecondary),
                    size: 32,
                  ),
                  SizedBox(height: 8),
                  Text(
                    _getCategoryText(category),
                    style: GoogleFonts.poppins(
                      color: isSelected ? Colors.white : Color(urls.textSecondary),
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
  
  IconData _getCategoryIcon(DuaCategory category) {
    switch (category) {
      case DuaCategory.personal:
        return Icons.person;
      case DuaCategory.family:
        return Icons.family_restroom;
      case DuaCategory.health:
        return Icons.favorite;
      case DuaCategory.career:
        return Icons.work;
      case DuaCategory.education:
        return Icons.school;
      case DuaCategory.spiritual:
        return Icons.spa;
      case DuaCategory.community:
        return Icons.people;
      case DuaCategory.financial:
        return Icons.attach_money;
      case DuaCategory.travel:
        return Icons.flight;
      case DuaCategory.other:
        return Icons.category;
    }
  }
  
  String _getCategoryText(DuaCategory category) {
    switch (category) {
      case DuaCategory.personal:
        return 'Personal';
      case DuaCategory.family:
        return 'Family';
      case DuaCategory.health:
        return 'Health';
      case DuaCategory.career:
        return 'Career';
      case DuaCategory.education:
        return 'Education';
      case DuaCategory.spiritual:
        return 'Spiritual';
      case DuaCategory.community:
        return 'Community';
      case DuaCategory.financial:
        return 'Financial';
      case DuaCategory.travel:
        return 'Travel';
      case DuaCategory.other:
        return 'Other';
    }
  }
} 