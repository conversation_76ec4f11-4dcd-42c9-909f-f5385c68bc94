import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../URLs.dart';
import '../models/dua_entry.dart';

class DuaEntryCard extends StatelessWidget {
  final DuaEntry entry;
  final Function() onTap;
  final Function() onFavoriteToggle;
  final Function(DuaStatus) onStatusChange;
  
  const DuaEntryCard({
    Key? key,
    required this.entry,
    required this.onTap,
    required this.onFavoriteToggle,
    required this.onStatusChange,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10.0,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with status and category
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      entry.getStatusColor().withOpacity(0.8),
                      entry.getCategoryColor().withOpacity(0.8),
                    ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(urls.defaultBorderRadius),
                    topRight: Radius.circular(urls.defaultBorderRadius),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Status
                    Row(
                      children: [
                        Icon(
                          entry.getStatusIcon(),
                          color: Colors.white,
                          size: 18.0,
                        ),
                        SizedBox(width: 8.0),
                        Text(
                          entry.getStatusText(),
                          style: GoogleFonts.poppins(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 14.0,
                          ),
                        ),
                      ],
                    ),
                    
                    // Category
                    Row(
                      children: [
                        Icon(
                          entry.getCategoryIcon(),
                          color: Colors.white,
                          size: 18.0,
                        ),
                        SizedBox(width: 8.0),
                        Text(
                          entry.getCategoryText(),
                          style: GoogleFonts.poppins(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 14.0,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Content
              Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Dua text
                    Text(
                      entry.duaText,
                      style: GoogleFonts.poppins(
                        color: Color(urls.textPrimary),
                        fontWeight: FontWeight.w500,
                        fontSize: 16.0,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 8.0),
                    
                    // Reflection (if not empty)
                    if (entry.reflection.isNotEmpty) ...[
                      Text(
                        'Reflection:',
                        style: GoogleFonts.poppins(
                          color: Color(urls.textSecondary),
                          fontWeight: FontWeight.w600,
                          fontSize: 12.0,
                        ),
                      ),
                      SizedBox(height: 4.0),
                      Text(
                        entry.reflection,
                        style: GoogleFonts.poppins(
                          color: Color(urls.textSecondary),
                          fontSize: 14.0,
                          fontStyle: FontStyle.italic,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 8.0),
                    ],
                    
                    // Tags
                    if (entry.tags.isNotEmpty) ...[
                      Wrap(
                        spacing: 8.0,
                        runSpacing: 8.0,
                        children: entry.tags.map((tag) {
                          return Container(
                            padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                            decoration: BoxDecoration(
                              color: Color(urls.primaryColor).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(urls.defaultBorderRadius / 2),
                            ),
                            child: Text(
                              tag,
                              style: GoogleFonts.poppins(
                                color: Color(urls.primaryColor),
                                fontSize: 12.0,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                      SizedBox(height: 12.0),
                    ],
                    
                    // Date and actions
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Date
                        Text(
                          entry.formattedCreatedDate,
                          style: GoogleFonts.poppins(
                            color: Color(urls.textSecondary),
                            fontSize: 12.0,
                          ),
                        ),
                        
                        // Actions
                        Row(
                          children: [
                            // Status change button
                            PopupMenuButton<DuaStatus>(
                              icon: Icon(
                                Icons.update,
                                color: Color(urls.textSecondary),
                                size: 20.0,
                              ),
                              onSelected: onStatusChange,
                              itemBuilder: (context) => DuaStatus.values
                                  .where((status) => status != entry.status)
                                  .map((status) {
                                return PopupMenuItem<DuaStatus>(
                                  value: status,
                                  child: Row(
                                    children: [
                                      Icon(
                                        _getStatusIcon(status),
                                        color: _getStatusColor(status, urls),
                                        size: 18.0,
                                      ),
                                      SizedBox(width: 8.0),
                                      Text(
                                        _getStatusText(status),
                                        style: GoogleFonts.poppins(
                                          fontSize: 14.0,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }).toList(),
                            ),
                            
                            // Favorite button
                            IconButton(
                              icon: Icon(
                                entry.isFavorite
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color: entry.isFavorite
                                    ? Color(urls.errorColor)
                                    : Color(urls.textSecondary),
                                size: 20.0,
                              ),
                              onPressed: onFavoriteToggle,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Answered indicator (if answered)
              if (entry.status == DuaStatus.answered && entry.answeredDate != null)
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                  decoration: BoxDecoration(
                    color: Color(urls.successColor).withOpacity(0.1),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(urls.defaultBorderRadius),
                      bottomRight: Radius.circular(urls.defaultBorderRadius),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Color(urls.successColor),
                        size: 16.0,
                      ),
                      SizedBox(width: 8.0),
                      Text(
                        'Answered on ${entry.formattedAnsweredDate}',
                        style: GoogleFonts.poppins(
                          color: Color(urls.successColor),
                          fontWeight: FontWeight.w500,
                          fontSize: 12.0,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
  
  Color _getStatusColor(DuaStatus status, URLs urls) {
    switch (status) {
      case DuaStatus.pending:
        return Color(urls.warningColor);
      case DuaStatus.inProgress:
        return Color(urls.infoColor);
      case DuaStatus.answered:
        return Color(urls.successColor);
      case DuaStatus.reflection:
        return Color(0xFF9C27B0); // Purple
    }
  }
  
  IconData _getStatusIcon(DuaStatus status) {
    switch (status) {
      case DuaStatus.pending:
        return Icons.hourglass_empty;
      case DuaStatus.inProgress:
        return Icons.autorenew;
      case DuaStatus.answered:
        return Icons.check_circle;
      case DuaStatus.reflection:
        return Icons.self_improvement;
    }
  }
  
  String _getStatusText(DuaStatus status) {
    switch (status) {
      case DuaStatus.pending:
        return 'Mark as Pending';
      case DuaStatus.inProgress:
        return 'Mark as In Progress';
      case DuaStatus.answered:
        return 'Mark as Answered';
      case DuaStatus.reflection:
        return 'Mark as Reflection';
    }
  }
} 