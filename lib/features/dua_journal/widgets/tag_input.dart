import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../URLs.dart';

class TagInput extends StatefulWidget {
  final List<String> tags;
  final Function(List<String>) onTagsChanged;
  final List<String> suggestedTags;
  
  const TagInput({
    Key? key,
    required this.tags,
    required this.onTagsChanged,
    this.suggestedTags = const [],
  }) : super(key: key);

  @override
  _TagInputState createState() => _TagInputState();
}

class _TagInputState extends State<TagInput> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _showSuggestions = false;
  
  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }
  
  void _addTag(String tag) {
    if (tag.isEmpty) return;
    
    // Normalize tag
    tag = tag.trim().toLowerCase();
    
    // Check if tag already exists
    if (!widget.tags.contains(tag)) {
      final updatedTags = List<String>.from(widget.tags)..add(tag);
      widget.onTagsChanged(updatedTags);
    }
    
    // Clear input
    _controller.clear();
  }
  
  void _removeTag(String tag) {
    final updatedTags = List<String>.from(widget.tags)..remove(tag);
    widget.onTagsChanged(updatedTags);
  }
  
  List<String> _getFilteredSuggestions(String query) {
    if (query.isEmpty) return widget.suggestedTags;
    
    query = query.toLowerCase();
    return widget.suggestedTags
        .where((tag) => tag.toLowerCase().contains(query))
        .where((tag) => !widget.tags.contains(tag))
        .toList();
  }
  
  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tags display
        if (widget.tags.isNotEmpty)
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
            child: Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: widget.tags.map((tag) {
                return Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
                  decoration: BoxDecoration(
                    color: Color(urls.primaryColor).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                    border: Border.all(
                      color: Color(urls.primaryColor).withOpacity(0.3),
                      width: 1.0,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        tag,
                        style: GoogleFonts.poppins(
                          color: Color(urls.primaryColor),
                          fontSize: 14.0,
                        ),
                      ),
                      SizedBox(width: 4.0),
                      GestureDetector(
                        onTap: () => _removeTag(tag),
                        child: Icon(
                          Icons.close,
                          size: 16.0,
                          color: Color(urls.primaryColor),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        
        // Tag input
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
            border: Border.all(
              color: _focusNode.hasFocus
                  ? Color(urls.primaryColor)
                  : Color(urls.mediumGray),
              width: 1.0,
            ),
            boxShadow: _focusNode.hasFocus
                ? [
                    BoxShadow(
                      color: Color(urls.primaryColor).withOpacity(0.1),
                      blurRadius: 4.0,
                      offset: Offset(0, 2),
                    )
                  ]
                : [],
          ),
          child: TextField(
            controller: _controller,
            focusNode: _focusNode,
            onChanged: (value) {
              setState(() {
                _showSuggestions = true;
              });
            },
            onSubmitted: (value) {
              _addTag(value);
              setState(() {
                _showSuggestions = false;
              });
            },
            decoration: InputDecoration(
              hintText: 'Add tags (e.g., family, health, guidance)',
              hintStyle: GoogleFonts.poppins(
                color: Color(urls.textSecondary),
                fontSize: 14.0,
              ),
              prefixIcon: Icon(
                Icons.tag,
                color: _focusNode.hasFocus
                    ? Color(urls.primaryColor)
                    : Color(urls.textSecondary),
              ),
              suffixIcon: IconButton(
                icon: Icon(
                  Icons.add_circle_outline,
                  color: _focusNode.hasFocus
                      ? Color(urls.primaryColor)
                      : Color(urls.textSecondary),
                ),
                onPressed: () {
                  _addTag(_controller.text);
                  setState(() {
                    _showSuggestions = false;
                  });
                  _focusNode.requestFocus();
                },
              ),
              contentPadding: EdgeInsets.symmetric(vertical: 12.0),
              border: InputBorder.none,
            ),
          ),
        ),
        
        // Tag suggestions
        if (_showSuggestions && widget.suggestedTags.isNotEmpty)
          Container(
            margin: EdgeInsets.only(top: 8.0),
            padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
              border: Border.all(
                color: Color(urls.mediumGray),
                width: 1.0,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4.0,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 8.0, bottom: 8.0),
                  child: Text(
                    'Suggested Tags',
                    style: GoogleFonts.poppins(
                      color: Color(urls.textSecondary),
                      fontSize: 12.0,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Wrap(
                  spacing: 8.0,
                  runSpacing: 8.0,
                  children: _getFilteredSuggestions(_controller.text)
                      .where((tag) => !widget.tags.contains(tag))
                      .take(10)
                      .map((tag) {
                    return GestureDetector(
                      onTap: () {
                        _addTag(tag);
                        setState(() {
                          _showSuggestions = false;
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
                        decoration: BoxDecoration(
                          color: Color(urls.lightGray),
                          borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                        ),
                        child: Text(
                          tag,
                          style: GoogleFonts.poppins(
                            color: Color(urls.textPrimary),
                            fontSize: 14.0,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
      ],
    );
  }
} 