import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'controllers/dua_journal_controller.dart';
import 'screens/dua_journal_list_screen.dart';

class DuaJournalMain extends StatelessWidget {
  const DuaJournalMain({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => DuaJournalController(),
      child: DuaJournalListScreen(),
    );
  }
} 