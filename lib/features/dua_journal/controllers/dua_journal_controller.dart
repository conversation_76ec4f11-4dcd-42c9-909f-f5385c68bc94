import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/dua_entry.dart';
import '../models/dua_suggestion.dart';

class DuaJournalController extends ChangeNotifier {
  List<DuaEntry> _entries = [];
  List<DuaEntry> get entries => _entries;
  
  List<DuaEntry> _favoriteEntries = [];
  List<DuaEntry> get favoriteEntries => _favoriteEntries;
  
  List<DuaEntry> _filteredEntries = [];
  List<DuaEntry> get filteredEntries => _filteredEntries;
  
  bool _isLoading = false;
  bool get isLoading => _isLoading;
  
  String _errorMessage = '';
  String get errorMessage => _errorMessage;
  
  DuaCategory? _selectedCategory;
  DuaCategory? get selectedCategory => _selectedCategory;
  
  DuaStatus? _selectedStatus;
  DuaStatus? get selectedStatus => _selectedStatus;
  
  String _searchQuery = '';
  String get searchQuery => _searchQuery;
  
  // Initialize the controller
  Future<void> initialize() async {
    await loadEntries();
  }
  
  // Load entries from local storage
  Future<void> loadEntries() async {
    _setLoading(true);
    _clearError();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final entriesJson = prefs.getStringList('dua_journal_entries') ?? [];
      
      _entries = entriesJson
          .map((json) => DuaEntry.fromJson(jsonDecode(json)))
          .toList();
      
      // Sort entries by date (newest first)
      _entries.sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));
      
      // Filter favorite entries
      _favoriteEntries = _entries.where((entry) => entry.isFavorite).toList();
      
      // Apply current filters
      _applyFilters();
      
      _setLoading(false);
    } catch (e) {
      _setError('Failed to load dua journal entries: $e');
    }
  }
  
  // Save entries to local storage
  Future<void> saveEntries() async {
    _setLoading(true);
    _clearError();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final entriesJson = _entries
          .map((entry) => jsonEncode(entry.toJson()))
          .toList();
      
      await prefs.setStringList('dua_journal_entries', entriesJson);
      
      _setLoading(false);
    } catch (e) {
      _setError('Failed to save dua journal entries: $e');
    }
  }
  
  // Add a new dua entry
  Future<void> addEntry({
    required String duaText,
    required String reflection,
    required DuaStatus status,
    required DuaCategory category,
    List<String> tags = const [],
    String answerNotes = '',
    DateTime? answeredDate,
    int reminderFrequency = 0,
    String scriptureReference = '',
  }) async {
    _clearError();
    
    try {
      final uuid = Uuid();
      final now = DateTime.now();
      final newEntry = DuaEntry(
        id: uuid.v4(),
        createdAt: now,
        lastUpdated: now,
        duaText: duaText,
        reflection: reflection,
        status: status,
        category: category,
        tags: tags,
        answerNotes: answerNotes,
        answeredDate: answeredDate,
        reminderFrequency: reminderFrequency,
        scriptureReference: scriptureReference,
      );
      
      _entries.insert(0, newEntry); // Add to beginning of list
      
      // Apply current filters
      _applyFilters();
      
      await saveEntries();
      notifyListeners();
    } catch (e) {
      _setError('Failed to add dua entry: $e');
    }
  }
  
  // Update an existing dua entry
  Future<void> updateEntry({
    required String id,
    String? duaText,
    String? reflection,
    DuaStatus? status,
    DuaCategory? category,
    List<String>? tags,
    bool? isFavorite,
    String? answerNotes,
    DateTime? answeredDate,
    int? reminderFrequency,
    String? scriptureReference,
  }) async {
    _clearError();
    
    try {
      final index = _entries.indexWhere((entry) => entry.id == id);
      
      if (index != -1) {
        final updatedEntry = _entries[index].copyWith(
          lastUpdated: DateTime.now(),
          duaText: duaText,
          reflection: reflection,
          status: status,
          category: category,
          tags: tags,
          isFavorite: isFavorite,
          answerNotes: answerNotes,
          answeredDate: answeredDate,
          reminderFrequency: reminderFrequency,
          scriptureReference: scriptureReference,
        );
        
        _entries[index] = updatedEntry;
        
        // Update favorite entries if needed
        if (isFavorite != null) {
          if (isFavorite) {
            _favoriteEntries.add(updatedEntry);
          } else {
            _favoriteEntries.removeWhere((entry) => entry.id == id);
          }
        }
        
        // Apply current filters
        _applyFilters();
        
        await saveEntries();
        notifyListeners();
      } else {
        _setError('Entry not found');
      }
    } catch (e) {
      _setError('Failed to update dua entry: $e');
    }
  }
  
  // Delete a dua entry
  Future<void> deleteEntry(String id) async {
    _clearError();
    
    try {
      _entries.removeWhere((entry) => entry.id == id);
      _favoriteEntries.removeWhere((entry) => entry.id == id);
      
      // Apply current filters
      _applyFilters();
      
      await saveEntries();
      notifyListeners();
    } catch (e) {
      _setError('Failed to delete dua entry: $e');
    }
  }
  
  // Toggle favorite status
  Future<void> toggleFavorite(String id) async {
    _clearError();
    
    try {
      final index = _entries.indexWhere((entry) => entry.id == id);
      
      if (index != -1) {
        final entry = _entries[index];
        final isFavorite = !entry.isFavorite;
        
        final updatedEntry = entry.copyWith(
          isFavorite: isFavorite,
          lastUpdated: DateTime.now(),
        );
        
        _entries[index] = updatedEntry;
        
        if (isFavorite) {
          _favoriteEntries.add(updatedEntry);
        } else {
          _favoriteEntries.removeWhere((e) => e.id == id);
        }
        
        // Apply current filters
        _applyFilters();
        
        await saveEntries();
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to toggle favorite: $e');
    }
  }
  
  // Update dua status
  Future<void> updateStatus(String id, DuaStatus status) async {
    _clearError();
    
    try {
      final index = _entries.indexWhere((entry) => entry.id == id);
      
      if (index != -1) {
        final entry = _entries[index];
        
        final updatedEntry = entry.copyWith(
          status: status,
          lastUpdated: DateTime.now(),
          answeredDate: status == DuaStatus.answered ? DateTime.now() : entry.answeredDate,
        );
        
        _entries[index] = updatedEntry;
        
        // Update in favorites if needed
        final favoriteIndex = _favoriteEntries.indexWhere((e) => e.id == id);
        if (favoriteIndex != -1) {
          _favoriteEntries[favoriteIndex] = updatedEntry;
        }
        
        // Apply current filters
        _applyFilters();
        
        await saveEntries();
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to update status: $e');
    }
  }
  
  // Set category filter
  void setCategoryFilter(DuaCategory? category) {
    _selectedCategory = category;
    _applyFilters();
    notifyListeners();
  }
  
  // Set status filter
  void setStatusFilter(DuaStatus? status) {
    _selectedStatus = status;
    _applyFilters();
    notifyListeners();
  }
  
  // Set search query
  void setSearchQuery(String query) {
    _searchQuery = query;
    _applyFilters();
    notifyListeners();
  }
  
  // Clear all filters
  void clearFilters() {
    _selectedCategory = null;
    _selectedStatus = null;
    _searchQuery = '';
    _applyFilters();
    notifyListeners();
  }
  
  // Apply filters to entries
  void _applyFilters() {
    _filteredEntries = _entries.where((entry) {
      // Apply category filter
      if (_selectedCategory != null && entry.category != _selectedCategory) {
        return false;
      }
      
      // Apply status filter
      if (_selectedStatus != null && entry.status != _selectedStatus) {
        return false;
      }
      
      // Apply search query
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return entry.duaText.toLowerCase().contains(query) ||
               entry.reflection.toLowerCase().contains(query) ||
               entry.tags.any((tag) => tag.toLowerCase().contains(query)) ||
               entry.answerNotes.toLowerCase().contains(query) ||
               entry.scriptureReference.toLowerCase().contains(query);
      }
      
      return true;
    }).toList();
  }
  
  // Get entries by category
  List<DuaEntry> getEntriesByCategory(DuaCategory category) {
    return _entries.where((entry) => entry.category == category).toList();
  }
  
  // Get entries by status
  List<DuaEntry> getEntriesByStatus(DuaStatus status) {
    return _entries.where((entry) => entry.status == status).toList();
  }
  
  // Get entries with reminders
  List<DuaEntry> getEntriesWithReminders() {
    return _entries.where((entry) => entry.reminderFrequency > 0).toList();
  }
  
  // Get statistics
  Map<String, dynamic> getStatistics() {
    final totalEntries = _entries.length;
    final answeredEntries = _entries.where((e) => e.status == DuaStatus.answered).length;
    final pendingEntries = _entries.where((e) => e.status == DuaStatus.pending).length;
    final inProgressEntries = _entries.where((e) => e.status == DuaStatus.inProgress).length;
    final reflectionEntries = _entries.where((e) => e.status == DuaStatus.reflection).length;
    
    final Map<DuaCategory, int> entriesByCategory = {};
    for (final category in DuaCategory.values) {
      entriesByCategory[category] = _entries.where((e) => e.category == category).length;
    }
    
    return {
      'totalEntries': totalEntries,
      'answeredEntries': answeredEntries,
      'pendingEntries': pendingEntries,
      'inProgressEntries': inProgressEntries,
      'reflectionEntries': reflectionEntries,
      'entriesByCategory': entriesByCategory,
      'answeredPercentage': totalEntries > 0 ? (answeredEntries / totalEntries * 100).toStringAsFixed(1) : '0',
    };
  }
  
  // Get all unique tags
  List<String> getAllTags() {
    final Set<String> tags = {};
    for (final entry in _entries) {
      tags.addAll(entry.tags);
    }
    return tags.toList()..sort();
  }
  
  // Get dua suggestions based on category
  List<DuaSuggestion> getDuaSuggestions(DuaCategory category) {
    return sampleDuaSuggestions
        .where((suggestion) => suggestion.category == category)
        .toList();
  }
  
  // Get random dua suggestion
  DuaSuggestion getRandomDuaSuggestion() {
    final random = DateTime.now().millisecondsSinceEpoch % sampleDuaSuggestions.length;
    return sampleDuaSuggestions[random];
  }
  
  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  // Set error message
  void _setError(String message) {
    _errorMessage = message;
    _isLoading = false;
    notifyListeners();
  }
  
  // Clear error message
  void _clearError() {
    _errorMessage = '';
    notifyListeners();
  }
} 