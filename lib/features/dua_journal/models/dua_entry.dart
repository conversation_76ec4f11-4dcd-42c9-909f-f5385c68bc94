import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

enum DuaStatus {
  pending,
  inProgress,
  answered,
  reflection,
}

enum DuaCategory {
  personal,
  family,
  health,
  career,
  education,
  spiritual,
  community,
  financial,
  travel,
  other,
}

class DuaEntry {
  final String id;
  final DateTime createdAt;
  final DateTime lastUpdated;
  final String duaText;
  final String reflection;
  final DuaStatus status;
  final DuaCategory category;
  final List<String> tags;
  final bool isFavorite;
  final String answerNotes;
  final DateTime? answeredDate;
  final int reminderFrequency; // 0 = none, 1 = daily, 7 = weekly, etc.
  final String scriptureReference; // Optional Quran or Hadith reference
  
  DuaEntry({
    required this.id,
    required this.createdAt,
    required this.lastUpdated,
    required this.duaText,
    required this.reflection,
    required this.status,
    required this.category,
    this.tags = const [],
    this.isFavorite = false,
    this.answerNotes = '',
    this.answeredDate,
    this.reminderFrequency = 0,
    this.scriptureReference = '',
  });

  // Create a copy of the dua entry with updated fields
  DuaEntry copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? lastUpdated,
    String? duaText,
    String? reflection,
    DuaStatus? status,
    DuaCategory? category,
    List<String>? tags,
    bool? isFavorite,
    String? answerNotes,
    DateTime? answeredDate,
    int? reminderFrequency,
    String? scriptureReference,
  }) {
    return DuaEntry(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      duaText: duaText ?? this.duaText,
      reflection: reflection ?? this.reflection,
      status: status ?? this.status,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      isFavorite: isFavorite ?? this.isFavorite,
      answerNotes: answerNotes ?? this.answerNotes,
      answeredDate: answeredDate ?? this.answeredDate,
      reminderFrequency: reminderFrequency ?? this.reminderFrequency,
      scriptureReference: scriptureReference ?? this.scriptureReference,
    );
  }

  // Convert DuaEntry to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'createdAt': createdAt.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
      'duaText': duaText,
      'reflection': reflection,
      'status': status.toString().split('.').last,
      'category': category.toString().split('.').last,
      'tags': tags,
      'isFavorite': isFavorite,
      'answerNotes': answerNotes,
      'answeredDate': answeredDate?.toIso8601String(),
      'reminderFrequency': reminderFrequency,
      'scriptureReference': scriptureReference,
    };
  }

  // Create DuaEntry from JSON
  factory DuaEntry.fromJson(Map<String, dynamic> json) {
    return DuaEntry(
      id: json['id'],
      createdAt: DateTime.parse(json['createdAt']),
      lastUpdated: DateTime.parse(json['lastUpdated']),
      duaText: json['duaText'],
      reflection: json['reflection'],
      status: DuaStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => DuaStatus.pending,
      ),
      category: DuaCategory.values.firstWhere(
        (e) => e.toString().split('.').last == json['category'],
        orElse: () => DuaCategory.other,
      ),
      tags: List<String>.from(json['tags'] ?? []),
      isFavorite: json['isFavorite'] ?? false,
      answerNotes: json['answerNotes'] ?? '',
      answeredDate: json['answeredDate'] != null
          ? DateTime.parse(json['answeredDate'])
          : null,
      reminderFrequency: json['reminderFrequency'] ?? 0,
      scriptureReference: json['scriptureReference'] ?? '',
    );
  }

  // Format date as a readable string
  String get formattedCreatedDate => DateFormat('EEEE, MMMM d, yyyy').format(createdAt);
  
  // Format time as a readable string
  String get formattedCreatedTime => DateFormat('h:mm a').format(createdAt);
  
  // Format answered date if available
  String get formattedAnsweredDate => answeredDate != null
      ? DateFormat('EEEE, MMMM d, yyyy').format(answeredDate!)
      : 'Not yet answered';
  
  // Get color based on status
  Color getStatusColor() {
    switch (status) {
      case DuaStatus.pending:
        return Color(0xFFFFAB00); // Amber
      case DuaStatus.inProgress:
        return Color(0xFF2979FF); // Blue
      case DuaStatus.answered:
        return Color(0xFF00C853); // Green
      case DuaStatus.reflection:
        return Color(0xFF9C27B0); // Purple
    }
  }
  
  // Get icon based on status
  IconData getStatusIcon() {
    switch (status) {
      case DuaStatus.pending:
        return Icons.hourglass_empty;
      case DuaStatus.inProgress:
        return Icons.autorenew;
      case DuaStatus.answered:
        return Icons.check_circle;
      case DuaStatus.reflection:
        return Icons.self_improvement;
    }
  }
  
  // Get text based on status
  String getStatusText() {
    switch (status) {
      case DuaStatus.pending:
        return 'Pending';
      case DuaStatus.inProgress:
        return 'In Progress';
      case DuaStatus.answered:
        return 'Answered';
      case DuaStatus.reflection:
        return 'Reflection';
    }
  }
  
  // Get color based on category
  Color getCategoryColor() {
    switch (category) {
      case DuaCategory.personal:
        return Color(0xFF1A237E); // Deep Indigo
      case DuaCategory.family:
        return Color(0xFF00BFA5); // Teal
      case DuaCategory.health:
        return Color(0xFF00C853); // Green
      case DuaCategory.career:
        return Color(0xFF6200EA); // Deep Purple
      case DuaCategory.education:
        return Color(0xFF2979FF); // Blue
      case DuaCategory.spiritual:
        return Color(0xFFFFD700); // Gold
      case DuaCategory.community:
        return Color(0xFFFF6D00); // Orange
      case DuaCategory.financial:
        return Color(0xFF00B0FF); // Light Blue
      case DuaCategory.travel:
        return Color(0xFF3D5AFE); // Indigo
      case DuaCategory.other:
        return Color(0xFF757575); // Gray
    }
  }
  
  // Get icon based on category
  IconData getCategoryIcon() {
    switch (category) {
      case DuaCategory.personal:
        return Icons.person;
      case DuaCategory.family:
        return Icons.family_restroom;
      case DuaCategory.health:
        return Icons.favorite;
      case DuaCategory.career:
        return Icons.work;
      case DuaCategory.education:
        return Icons.school;
      case DuaCategory.spiritual:
        return Icons.spa;
      case DuaCategory.community:
        return Icons.people;
      case DuaCategory.financial:
        return Icons.attach_money;
      case DuaCategory.travel:
        return Icons.flight;
      case DuaCategory.other:
        return Icons.category;
    }
  }
  
  // Get text based on category
  String getCategoryText() {
    switch (category) {
      case DuaCategory.personal:
        return 'Personal';
      case DuaCategory.family:
        return 'Family';
      case DuaCategory.health:
        return 'Health';
      case DuaCategory.career:
        return 'Career';
      case DuaCategory.education:
        return 'Education';
      case DuaCategory.spiritual:
        return 'Spiritual';
      case DuaCategory.community:
        return 'Community';
      case DuaCategory.financial:
        return 'Financial';
      case DuaCategory.travel:
        return 'Travel';
      case DuaCategory.other:
        return 'Other';
    }
  }
} 