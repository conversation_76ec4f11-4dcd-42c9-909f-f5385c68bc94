import 'package:flutter/material.dart';
import '../models/dua_entry.dart';

class DuaSuggestion {
  final String id;
  final String arabicText;
  final String transliteration;
  final String translation;
  final String source;
  final String reference;
  final DuaCategory category;
  final List<String> tags;
  final String benefit;
  
  DuaSuggestion({
    required this.id,
    required this.arabicText,
    required this.transliteration,
    required this.translation,
    required this.source,
    required this.reference,
    required this.category,
    this.tags = const [],
    this.benefit = '',
  });

  // Convert DuaSuggestion to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'arabicText': arabicText,
      'transliteration': transliteration,
      'translation': translation,
      'source': source,
      'reference': reference,
      'category': category.toString().split('.').last,
      'tags': tags,
      'benefit': benefit,
    };
  }

  // Create DuaSuggestion from JSON
  factory DuaSuggestion.fromJson(Map<String, dynamic> json) {
    return DuaSuggestion(
      id: json['id'],
      arabicText: json['arabicText'],
      transliteration: json['transliteration'],
      translation: json['translation'],
      source: json['source'],
      reference: json['reference'],
      category: DuaCategory.values.firstWhere(
        (e) => e.toString().split('.').last == json['category'],
        orElse: () => DuaCategory.other,
      ),
      tags: List<String>.from(json['tags'] ?? []),
      benefit: json['benefit'] ?? '',
    );
  }
}

// Sample dua suggestions for inspiration
List<DuaSuggestion> sampleDuaSuggestions = [
  DuaSuggestion(
    id: '1',
    arabicText: 'رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً وَفِي الْآخِرَةِ حَسَنَةً وَقِنَا عَذَابَ النَّارِ',
    transliteration: 'Rabbana atina fid-dunya hasanatan wa fil-akhirati hasanatan waqina adhaban-nar',
    translation: 'Our Lord, give us in this world [that which is] good and in the Hereafter [that which is] good and protect us from the punishment of the Fire.',
    source: 'Quran',
    reference: 'Surah Al-Baqarah 2:201',
    category: DuaCategory.personal,
    tags: ['general', 'daily', 'protection'],
    benefit: 'A comprehensive dua asking for good in this life and the hereafter.',
  ),
  DuaSuggestion(
    id: '2',
    arabicText: 'رَبِّ اشْرَحْ لِي صَدْرِي وَيَسِّرْ لِي أَمْرِي',
    transliteration: 'Rabbi shrah li sadri wa yassir li amri',
    translation: 'My Lord, expand for me my chest and ease for me my task.',
    source: 'Quran',
    reference: 'Surah Taha 20:25-26',
    category: DuaCategory.career,
    tags: ['work', 'stress', 'anxiety'],
    benefit: 'For seeking ease in difficult tasks and relief from anxiety.',
  ),
  DuaSuggestion(
    id: '3',
    arabicText: 'رَبِّ زِدْنِي عِلْمًا',
    transliteration: 'Rabbi zidni ilma',
    translation: 'My Lord, increase me in knowledge.',
    source: 'Quran',
    reference: 'Surah Taha 20:114',
    category: DuaCategory.education,
    tags: ['knowledge', 'study', 'learning'],
    benefit: 'For seeking increase in beneficial knowledge and understanding.',
  ),
  DuaSuggestion(
    id: '4',
    arabicText: 'اللَّهُمَّ إِنِّي أَسْأَلُكَ الْعَفْوَ وَالْعَافِيَةَ فِي الدُّنْيَا وَالْآخِرَةِ',
    transliteration: 'Allahumma inni as\'aluka al-\'afwa wal-\'afiyah fid-dunya wal-akhirah',
    translation: 'O Allah, I ask You for pardon and well-being in this life and the next.',
    source: 'Hadith',
    reference: 'Ibn Majah',
    category: DuaCategory.health,
    tags: ['health', 'wellbeing', 'protection'],
    benefit: 'For seeking complete well-being in body, mind, and soul.',
  ),
  DuaSuggestion(
    id: '5',
    arabicText: 'اللَّهُمَّ بَارِكْ لَنَا فِي أَهْلِنَا وَمَالِنَا',
    transliteration: 'Allahumma barik lana fi ahlina wa malina',
    translation: 'O Allah, bless our family and our wealth.',
    source: 'Hadith',
    reference: 'Bukhari',
    category: DuaCategory.family,
    tags: ['family', 'wealth', 'blessing'],
    benefit: 'For seeking blessings in family relationships and provision.',
  ),
  DuaSuggestion(
    id: '6',
    arabicText: 'اللَّهُمَّ اغْفِرْ لِي ذَنْبِي كُلَّهُ، دِقَّهُ وَجِلَّهُ، وَأَوَّلَهُ وَآخِرَهُ، وَعَلَانِيَتَهُ وَسِرَّهُ',
    transliteration: 'Allahumma-ghfir li dhanbi kullahu, diqqahu wa jillahu, wa awwalahu wa akhirahu, wa alaniyatahu wa sirrahu',
    translation: 'O Allah, forgive me all my sins, small and great, first and last, open and secret.',
    source: 'Hadith',
    reference: 'Muslim',
    category: DuaCategory.spiritual,
    tags: ['forgiveness', 'repentance', 'spiritual growth'],
    benefit: 'For seeking complete forgiveness and spiritual purification.',
  ),
  DuaSuggestion(
    id: '7',
    arabicText: 'اللَّهُمَّ إِنِّي أَسْأَلُكَ الْهُدَى وَالتُّقَى وَالْعَفَافَ وَالْغِنَى',
    transliteration: 'Allahumma inni as\'aluka al-huda wat-tuqa wal-\'afafa wal-ghina',
    translation: 'O Allah, I ask You for guidance, piety, chastity, and contentment.',
    source: 'Hadith',
    reference: 'Muslim',
    category: DuaCategory.personal,
    tags: ['guidance', 'contentment', 'character'],
    benefit: 'For seeking the most important qualities for success in this life and the next.',
  ),
  DuaSuggestion(
    id: '8',
    arabicText: 'اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنْ الْهَمِّ وَالْحَزَنِ، وَالْعَجْزِ وَالْكَسَلِ، وَالْبُخْلِ وَالْجُبْنِ، وَضَلَعِ الدَّيْنِ وَغَلَبَةِ الرِّجَالِ',
    transliteration: 'Allahumma inni a\'udhu bika min al-hammi wal-hazan, wal-\'ajzi wal-kasal, wal-bukhli wal-jubn, wa dala\'i ad-dayn wa ghalabati ar-rijal',
    translation: 'O Allah, I seek refuge in You from anxiety and sorrow, weakness and laziness, miserliness and cowardice, the burden of debts and from being overpowered by men.',
    source: 'Hadith',
    reference: 'Bukhari',
    category: DuaCategory.personal,
    tags: ['protection', 'anxiety', 'financial'],
    benefit: 'For seeking protection from negative emotions and difficult situations.',
  ),
  DuaSuggestion(
    id: '9',
    arabicText: 'اللَّهُمَّ اكْفِنِي بِحَلَالِكَ عَنْ حَرَامِكَ، وَأَغْنِنِي بِفَضْلِكَ عَمَّنْ سِوَاكَ',
    transliteration: 'Allahumma-kfini bi halalika an haramika wa aghnini bi fadlika amman siwaka',
    translation: 'O Allah, suffice me with what You have allowed instead of what You have forbidden, and make me independent of all others besides You.',
    source: 'Hadith',
    reference: 'Tirmidhi',
    category: DuaCategory.financial,
    tags: ['provision', 'halal', 'independence'],
    benefit: 'For seeking halal provision and contentment with what Allah provides.',
  ),
  DuaSuggestion(
    id: '10',
    arabicText: 'اللَّهُمَّ إِنِّي أَسْتَخِيرُكَ بِعِلْمِكَ، وَأَسْتَقْدِرُكَ بِقُدْرَتِكَ، وَأَسْأَلُكَ مِنْ فَضْلِكَ الْعَظِيمِ',
    transliteration: 'Allahumma inni astakhiruka bi\'ilmika, wa astaqdiruka biqudratika, wa as\'aluka min fadlikal-\'azim',
    translation: 'O Allah, I seek Your guidance by Your knowledge, and I seek ability by Your power, and I ask You from Your great bounty.',
    source: 'Hadith',
    reference: 'Bukhari',
    category: DuaCategory.personal,
    tags: ['guidance', 'decision-making', 'istikhara'],
    benefit: 'For seeking guidance when making important decisions.',
  ),
]; 