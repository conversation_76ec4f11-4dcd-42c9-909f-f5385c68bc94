import 'dart:convert';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_native_timezone/flutter_native_timezone.dart';
import 'package:intl/intl.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/models/adhan_option.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/models/prayer_reminder_settings.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/screens/prayer_reminder_settings.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PrayerNotificationService {
  static final PrayerNotificationService _instance = PrayerNotificationService._internal();
  
  factory PrayerNotificationService() {
    return _instance;
  }
  
  PrayerNotificationService._internal();
  
  final Dio _dio = Dio();
  
  // Notification channel keys
  static const String prayerChannelKey = 'prayer_notifications';
  static const String prayerChannelName = 'Prayer Notifications';
  static const String prayerChannelDescription = 'Notifications for prayer times';
  
  // Notification IDs for each prayer
  static const int fajrNotificationId = 1;
  static const int dhuhrNotificationId = 2;
  static const int asrNotificationId = 3;
  static const int maghribNotificationId = 4;
  static const int ishaNotificationId = 5;
  
  // Map prayer names to API field names
  final Map<String, String> _prayerToApiField = {
    'Fajr': 'fajrJamah',
    'Dhuhr': 'zuhrJamah',
    'Asr': 'asrJamah',
    'Maghrib': 'maghribJamah',
    'Isha': 'ishaJamah',
  };
  
  // Map prayer names to notification IDs
  final Map<String, int> _prayerToNotificationId = {
    'Fajr': fajrNotificationId,
    'Dhuhr': dhuhrNotificationId,
    'Asr': asrNotificationId,
    'Maghrib': maghribNotificationId,
    'Isha': ishaNotificationId,
  };
  
  // Initialize the notification service
  Future<void> initialize() async {
    try {
      // Initialize time zone
      final String timeZoneName = await FlutterNativeTimezone.getLocalTimezone();
      
      // Set localization with proper parameters for Awesome Notifications 0.10.1
      await AwesomeNotifications().setLocalization(
        languageCode: 'en',
      );
      
      // Initialize Awesome Notifications
      await AwesomeNotifications().initialize(
        // App icon will be used as fallback when no notification icon is defined
        'resource://drawable/ic_notification',
        [
          NotificationChannel(
            channelGroupKey: 'prayer_time_group',
            channelKey: prayerChannelKey,
            channelName: prayerChannelName,
            channelDescription: prayerChannelDescription,
            defaultColor: Color(URLs().colorScheme),
            ledColor: Colors.white,
            importance: NotificationImportance.High,
            playSound: true,
            enableVibration: true,
            soundSource: 'resource://raw/notification_sound',
            channelShowBadge: true,
          ),
        ],
        debug: true,
      );
      
      // Request notification permissions
      await AwesomeNotifications().isNotificationAllowed().then((isAllowed) async {
        if (!isAllowed) {
          await AwesomeNotifications().requestPermissionToSendNotifications();
        }
      });
      
      // Cancel any existing notifications before scheduling new ones
      await cancelAllPrayerNotifications();
      
      // Schedule notifications for today
      await scheduleAllPrayerNotifications();
      
      debugPrint('Prayer notification service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing prayer notification service: $e');
    }
  }
  
  // Get user settings from shared preferences
  Future<PrayerReminderSettings> getSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? settingsJson = prefs.getString('prayer_reminder_settings');
      
      if (settingsJson == null) {
        return PrayerReminderSettings.defaultSettings();
      }
      
      return PrayerReminderSettings.fromJson(json.decode(settingsJson));
    } catch (e) {
      debugPrint('Error getting prayer reminder settings: $e');
      return PrayerReminderSettings.defaultSettings();
    }
  }
  
  // Update user settings in shared preferences
  Future<void> updateSettings(PrayerReminderSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('prayer_reminder_settings', json.encode(settings.toJson()));
      
      // Reschedule notifications with new settings
      await scheduleAllPrayerNotifications();
    } catch (e) {
      debugPrint('Error updating prayer reminder settings: $e');
    }
  }
  
  // Fetch prayer times from API
  Future<Map<String, String>> fetchPrayerTimes() async {
    try {
      final formatter = DateFormat('yyyy-MM-dd');
      final formattedDate = formatter.format(DateTime.now());
      
      final response = await _dio.get('${URLs().WEBURL}/wp-json/dpt/v1/prayertime?filter=today');
      
      if (response.statusCode == 200 && response.data.isNotEmpty) {
        final data = response.data[0];
        debugPrint('Fetched prayer times data: $data');
        
        // Parse the time strings and create DateTime objects
        Map<String, String> prayerTimes = {};
        
        try {
          // Check if each prayer time exists in the response before formatting
          if (data['fajrJamah'] != null) {
            prayerTimes['Fajr'] = _formatPrayerTimeString(formattedDate, data['fajrJamah'].toString());
          }
          
          if (data['zuhrJamah'] != null) {
            prayerTimes['Dhuhr'] = _formatPrayerTimeString(formattedDate, data['zuhrJamah'].toString());
          }
          
          if (data['asrJamah'] != null) {
            prayerTimes['Asr'] = _formatPrayerTimeString(formattedDate, data['asrJamah'].toString());
          }
          
          if (data['maghribJamah'] != null) {
            prayerTimes['Maghrib'] = _formatPrayerTimeString(formattedDate, data['maghribJamah'].toString());
          }
          
          if (data['ishaJamah'] != null) {
            prayerTimes['Isha'] = _formatPrayerTimeString(formattedDate, data['ishaJamah'].toString());
          }
          
          debugPrint('Formatted prayer times: $prayerTimes');
          return prayerTimes;
        } catch (e) {
          debugPrint('Error formatting prayer times: $e');
          throw Exception('Error formatting prayer times: $e');
        }
      } else {
        debugPrint('Failed to load prayer times. Status code: ${response.statusCode}, Data: ${response.data}');
        throw Exception('Failed to load prayer times');
      }
    } catch (e) {
      debugPrint('Error fetching prayer times: $e');
      throw Exception('Failed to load prayer times: $e');
    }
  }
  
  // Helper method to format prayer time string
  String _formatPrayerTimeString(String date, String timeString) {
    // Remove any extra spaces
    timeString = timeString.trim();
    
    // Check if the time is in 12-hour format with AM/PM
    if (timeString.toLowerCase().contains('am') || timeString.toLowerCase().contains('pm')) {
      // Parse the 12-hour format time
      final timeParts = timeString.split(' ');
      final time = timeParts[0];
      final amPm = timeParts[1].toUpperCase();
      
      final hourMinute = time.split(':');
      int hour = int.parse(hourMinute[0]);
      final minute = int.parse(hourMinute[1]);
      
      // Convert to 24-hour format
      if (amPm == 'PM' && hour < 12) {
        hour += 12;
      } else if (amPm == 'AM' && hour == 12) {
        hour = 0;
      }
      
      // Format as "yyyy-MM-dd HH:mm:ss"
      return '$date ${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}:00';
    } else {
      // Assume it's already in 24-hour format
      return '$date $timeString:00';
    }
  }
  
  // Fetch tomorrow's prayer times from API
  Future<Map<String, String>> fetchTomorrowPrayerTimes() async {
    try {
      final formatter = DateFormat('yyyy-MM-dd');
      final tomorrow = DateTime.now().add(Duration(days: 1));
      final formattedTomorrow = formatter.format(tomorrow);
      
      // Use the specific endpoint for tomorrow's prayer times
      final response = await _dio.get('${URLs().WEBURL}/wp-json/dpt/v1/prayertime?filter=tomorrow');
      
      if (response.statusCode == 200 && response.data.isNotEmpty) {
        final data = response.data[0];
        debugPrint('Fetched tomorrow prayer times data: $data');
        
        Map<String, String> prayerTimes = {};
        
        // Check if the API returns tomorrow data directly or in a nested 'tomorrow' object
        if (data.containsKey('fajrJamah')) {
          // Direct format
          prayerTimes['Fajr'] = _formatPrayerTimeString(formattedTomorrow, data['fajrJamah']);
          prayerTimes['Dhuhr'] = _formatPrayerTimeString(formattedTomorrow, data['zuhrJamah']);
          prayerTimes['Asr'] = _formatPrayerTimeString(formattedTomorrow, data['asrJamah']);
          prayerTimes['Maghrib'] = _formatPrayerTimeString(formattedTomorrow, data['maghribJamah']);
          prayerTimes['Isha'] = _formatPrayerTimeString(formattedTomorrow, data['ishaJamah']);
        } else if (data.containsKey('tomorrow')) {
          // Nested format
          final tomorrowData = data['tomorrow'];
          prayerTimes['Fajr'] = _formatPrayerTimeString(formattedTomorrow, tomorrowData['fajrJamah']);
          prayerTimes['Dhuhr'] = _formatPrayerTimeString(formattedTomorrow, tomorrowData['zuhrJamah']);
          prayerTimes['Asr'] = _formatPrayerTimeString(formattedTomorrow, tomorrowData['asrJamah']);
          prayerTimes['Maghrib'] = _formatPrayerTimeString(formattedTomorrow, tomorrowData['maghribJamah']);
          prayerTimes['Isha'] = _formatPrayerTimeString(formattedTomorrow, tomorrowData['ishaJamah']);
        } else {
          debugPrint('Tomorrow prayer times data format not recognized: $data');
          throw Exception('Tomorrow prayer times data format not recognized');
        }
        
        debugPrint('Formatted tomorrow prayer times: $prayerTimes');
        return prayerTimes;
      } else {
        debugPrint('Failed to load tomorrow\'s prayer times. Status code: ${response.statusCode}, Data: ${response.data}');
        throw Exception('Failed to load tomorrow\'s prayer times');
      }
    } catch (e) {
      debugPrint('Error fetching tomorrow\'s prayer times: $e');
      throw Exception('Failed to load tomorrow\'s prayer times: $e');
    }
  }
  
  // Schedule notifications for all enabled prayers
  Future<void> scheduleAllPrayerNotifications() async {
    try {
      final settings = await getSettings();
      
      if (!settings.masterEnabled) {
        await cancelAllPrayerNotifications();
        return;
      }
      
      // Get next prayer info to determine which prayers to schedule
      final nextPrayerInfo = await getNextPrayerInfo();
      final isTomorrowFajr = nextPrayerInfo['isTomorrow'] == 'true';
      final nextPrayerName = nextPrayerInfo['nextPrayer'] ?? '';
      
      // Fetch today's prayer times
      final todayTimes = await fetchPrayerTimes();
      
      // Order of prayers
      final prayerOrder = ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
      
      // Find the index of the next prayer
      int nextPrayerIndex = prayerOrder.indexOf(nextPrayerName);
      if (nextPrayerIndex == -1) nextPrayerIndex = 0;
      
      // Schedule notifications for today's remaining prayers
      for (int i = nextPrayerIndex; i < prayerOrder.length; i++) {
        final prayerName = prayerOrder[i];
        if (settings.prayerEnabled[prayerName] == true) {
          await schedulePrayerNotification(prayerName, prayerTimes: todayTimes);
        } else {
          await cancelPrayerNotification(prayerName);
        }
      }
      
      // If the next prayer is tomorrow's Fajr or we've gone through all today's prayers,
      // schedule tomorrow's Fajr
      if (isTomorrowFajr || nextPrayerIndex == 0) {
        try {
          final tomorrowTimes = await fetchTomorrowPrayerTimes();
          if (settings.prayerEnabled['Fajr'] == true) {
            await schedulePrayerNotification('Fajr', prayerTimes: tomorrowTimes);
          }
        } catch (e) {
          debugPrint('Error scheduling tomorrow\'s Fajr: $e');
        }
      }
      
      debugPrint('Successfully scheduled all prayer notifications');
    } catch (e) {
      debugPrint('Error scheduling all prayer notifications: $e');
    }
  }
  
  // Schedule notification for a specific prayer
  Future<void> schedulePrayerNotification(String prayerName, {Map<String, String>? prayerTimes}) async {
    try {
      final settings = await getSettings();
      
      if (!settings.masterEnabled || settings.prayerEnabled[prayerName] != true) {
        await cancelPrayerNotification(prayerName);
        return;
      }
      
      final times = prayerTimes ?? await fetchPrayerTimes();
      final prayerTimeString = times[prayerName];
      
      if (prayerTimeString == null) {
        throw Exception('Prayer time not found for $prayerName');
      }
      
      try {
        final prayerTime = DateTime.parse(prayerTimeString);
        final reminderMinutes = settings.reminderMinutes[prayerName] ?? 15;
        final notificationTime = prayerTime.subtract(Duration(minutes: reminderMinutes));
        
        // Cancel existing notification for this prayer
        await cancelPrayerNotification(prayerName);
        
        // Only schedule if the notification time is in the future
        if (notificationTime.isAfter(DateTime.now())) {
          await _scheduleNotification(
            prayerName: prayerName,
            notificationTime: notificationTime,
            prayerTime: prayerTime,
            adhanOption: settings.adhanOptions[prayerName] ?? AdhanOption.defaultAdhan,
          );
          debugPrint('Scheduled notification for $prayerName at ${notificationTime.toString()}');
        } else {
          debugPrint('Not scheduling notification for $prayerName as the time has already passed: ${notificationTime.toString()}');
          
          // If this is today's Fajr and it has passed, check if we need to schedule tomorrow's Fajr
          if (prayerName == 'Fajr' && prayerTimeString.contains(DateFormat('yyyy-MM-dd').format(DateTime.now()))) {
            try {
              final tomorrowTimes = await fetchTomorrowPrayerTimes();
              final tomorrowFajrString = tomorrowTimes['Fajr'];
              
              if (tomorrowFajrString != null) {
                final tomorrowFajrTime = DateTime.parse(tomorrowFajrString);
                final tomorrowNotificationTime = tomorrowFajrTime.subtract(Duration(minutes: reminderMinutes));
                
                if (tomorrowNotificationTime.isAfter(DateTime.now())) {
                  await _scheduleNotification(
                    prayerName: 'Fajr',
                    notificationTime: tomorrowNotificationTime,
                    prayerTime: tomorrowFajrTime,
                    adhanOption: settings.adhanOptions['Fajr'] ?? AdhanOption.defaultAdhan,
                  );
                  debugPrint('Scheduled notification for tomorrow\'s Fajr at ${tomorrowNotificationTime.toString()}');
                }
              }
            } catch (e) {
              debugPrint('Error scheduling tomorrow\'s Fajr notification: $e');
            }
          }
        }
      } catch (e) {
        debugPrint('Error parsing prayer time for $prayerName: $e');
      }
    } catch (e) {
      debugPrint('Error scheduling prayer notification for $prayerName: $e');
    }
  }
  
  // Log notification to history
  Future<void> _logNotificationToHistory({
    required String title,
    required String body,
    String action = '',
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString('prayer_notification_history') ?? '[]';
      final List<dynamic> historyList = json.decode(historyJson);
      
      // Add new notification to history
      historyList.add({
        'title': title,
        'body': body,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'action': action,
      });
      
      // Limit history to 50 items
      if (historyList.length > 50) {
        historyList.removeRange(0, historyList.length - 50);
      }
      
      // Save updated history
      await prefs.setString('prayer_notification_history', json.encode(historyList));
    } catch (e) {
      debugPrint('Error logging notification to history: $e');
    }
  }
  
  // Schedule a notification with the specified details
  Future<void> _scheduleNotification({
    required String prayerName,
    required DateTime notificationTime,
    required DateTime prayerTime,
    required AdhanOption adhanOption,
  }) async {
    final notificationId = _prayerToNotificationId[prayerName.split(' ')[0]] ?? 0;
    final prayerTimeFormatted = DateFormat('h:mm a').format(prayerTime);
    
    // Determine if we should play sound based on adhan option
    final bool playSound = adhanOption != AdhanOption.silentNotification;
    final bool enableVibration = adhanOption != AdhanOption.vibrationOnly && adhanOption != AdhanOption.silentNotification;
    
    // Get the sound source if applicable
    String? soundSource;
    if (adhanOption != AdhanOption.silentNotification && 
        adhanOption != AdhanOption.vibrationOnly) {
      // Map adhan option to the correct sound file
      switch (adhanOption) {
        case AdhanOption.defaultAdhan:
          soundSource = 'resource://raw/adhan_default';
          break;
        case AdhanOption.fajrAdhan:
          soundSource = 'resource://raw/adhan_fajr';
          break;
        case AdhanOption.meccaAdhan:
          soundSource = 'resource://raw/adhan_mecca';
          break;
        case AdhanOption.madinaAdhan:
          soundSource = 'resource://raw/adhan_madina';
          break;
        case AdhanOption.alAqsaAdhan:
          soundSource = 'resource://raw/adhan_aqsa';
          break;
        case AdhanOption.shortNotification:
          soundSource = 'resource://raw/notification_short';
          break;
        default:
          soundSource = 'resource://raw/adhan_default';
      }
    }
    
    // Determine the title and body based on whether it's tomorrow's prayer
    final isTomorrow = prayerName.contains('Tomorrow');
    final cleanPrayerName = prayerName.split(' ')[0]; // Remove "(Tomorrow)" if present
    
    final title = '${cleanPrayerName} Prayer Time${isTomorrow ? ' (Tomorrow)' : ''}';
    final body = 'It\'s time for ${cleanPrayerName} prayer at $prayerTimeFormatted${isTomorrow ? ' tomorrow' : ''}';
    
    try {
      await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: notificationId,
          channelKey: prayerChannelKey,
          title: title,
          body: body,
          notificationLayout: NotificationLayout.Default,
          category: NotificationCategory.Alarm,
          wakeUpScreen: true,
          fullScreenIntent: true,
          criticalAlert: true,
          color: Color(URLs().colorScheme),
          backgroundColor: Color(URLs().colorScheme),
          payload: {'prayer_name': cleanPrayerName},
          autoDismissible: false,
          showWhen: true,
          displayOnForeground: true,
          displayOnBackground: true,
        ),
        actionButtons: [
          NotificationActionButton(
            key: 'MARK_AS_PRAYED',
            label: 'Mark as Prayed',
            color: Color(URLs().colorScheme),
            autoDismissible: true,
          ),
          NotificationActionButton(
            key: 'SNOOZE',
            label: 'Snooze 5 min',
            color: Colors.grey,
            autoDismissible: true,
          ),
        ],
        schedule: NotificationCalendar.fromDate(
          date: notificationTime,
          allowWhileIdle: true,
          preciseAlarm: true,
          repeats: false,
        ),
      );
      
      // Log notification to history
      await _logNotificationToHistory(
        title: title,
        body: body,
      );
      
      debugPrint('Successfully scheduled notification for $title at ${notificationTime.toString()}');
    } catch (e) {
      debugPrint('Error scheduling notification for $prayerName: $e');
    }
  }
  
  // Cancel notification for a specific prayer
  Future<void> cancelPrayerNotification(String prayerName) async {
    final notificationId = _prayerToNotificationId[prayerName] ?? 0;
    await AwesomeNotifications().cancel(notificationId);
  }
  
  // Cancel all prayer notifications
  Future<void> cancelAllPrayerNotifications() async {
    await AwesomeNotifications().cancelAll();
  }
  
  // Handle notification action (when user taps on notification)
  Future<void> handleNotificationAction(ReceivedAction receivedAction) async {
    if (receivedAction.payload?['prayer_name'] != null) {
      final prayerName = receivedAction.payload!['prayer_name']!;
      
      if (receivedAction.buttonKeyPressed == 'MARK_AS_PRAYED') {
        // Handle mark as prayed action
        await cancelPrayerNotification(prayerName);
        
        // Log action to history
        await _logNotificationToHistory(
          title: '$prayerName Prayer',
          body: 'Marked as prayed',
          action: 'MARK_AS_PRAYED',
        );
      } else if (receivedAction.buttonKeyPressed == 'SNOOZE') {
        // Handle snooze action - reschedule for 5 minutes later
        final settings = await getSettings();
        final adhanOption = settings.adhanOptions[prayerName] ?? AdhanOption.defaultAdhan;
        
        final snoozeTime = DateTime.now().add(Duration(minutes: 5));
        
        await _scheduleNotification(
          prayerName: prayerName,
          notificationTime: snoozeTime,
          prayerTime: snoozeTime,
          adhanOption: adhanOption,
        );
        
        // Log action to history
        await _logNotificationToHistory(
          title: '$prayerName Prayer',
          body: 'Snoozed for 5 minutes',
          action: 'SNOOZE',
        );
      }
    }
  }
  
  // Get the next prayer time and remaining time
  Future<Map<String, String>> getNextPrayerInfo() async {
    try {
      final now = DateTime.now();
      debugPrint('Current time: ${now.toString()}');
      
      // Fetch today's prayer times
      final todayTimes = await fetchPrayerTimes();
      debugPrint('Today\'s prayer times: $todayTimes');
      
      // Find the next prayer time for today
      DateTime? nextPrayerTime;
      String nextPrayerName = '';
      
      // Order of prayers
      final prayerOrder = ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
      
      // Check each prayer in order
      for (final prayerName in prayerOrder) {
        final timeString = todayTimes[prayerName];
        if (timeString == null) {
          debugPrint('No time found for $prayerName');
          continue;
        }
        
        try {
          final prayerTime = DateTime.parse(timeString);
          debugPrint('$prayerName time: ${prayerTime.toString()}');
          
          if (prayerTime.isAfter(now)) {
            if (nextPrayerTime == null || prayerTime.isBefore(nextPrayerTime)) {
              nextPrayerTime = prayerTime;
              nextPrayerName = prayerName;
              debugPrint('Found next prayer: $nextPrayerName at ${nextPrayerTime.toString()}');
            }
          } else {
            debugPrint('$prayerName has already passed');
          }
        } catch (e) {
          debugPrint('Error parsing time for $prayerName: $e');
        }
      }
      
      // If no prayer time is found for today, get tomorrow's Fajr
      if (nextPrayerTime == null) {
        debugPrint('No next prayer found for today, checking tomorrow\'s Fajr');
        
        try {
          final tomorrowTimes = await fetchTomorrowPrayerTimes();
          final tomorrowFajrString = tomorrowTimes['Fajr'];
          
          if (tomorrowFajrString != null) {
            final tomorrowFajrTime = DateTime.parse(tomorrowFajrString);
            debugPrint('Tomorrow\'s Fajr time: ${tomorrowFajrTime.toString()}');
            
            // Calculate time until tomorrow's Fajr
            final difference = tomorrowFajrTime.difference(now);
            final hours = difference.inHours;
            final minutes = difference.inMinutes % 60;
            
            debugPrint('Time until tomorrow\'s Fajr: $hours hours and $minutes minutes');
            
            return {
              'nextPrayer': 'Fajr',
              'timeRemaining': '$hours hr ${minutes.toString().padLeft(2, '0')} min',
              'isTomorrow': 'true',
              'nextPrayerTime': tomorrowFajrTime.toString(),
            };
          } else {
            debugPrint('Tomorrow\'s Fajr time not found');
          }
        } catch (e) {
          debugPrint('Error getting tomorrow\'s Fajr time: $e');
        }
        
        // Default values if we can't get tomorrow's Fajr
        return {
          'nextPrayer': 'Fajr',
          'timeRemaining': 'Loading...',
          'isTomorrow': 'true',
          'nextPrayerTime': '',
        };
      } else {
        // Calculate time until next prayer
        final difference = nextPrayerTime.difference(now);
        final hours = difference.inHours;
        final minutes = difference.inMinutes % 60;
        
        debugPrint('Time until $nextPrayerName: $hours hours and $minutes minutes');
        
        return {
          'nextPrayer': nextPrayerName,
          'timeRemaining': '$hours hr ${minutes.toString().padLeft(2, '0')} min',
          'isTomorrow': 'false',
          'nextPrayerTime': nextPrayerTime.toString(),
        };
      }
    } catch (e) {
      debugPrint('Error getting next prayer info: $e');
      return {
        'nextPrayer': 'Unknown',
        'timeRemaining': 'Error',
        'isTomorrow': 'false',
        'nextPrayerTime': '',
      };
    }
  }
} 