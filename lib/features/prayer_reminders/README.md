# Prayer Reminders Feature

## Overview

The Prayer Reminders feature allows users to receive timely notifications for Islamic prayer times. It provides a comprehensive set of tools for managing prayer notifications, including customizable reminder times, different adhan (call to prayer) options, and a user-friendly interface.

## Key Components

### Models

- **PrayerReminderSettings**: Manages user preferences for prayer notifications, including which prayers are enabled, reminder times, and adhan options.
- **AdhanOption**: Enum defining different adhan options (default, Fajr, Mecca, Madina, Al-Aqsa, etc.) with properties for names, descriptions, and asset paths.

### Services

- **PrayerNotificationService**: Core service that handles fetching prayer times, scheduling notifications, and managing user settings.

### Controllers

- **NotificationController**: Manages notification actions, permissions, and provides utility methods for testing notifications.

### Screens

- **PrayerReminderSettingsScreen**: Main settings screen for configuring prayer notifications.
- **PrayerReminderDashboard**: Comprehensive dashboard showing prayer times, countdown to next prayer, and quick actions.
- **PrayerNotificationHistoryScreen**: Shows a history of past notifications and user actions.

### Widgets

- **PrayerReminderWidget**: Home screen widget showing upcoming prayer times and notification status.
- **AdhanSelectionDialog**: Dialog for selecting adhan options for each prayer.
- **ReminderTimeDialog**: Dialog for selecting how many minutes before prayer to be notified.

## Features

- **Individual Prayer Controls**: Enable/disable notifications for specific prayers.
- **Customizable Reminder Times**: Set different reminder times for each prayer.
- **Multiple Adhan Options**: Choose from various adhan sounds for each prayer.
- **Prayer Dashboard**: View all prayer times for the day with countdown to next prayer.
- **Notification History**: Track past notifications and actions taken.
- **Quick Actions**: Test notifications, access settings, and refresh prayer times.
- **Background Notifications**: Receive notifications even when the app is closed.

## Usage

### Adding to Home Screen

Add the `PrayerReminderWidget` to your home screen:

```dart
PrayerReminderWidget(),
```

### Accessing from Drawer

The feature is accessible from the main drawer with options for:
- Prayer Dashboard
- Prayer Reminder Settings

### Testing Notifications

Use the floating action button in the dashboard or settings screen to test notifications.

## Technical Details

### Prayer Time API

Prayer times are fetched from the API endpoint:
```
${URLs().WEBURL}/wp-json/dpt/v1/prayertime?filter=today
```

### Notification Storage

Settings are stored in SharedPreferences under the key `prayer_reminder_settings`.
Notification history is stored under `prayer_notification_history`.

### Notification Scheduling

Notifications are scheduled using the Awesome Notifications package with precise alarms and wake-up screen capabilities.

## Dependencies

- awesome_notifications: ^0.10.1
- flutter_native_timezone
- shared_preferences
- intl
- dio

## Assets

The feature requires the following audio assets:
- assets/audio/adhan_default.mp3
- assets/audio/adhan_fajr.mp3
- assets/audio/adhan_mecca.mp3
- assets/audio/adhan_madina.mp3
- assets/audio/adhan_aqsa.mp3
- assets/audio/notification_short.mp3 