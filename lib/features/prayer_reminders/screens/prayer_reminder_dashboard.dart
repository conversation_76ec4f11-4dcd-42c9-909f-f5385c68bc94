import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/controllers/notification_controller.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/models/prayer_reminder_settings.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/screens/prayer_notification_history.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/screens/prayer_reminder_settings.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/services/prayer_notification_service.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:dio/dio.dart';

class PrayerReminderDashboard extends StatefulWidget {
  const PrayerReminderDashboard({Key? key}) : super(key: key);

  @override
  State<PrayerReminderDashboard> createState() => _PrayerReminderDashboardState();
}

class _PrayerReminderDashboardState extends State<PrayerReminderDashboard> {
  final PrayerNotificationService _notificationService = PrayerNotificationService();
  Map<String, String> _prayerTimes = {};
  PrayerReminderSettings? _settings;
  bool _isLoading = true;
  Timer? _refreshTimer;
  String _nextPrayer = '';
  String _timeUntilNextPrayer = '';
  DateTime _today = DateTime.now();
  final Dio _dio = Dio();
  
  @override
  void initState() {
    super.initState();
    _loadData();
    
    // Refresh the countdown timer every minute
    _refreshTimer = Timer.periodic(Duration(minutes: 1), (timer) {
      _updateNextPrayerTime();
    });
  }
  
  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }
  
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Load settings
      final settings = await _notificationService.getSettings();
      
      // Load prayer times
      final prayerTimes = await _notificationService.fetchPrayerTimes();
      
      // Get next prayer info
      final nextPrayerInfo = await _notificationService.getNextPrayerInfo();
      
      setState(() {
        _settings = settings;
        _prayerTimes = prayerTimes;
        
        // Set next prayer name and time
        _nextPrayer = nextPrayerInfo['nextPrayer'] ?? 'Unknown';
        if (nextPrayerInfo['isTomorrow'] == 'true') {
          _nextPrayer += ' (Tomorrow)';
        }
        
        // Set time remaining
        _timeUntilNextPrayer = nextPrayerInfo['timeRemaining'] ?? 'Loading...';
        
        _isLoading = false;
      });
      
      debugPrint('Prayer dashboard data loaded successfully');
      debugPrint('Next prayer: $_nextPrayer');
      debugPrint('Time remaining: $_timeUntilNextPrayer');
      debugPrint('Prayer times: $_prayerTimes');
    } catch (e) {
      debugPrint('Error loading prayer dashboard data: $e');
      setState(() {
        _isLoading = false;
      });
      
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load prayer times. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  void _updateNextPrayerTime() async {
    try {
      // Use the service to get the next prayer info
      final nextPrayerInfo = await _notificationService.getNextPrayerInfo();
      
      if (mounted) {
        setState(() {
          // Set next prayer name
          _nextPrayer = nextPrayerInfo['nextPrayer'] ?? 'Unknown';
          if (nextPrayerInfo['isTomorrow'] == 'true') {
            _nextPrayer += ' (Tomorrow)';
          }
          
          // Set time remaining
          _timeUntilNextPrayer = nextPrayerInfo['timeRemaining'] ?? 'Loading...';
        });
        
        debugPrint('Updated next prayer: $_nextPrayer');
        debugPrint('Updated time remaining: $_timeUntilNextPrayer');
      }
    } catch (error) {
      debugPrint('Error updating next prayer time: $error');
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'Prayer Dashboard',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Color(URLs().colorScheme),
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Color(URLs().colorScheme)),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.settings, color: Color(URLs().colorScheme)),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PrayerReminderSettingsScreen(),
                ),
              ).then((_) => _loadData());
            },
            tooltip: 'Settings',
          ),
          IconButton(
            icon: Icon(Icons.history, color: Color(URLs().colorScheme)),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PrayerNotificationHistoryScreen(),
                ),
              );
            },
            tooltip: 'Notification History',
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator(color: Color(URLs().colorScheme)))
          : RefreshIndicator(
              onRefresh: _loadData,
              color: Color(URLs().colorScheme),
              child: SingleChildScrollView(
                physics: AlwaysScrollableScrollPhysics(),
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildNextPrayerCard(),
                    SizedBox(height: 24),
                    _buildTodaysPrayerTimesCard(),
                    SizedBox(height: 24),
                    _buildReminderSettingsCard(),
                    SizedBox(height: 24),
                    _buildQuickActionsCard(),
                  ],
                ),
              ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await NotificationController.createTestNotification();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Test notification sent'),
              backgroundColor: Color(URLs().colorScheme),
            ),
          );
        },
        backgroundColor: Color(URLs().colorScheme),
        child: Icon(Icons.notifications_active),
        tooltip: 'Test Notification',
      ),
    );
  }
  
  Widget _buildNextPrayerCard() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(URLs().colorScheme),
            Color(URLs().colorScheme).withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Color(URLs().colorScheme).withOpacity(0.3),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      padding: EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.access_time_rounded,
                color: Colors.white,
                size: 20,
              ),
              SizedBox(width: 12),
              Text(
                'Next Prayer',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    _getIconForPrayer(_nextPrayer.split(' ')[0]),
                    color: Colors.white,
                    size: 30,
                  ),
                ),
              ),
              SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _nextPrayer,
                      style: GoogleFonts.poppins(
                        fontSize: 24,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Time remaining: $_timeUntilNextPrayer',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          if (_settings != null)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _settings!.masterEnabled
                        ? Icons.notifications_active
                        : Icons.notifications_off,
                    color: Colors.white,
                    size: 16,
                  ),
                  SizedBox(width: 8),
                  Text(
                    _settings!.masterEnabled
                        ? 'Notifications enabled'
                        : 'Notifications disabled',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(width: 8),
                  Switch(
                    value: _settings!.masterEnabled,
                    onChanged: (value) async {
                      final newSettings = _settings!.copyWith(masterEnabled: value);
                      await _notificationService.updateSettings(newSettings);
                      setState(() {
                        _settings = newSettings;
                      });
                      
                      // Refresh the prayer times and next prayer info
                      if (value) {
                        _loadData();
                      }
                    },
                    activeColor: Colors.white,
                    activeTrackColor: Colors.white.withOpacity(0.3),
                    inactiveThumbColor: Colors.white.withOpacity(0.8),
                    inactiveTrackColor: Colors.white.withOpacity(0.2),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
  
  // Helper method to get the appropriate icon for a prayer
  IconData _getIconForPrayer(String prayerName) {
    switch (prayerName) {
      case 'Fajr':
        return FontAwesomeIcons.sun;
      case 'Dhuhr':
        return FontAwesomeIcons.solidSun;
      case 'Asr':
        return FontAwesomeIcons.cloudSun;
      case 'Maghrib':
        return FontAwesomeIcons.solidMoon;
      case 'Isha':
        return FontAwesomeIcons.moon;
      default:
        return Icons.access_time_rounded;
    }
  }
  
  Widget _buildTodaysPrayerTimesCard() {
    final dateFormatter = DateFormat('EEEE, MMMM d, yyyy');
    final timeFormatter = DateFormat('h:mm a');
    
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      padding: EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                color: Color(URLs().colorScheme),
                size: 20,
              ),
              SizedBox(width: 12),
              Text(
                'Today\'s Prayer Times',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            dateFormatter.format(_today),
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 16),
          if (_prayerTimes.isEmpty)
            Center(
              child: Text(
                'No prayer times available',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            )
          else
            Column(
              children: [
                _buildPrayerTimeItem('Fajr', _formatPrayerTime(_prayerTimes['Fajr']), FontAwesomeIcons.sun),
                SizedBox(height: 12),
                _buildPrayerTimeItem('Dhuhr', _formatPrayerTime(_prayerTimes['Dhuhr']), FontAwesomeIcons.solidSun),
                SizedBox(height: 12),
                _buildPrayerTimeItem('Asr', _formatPrayerTime(_prayerTimes['Asr']), FontAwesomeIcons.cloudSun),
                SizedBox(height: 12),
                _buildPrayerTimeItem('Maghrib', _formatPrayerTime(_prayerTimes['Maghrib']), FontAwesomeIcons.solidMoon),
                SizedBox(height: 12),
                _buildPrayerTimeItem('Isha', _formatPrayerTime(_prayerTimes['Isha']), FontAwesomeIcons.moon),
              ],
            ),
        ],
      ),
    );
  }
  
  // Helper method to format prayer time string
  String? _formatPrayerTime(String? timeString) {
    if (timeString == null || timeString.trim().isEmpty) {
      return 'N/A';
    }
    
    try {
      // Try to parse as DateTime
      final prayerTime = DateTime.parse(timeString);
      return DateFormat('h:mm a').format(prayerTime);
    } catch (e) {
      debugPrint('Error formatting prayer time: $e');
      
      // If parsing fails, try to extract just the time part
      try {
        // Check if it contains a time in format HH:mm
        RegExp timeRegex = RegExp(r'(\d{1,2}):(\d{2})');
        var match = timeRegex.firstMatch(timeString);
        if (match != null) {
          return '${match.group(0)}';
        }
        
        // Check if it contains AM/PM format
        RegExp amPmRegex = RegExp(r'(\d{1,2}):(\d{2})\s*(AM|PM|am|pm)');
        match = amPmRegex.firstMatch(timeString);
        if (match != null) {
          return '${match.group(0)}';
        }
        
        // If all else fails, return the original string
        return timeString;
      } catch (e) {
        debugPrint('Error extracting time from string: $e');
        return timeString;
      }
    }
  }
  
  Widget _buildPrayerTimeItem(String prayerName, String? formattedTime, IconData icon) {
    if (formattedTime == null) return SizedBox();
    
    final isNext = prayerName == _nextPrayer.split(' ')[0]; // Remove "(Tomorrow)" if present
    final isPast = _isPrayerTimePast(prayerName);
    
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isNext
            ? Color(URLs().colorScheme).withOpacity(0.1)
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isNext
              ? Color(URLs().colorScheme)
              : Colors.grey[300]!,
          width: isNext ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isNext
                  ? Color(URLs().colorScheme)
                  : isPast
                      ? Colors.grey[300]
                      : Colors.grey[100],
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                icon,
                color: isNext
                    ? Colors.white
                    : isPast
                        ? Colors.grey[600]
                        : Colors.grey[800],
                size: 20,
              ),
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  prayerName,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isNext
                        ? Color(URLs().colorScheme)
                        : isPast
                            ? Colors.grey[600]
                            : Colors.grey[800],
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  formattedTime,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: isNext
                        ? Color(URLs().colorScheme)
                        : isPast
                            ? Colors.grey[500]
                            : Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
          if (_settings != null)
            Switch(
              value: _settings!.prayerEnabled[prayerName] ?? false,
              onChanged: _settings!.masterEnabled
                  ? (value) async {
                      final newPrayerEnabled = Map<String, bool>.from(_settings!.prayerEnabled);
                      newPrayerEnabled[prayerName] = value;
                      
                      final newSettings = _settings!.copyWith(
                        prayerEnabled: newPrayerEnabled,
                      );
                      
                      await _notificationService.updateSettings(newSettings);
                      setState(() {
                        _settings = newSettings;
                      });
                    }
                  : null,
              activeColor: Color(URLs().colorScheme),
            ),
        ],
      ),
    );
  }
  
  // Helper method to check if a prayer time has passed
  bool _isPrayerTimePast(String prayerName) {
    final timeString = _prayerTimes[prayerName];
    if (timeString == null) return false;
    
    try {
      final prayerTime = DateTime.parse(timeString);
      return prayerTime.isBefore(DateTime.now());
    } catch (e) {
      debugPrint('Error checking if prayer time has passed: $e');
      return false;
    }
  }
  
  Widget _buildReminderSettingsCard() {
    if (_settings == null) return SizedBox();
    
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      padding: EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.settings,
                color: Color(URLs().colorScheme),
                size: 20,
              ),
              SizedBox(width: 12),
              Text(
                'Reminder Settings',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Text(
            'Notification Times',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _settings!.reminderMinutes.entries.map((entry) {
              final prayerName = entry.key;
              final minutes = entry.value;
              
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '$prayerName: $minutes min before',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[800],
                  ),
                ),
              );
            }).toList(),
          ),
          SizedBox(height: 16),
          Text(
            'Adhan Sounds',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _settings!.adhanOptions.entries.map((entry) {
              final prayerName = entry.key;
              final adhanOption = entry.value;
              
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '$prayerName: ${adhanOption.name}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[800],
                  ),
                ),
              );
            }).toList(),
          ),
          SizedBox(height: 16),
          Center(
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PrayerReminderSettingsScreen(),
                  ),
                ).then((_) => _loadData());
              },
              icon: Icon(Icons.edit),
              label: Text('Edit Settings'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(URLs().colorScheme),
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildQuickActionsCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      padding: EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.flash_on,
                color: Color(URLs().colorScheme),
                size: 20,
              ),
              SizedBox(width: 12),
              Text(
                'Quick Actions',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  icon: Icons.notifications_active,
                  label: 'Test Notification',
                  onTap: () async {
                    await NotificationController.createTestNotification();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Test notification sent'),
                        backgroundColor: Color(URLs().colorScheme),
                      ),
                    );
                  },
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  icon: Icons.settings,
                  label: 'Notification Settings',
                  onTap: () async {
                    await NotificationController.openNotificationSettings();
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  icon: Icons.refresh,
                  label: 'Refresh Prayer Times',
                  onTap: () async {
                    await _loadData();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Prayer times refreshed'),
                        backgroundColor: Color(URLs().colorScheme),
                      ),
                    );
                  },
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  icon: Icons.history,
                  label: 'Notification History',
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PrayerNotificationHistoryScreen(),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: Color(URLs().colorScheme),
              size: 24,
            ),
            SizedBox(height: 8),
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey[800],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
} 