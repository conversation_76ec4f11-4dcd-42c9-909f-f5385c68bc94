import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:flutter/material.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/services/prayer_notification_service.dart';

class NotificationController {
  /// Singleton pattern
  static final NotificationController _instance = NotificationController._internal();
  
  /// Global navigator key for navigation from notifications
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  factory NotificationController() {
    return _instance;
  }
  
  NotificationController._internal();
  
  /// Initialize notification listeners
  static Future<void> initializeNotificationListeners() async {
    // Only initialize once
    AwesomeNotifications().setListeners(
      onActionReceivedMethod: onActionReceivedMethod,
      onNotificationCreatedMethod: onNotificationCreatedMethod,
      onNotificationDisplayedMethod: onNotificationDisplayedMethod,
      onDismissActionReceivedMethod: onDismissActionReceivedMethod,
    );
  }
  
  /// Use this method to detect when a new notification or a schedule is created
  @pragma("vm:entry-point")
  static Future<void> onNotificationCreatedMethod(ReceivedNotification receivedNotification) async {
    debugPrint('Notification created: ${receivedNotification.id}');
  }

  /// Use this method to detect every time that a new notification is displayed
  @pragma("vm:entry-point")
  static Future<void> onNotificationDisplayedMethod(ReceivedNotification receivedNotification) async {
    debugPrint('Notification displayed: ${receivedNotification.id}');
  }

  /// Use this method to detect if the user dismissed a notification
  @pragma("vm:entry-point")
  static Future<void> onDismissActionReceivedMethod(ReceivedAction receivedAction) async {
    debugPrint('Notification dismissed: ${receivedAction.id}');
  }

  /// Use this method to detect when the user taps on a notification or action button
  @pragma("vm:entry-point")
  static Future<void> onActionReceivedMethod(ReceivedAction receivedAction) async {
    debugPrint('Notification action received: ${receivedAction.buttonKeyPressed}');
    
    // Handle the action
    await PrayerNotificationService().handleNotificationAction(receivedAction);
    
    // Navigate to specific screen based on notification if app is in background
    if (receivedAction.actionType == ActionType.SilentBackgroundAction ||
        receivedAction.actionType == ActionType.Default) {
      // Navigate to prayer reminder settings when notification is tapped
      navigatorKey.currentState?.pushNamed('/prayerReminders');
    }
  }
  
  /// Request notification permissions
  static Future<bool> requestPermissions() async {
    return await AwesomeNotifications().requestPermissionToSendNotifications();
  }
  
  /// Check if notifications are allowed
  static Future<bool> checkPermissions() async {
    return await AwesomeNotifications().isNotificationAllowed();
  }
  
  /// Open notification settings
  static Future<void> openNotificationSettings() async {
    await AwesomeNotifications().showNotificationConfigPage();
  }
  
  /// Create a test notification to verify everything is working
  static Future<void> createTestNotification() async {
    await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: 999,
        channelKey: 'prayer_notifications',
        title: 'Test Notification',
        body: 'This is a test notification to verify that notifications are working correctly.',
        notificationLayout: NotificationLayout.Default,
        category: NotificationCategory.Message,
      ),
      actionButtons: [
        NotificationActionButton(
          key: 'DISMISS',
          label: 'Dismiss',
          autoDismissible: true,
        ),
      ],
    );
  }
} 