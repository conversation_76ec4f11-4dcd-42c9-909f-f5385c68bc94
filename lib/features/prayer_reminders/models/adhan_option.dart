/// Enum representing different adhan options for prayer notifications
enum AdhanOption {
  default<PERSON>dha<PERSON>,
  fajr<PERSON><PERSON><PERSON>,
  mecca<PERSON><PERSON>n,
  madina<PERSON><PERSON>n,
  alAqsaAdhan,
  shortNotification,
  vibrationOnly,
  silentNotification,
}

/// Extension to provide additional properties for AdhanOption
extension AdhanOptionExtension on AdhanOption {
  /// Returns a user-friendly name for the adhan option
  String get name {
    switch (this) {
      case AdhanOption.defaultAdhan:
        return 'Default Adhan';
      case AdhanOption.fajrAdhan:
        return 'Fajr Adhan';
      case AdhanOption.meccaAdhan:
        return 'Mecca Adhan';
      case AdhanOption.madinaAdhan:
        return '<PERSON><PERSON>han';
      case AdhanOption.alAqsaAdhan:
        return 'Al-<PERSON><PERSON><PERSON>han';
      case AdhanOption.shortNotification:
        return 'Short Notification';
      case AdhanOption.vibrationOnly:
        return 'Vibration Only';
      case AdhanOption.silentNotification:
        return 'Silent Notification';
    }
  }

  /// Returns a description for the adhan option
  String get description {
    switch (this) {
      case AdhanOption.defaultAdhan:
        return 'Standard adhan sound';
      case AdhanOption.fajrAdhan:
        return 'Special adhan for Fajr prayer';
      case AdhanOption.meccaAdhan:
        return '<PERSON>han from Masjid al-Haram in Mecca';
      case AdhanOption.madinaAdhan:
        return 'Adhan from Masjid an-Nabawi in Madina';
      case AdhanOption.alAqsaAdhan:
        return 'Adhan from Masjid al-Aqsa in Jerusalem';
      case AdhanOption.shortNotification:
        return 'Brief notification sound';
      case AdhanOption.vibrationOnly:
        return 'Only vibration, no sound';
      case AdhanOption.silentNotification:
        return 'Silent notification, no sound or vibration';
    }
  }

  /// Returns the asset path for the adhan sound file
  String? get assetPath {
    switch (this) {
      case AdhanOption.defaultAdhan:
        return 'assets/audio/adhan_default.mp3';
      case AdhanOption.fajrAdhan:
        return 'assets/audio/adhan_fajr.mp3';
      case AdhanOption.meccaAdhan:
        return 'assets/audio/adhan_mecca.mp3';
      case AdhanOption.madinaAdhan:
        return 'assets/audio/adhan_madina.mp3';
      case AdhanOption.alAqsaAdhan:
        return 'assets/audio/adhan_aqsa.mp3';
      case AdhanOption.shortNotification:
        return 'assets/audio/notification_short.mp3';
      case AdhanOption.vibrationOnly:
      case AdhanOption.silentNotification:
        return null;
    }
  }
} 