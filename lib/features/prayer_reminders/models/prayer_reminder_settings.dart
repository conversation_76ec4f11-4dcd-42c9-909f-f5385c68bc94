import 'package:new_islamic_app_uk/features/prayer_reminders/models/adhan_option.dart';

/// Model class for prayer reminder settings
class PrayerReminderSettings {
  /// Whether all prayer notifications are enabled
  final bool masterEnabled;
  
  /// Map of prayer names to whether notifications are enabled for that prayer
  final Map<String, bool> prayerEnabled;
  
  /// Map of prayer names to how many minutes before prayer time to send the notification
  final Map<String, int> reminderMinutes;
  
  /// Map of prayer names to which adhan option to use for that prayer
  final Map<String, AdhanOption> adhanOptions;
  
  /// Constructor
  PrayerReminderSettings({
    required this.masterEnabled,
    required this.prayerEnabled,
    required this.reminderMinutes,
    required this.adhanOptions,
  });
  
  /// Factory method to create default settings
  factory PrayerReminderSettings.defaultSettings() {
    return PrayerReminderSettings(
      masterEnabled: true,
      prayerEnabled: {
        'Fajr': true,
        'Dhuhr': true,
        'Asr': true,
        'Maghrib': true,
        '<PERSON>ha': true,
      },
      reminderMinutes: {
        'Fajr': 30,
        '<PERSON>huhr': 15,
        'Asr': 15,
        'Maghrib': 10,
        '<PERSON>ha': 15,
      },
      adhanOptions: {
        'Fajr': AdhanOption.fajrAdhan,
        'Dhuhr': AdhanOption.defaultAdhan,
        'Asr': AdhanOption.defaultAdhan,
        'Maghrib': AdhanOption.defaultAdhan,
        'Isha': AdhanOption.defaultAdhan,
      },
    );
  }
  
  /// Create a copy of this object with some fields replaced
  PrayerReminderSettings copyWith({
    bool? masterEnabled,
    Map<String, bool>? prayerEnabled,
    Map<String, int>? reminderMinutes,
    Map<String, AdhanOption>? adhanOptions,
  }) {
    return PrayerReminderSettings(
      masterEnabled: masterEnabled ?? this.masterEnabled,
      prayerEnabled: prayerEnabled ?? Map.from(this.prayerEnabled),
      reminderMinutes: reminderMinutes ?? Map.from(this.reminderMinutes),
      adhanOptions: adhanOptions ?? Map.from(this.adhanOptions),
    );
  }
  
  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'masterEnabled': masterEnabled,
      'prayerEnabled': prayerEnabled,
      'reminderMinutes': reminderMinutes,
      'adhanOptions': adhanOptions.map((key, value) => MapEntry(key, value.index)),
    };
  }
  
  /// Create from JSON
  factory PrayerReminderSettings.fromJson(Map<String, dynamic> json) {
    // Handle missing keys with default values
    final Map<String, bool> prayerEnabled = {};
    final Map<String, int> reminderMinutes = {};
    final Map<String, AdhanOption> adhanOptions = {};
    
    // Parse prayerEnabled
    if (json['prayerEnabled'] != null) {
      (json['prayerEnabled'] as Map).forEach((key, value) {
        if (key is String && value is bool) {
          prayerEnabled[key] = value;
        }
      });
    }
    
    // Parse reminderMinutes
    if (json['reminderMinutes'] != null) {
      (json['reminderMinutes'] as Map).forEach((key, value) {
        if (key is String && value is int) {
          reminderMinutes[key] = value;
        }
      });
    }
    
    // Parse adhanOptions
    if (json['adhanOptions'] != null) {
      (json['adhanOptions'] as Map).forEach((key, value) {
        if (key is String && value is int && value < AdhanOption.values.length) {
          adhanOptions[key] = AdhanOption.values[value];
        }
      });
    }
    
    return PrayerReminderSettings(
      masterEnabled: json['masterEnabled'] ?? true,
      prayerEnabled: prayerEnabled.isEmpty ? 
        PrayerReminderSettings.defaultSettings().prayerEnabled : prayerEnabled,
      reminderMinutes: reminderMinutes.isEmpty ? 
        PrayerReminderSettings.defaultSettings().reminderMinutes : reminderMinutes,
      adhanOptions: adhanOptions.isEmpty ? 
        PrayerReminderSettings.defaultSettings().adhanOptions : adhanOptions,
    );
  }
} 