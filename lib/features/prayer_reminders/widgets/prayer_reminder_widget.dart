import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/models/prayer_reminder_settings.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/screens/prayer_reminder_dashboard.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/services/prayer_notification_service.dart';

class PrayerReminderWidget extends StatefulWidget {
  const PrayerReminderWidget({Key? key}) : super(key: key);

  @override
  State<PrayerReminderWidget> createState() => _PrayerReminderWidgetState();
}

class _PrayerReminderWidgetState extends State<PrayerReminderWidget> {
  final PrayerNotificationService _service = PrayerNotificationService();
  bool _isEnabled = false;
  bool _isLoading = true;
  String _nextPrayer = '';
  String _nextPrayerTime = '';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await _service.getSettings();
      final prayerTimes = await _service.fetchPrayerTimes();
      
      // Find next prayer
      final now = DateTime.now();
      DateTime? nextPrayerTime;
      String nextPrayerName = '';
      
      for (final entry in prayerTimes.entries) {
        try {
          final prayerTime = DateTime.parse(entry.value);
          if (prayerTime.isAfter(now) && (nextPrayerTime == null || prayerTime.isBefore(nextPrayerTime))) {
            nextPrayerTime = prayerTime;
            nextPrayerName = entry.key;
          }
        } catch (e) {
          // Skip invalid time formats
        }
      }
      
      setState(() {
        _isEnabled = settings.masterEnabled;
        _nextPrayer = nextPrayerName;
        _nextPrayerTime = nextPrayerTime != null 
            ? '${nextPrayerTime.hour}:${nextPrayerTime.minute.toString().padLeft(2, '0')}'
            : 'N/A';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PrayerReminderDashboard(),
              ),
            ).then((_) => _loadSettings());
          },
          child: Padding(
            padding: EdgeInsets.all(16),
            child: _isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      color: Color(URLs().colorScheme),
                    ),
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.notifications_active,
                            color: Color(URLs().colorScheme),
                            size: 20,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Prayer Reminders',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[800],
                            ),
                          ),
                          Spacer(),
                          Switch(
                            value: _isEnabled,
                            onChanged: (value) async {
                              final settings = await _service.getSettings();
                              final newSettings = settings.copyWith(masterEnabled: value);
                              await _service.updateSettings(newSettings);
                              setState(() {
                                _isEnabled = value;
                              });
                            },
                            activeColor: Color(URLs().colorScheme),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      if (_nextPrayer.isNotEmpty)
                        Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              color: Colors.grey[600],
                              size: 16,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Next: $_nextPrayer at $_nextPrayerTime',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            'Tap to manage reminders',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Colors.grey[500],
                            ),
                          ),
                          SizedBox(width: 4),
                          Icon(
                            Icons.arrow_forward_ios,
                            color: Colors.grey[400],
                            size: 12,
                          ),
                        ],
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
} 