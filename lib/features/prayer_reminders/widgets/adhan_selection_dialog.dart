import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/models/adhan_option.dart';

class AdhanSelectionDialog extends StatefulWidget {
  final String prayerName;
  final AdhanOption currentOption;

  const AdhanSelectionDialog({
    Key? key,
    required this.prayerName,
    required this.currentOption,
  }) : super(key: key);

  @override
  State<AdhanSelectionDialog> createState() => _AdhanSelectionDialogState();
}

class _AdhanSelectionDialogState extends State<AdhanSelectionDialog> {
  late AdhanOption _selectedOption;
  Timer? _previewTimer;
  bool _isPlaying = false;
  
  @override
  void initState() {
    super.initState();
    _selectedOption = widget.currentOption;
  }
  
  @override
  void dispose() {
    _previewTimer?.cancel();
    super.dispose();
  }
  
  void _playPreview(AdhanOption option) {
    // In a real implementation, you would use an audio player to play the adhan
    // For this example, we'll just simulate playback for 3 seconds
    
    setState(() {
      _isPlaying = true;
      _selectedOption = option;
    });
    
    _previewTimer?.cancel();
    _previewTimer = Timer(Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isPlaying = false;
        });
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Select Adhan for ${widget.prayerName}',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Choose the adhan sound to play for ${widget.prayerName} prayer notifications.',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 16),
            
            // Adhan options
            Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.4,
              ),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildAdhanOption(AdhanOption.defaultAdhan),
                    _buildAdhanOption(AdhanOption.fajrAdhan),
                    _buildAdhanOption(AdhanOption.meccaAdhan),
                    _buildAdhanOption(AdhanOption.madinaAdhan),
                    _buildAdhanOption(AdhanOption.alAqsaAdhan),
                    _buildAdhanOption(AdhanOption.shortNotification),
                    _buildAdhanOption(AdhanOption.vibrationOnly),
                    _buildAdhanOption(AdhanOption.silentNotification),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'Cancel',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context, _selectedOption),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(URLs().colorScheme),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Select',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAdhanOption(AdhanOption option) {
    final isSelected = _selectedOption == option;
    final isPlayable = option != AdhanOption.vibrationOnly && 
                       option != AdhanOption.silentNotification;
    
    return InkWell(
      onTap: () {
        setState(() {
          _selectedOption = option;
        });
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? Color(URLs().colorScheme).withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? Color(URLs().colorScheme) : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: Color(URLs().colorScheme).withOpacity(0.1),
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ] : null,
        ),
        margin: EdgeInsets.only(bottom: 8),
        child: Row(
          children: [
            // Selection indicator
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? Color(URLs().colorScheme) : Colors.grey[400]!,
                  width: 2,
                ),
              ),
              child: isSelected
                  ? Center(
                      child: Container(
                        width: 10,
                        height: 10,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Color(URLs().colorScheme),
                        ),
                      ),
                    )
                  : null,
            ),
            SizedBox(width: 12),
            
            // Option details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    option.name,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[800],
                    ),
                  ),
                  SizedBox(height: 2),
                  Text(
                    option.description,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            
            // Preview button
            if (isPlayable)
              IconButton(
                onPressed: _isPlaying ? null : () => _playPreview(option),
                icon: Icon(
                  _isPlaying && _selectedOption == option
                      ? Icons.stop
                      : Icons.play_arrow,
                  color: _isPlaying && _selectedOption == option
                      ? Colors.red
                      : Color(URLs().colorScheme),
                ),
                tooltip: 'Preview',
              ),
          ],
        ),
      ),
    );
  }
} 