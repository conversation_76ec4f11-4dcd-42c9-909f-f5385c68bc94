import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';

class ReminderTimeDialog extends StatefulWidget {
  final String prayerName;
  final int currentMinutes;

  const ReminderTimeDialog({
    Key? key,
    required this.prayerName,
    required this.currentMinutes,
  }) : super(key: key);

  @override
  State<ReminderTimeDialog> createState() => _ReminderTimeDialogState();
}

class _ReminderTimeDialogState extends State<ReminderTimeDialog> {
  late int _selectedMinutes;
  final List<int> _timeOptions = [5, 10, 15, 20, 30, 45, 60];

  @override
  void initState() {
    super.initState();
    _selectedMinutes = widget.currentMinutes;
    
    // Ensure current minutes is in the list
    if (!_timeOptions.contains(_selectedMinutes)) {
      _timeOptions.add(_selectedMinutes);
      _timeOptions.sort();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  color: Color(URLs().colorScheme),
                  size: 24,
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Reminder Time for ${widget.prayerName}',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            
            // Time options
            Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.4,
              ),
              child: SingleChildScrollView(
                child: Column(
                  children: _timeOptions.map((minutes) => _buildTimeOption(minutes)).toList(),
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Notification message
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Color(URLs().colorScheme).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Color(URLs().colorScheme).withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Color(URLs().colorScheme),
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'You will be notified $_selectedMinutes minutes before ${widget.prayerName} prayer time.',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[800],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 16),
            
            // Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'Cancel',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context, _selectedMinutes),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(URLs().colorScheme),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Set',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeOption(int minutes) {
    final isSelected = _selectedMinutes == minutes;
    
    return InkWell(
      onTap: () {
        setState(() {
          _selectedMinutes = minutes;
        });
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? Color(URLs().colorScheme).withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? Color(URLs().colorScheme) : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        margin: EdgeInsets.only(bottom: 8),
        child: Row(
          children: [
            // Selection indicator
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? Color(URLs().colorScheme) : Colors.grey[400]!,
                  width: 2,
                ),
              ),
              child: isSelected
                  ? Center(
                      child: Container(
                        width: 10,
                        height: 10,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Color(URLs().colorScheme),
                        ),
                      ),
                    )
                  : null,
            ),
            SizedBox(width: 12),
            
            // Time text
            Text(
              '$minutes minutes before',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[800],
              ),
            ),
          ],
        ),
      ),
    );
  }
} 