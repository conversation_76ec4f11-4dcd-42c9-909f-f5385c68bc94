import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:quran/quran.dart' as quran;
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:new_islamic_app_uk/URLs.dart';

import '../models/quran_models.dart';

class QuranService {
  static final QuranService _instance = QuranService._internal();
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  factory QuranService() {
    return _instance;
  }
  
  QuranService._internal();
  
  AudioPlayer get audioPlayer => _audioPlayer;
  
  // Get all surahs
  List<SurahInfo> getAllSurahs() {
    List<SurahInfo> surahs = [];
    
    for (int i = 1; i <= quran.totalSurahCount; i++) {
      surahs.add(
        SurahInfo(
          number: i,
          nameArabic: quran.getSurahNameArabic(i),
          nameEnglish: quran.getSurahNameEnglish(i),
          nameTransliteration: quran.getSurahName(i),
          versesCount: quran.getVerseCount(i),
          placeOfRevelation: quran.getPlaceOfRevelation(i),
        ),
      );
    }
    
    return surahs;
  }
  
  // New async version of getAllSurahs for compatibility with refactored code
  Future<List<SurahInfo>> getSurahs() async {
    // This is just a wrapper around the synchronous method for now
    return getAllSurahs();
  }
  
  // Search surahs by query
  Future<List<SurahInfo>> searchSurahs(String query) async {
    // Get all surahs
    List<SurahInfo> allSurahs = getAllSurahs();
    
    // If query is empty, return empty list
    if (query.trim().isEmpty) {
      return [];
    }
    
    query = query.toLowerCase().trim();
    
    // Try parsing as number first
    try {
      int surahNumber = int.parse(query);
      if (surahNumber >= 1 && surahNumber <= 114) {
        return [allSurahs[surahNumber - 1]];
      }
    } catch (e) {
      // Not a number, continue with text search
    }
    
    // Filter by different criteria
    return allSurahs.where((surah) {
      // Surah name in English (case insensitive)
      if (surah.nameEnglish.toLowerCase().contains(query)) {
        return true;
      }
      
      // Surah name transliteration (case insensitive)
      if (surah.nameTransliteration.toLowerCase().contains(query)) {
        return true;
      }
      
      // Place of revelation (look for Meccan or Medinan)
      if (surah.placeOfRevelation.toLowerCase().contains(query)) {
        return true;
      }
      
      // Check for Juz search (e.g., 'Juz 1', 'Juz 30')
      if (query.startsWith('juz ')) {
        try {
          int juzNumber = int.parse(query.substring(4));
          // This would require additional data about which surahs are in which juz
          // For simplicity, just handling a few common cases
          if (juzNumber == 1 && surah.number <= 2) {
            return true;
          } else if (juzNumber == 30 && surah.number >= 78) {
            return true;
          }
        } catch (e) {
          // Not a valid juz number format
        }
      }
      
      // Search for "short" surahs
      if (query == 'short' && surah.versesCount < 30) {
        return true;
      }
      
      return false;
    }).toList();
  }
  
  // Get verses for a specific surah
  List<VerseInfo> getVersesForSurah(int surahNumber, {QuranTranslation translation = QuranTranslation.enSaheeh}) {
    List<VerseInfo> verses = [];
    int verseCount = quran.getVerseCount(surahNumber);
    
    for (int i = 1; i <= verseCount; i++) {
      verses.add(
        VerseInfo(
          surahNumber: surahNumber,
          verseNumber: i,
          arabicText: quran.getVerse(surahNumber, i, verseEndSymbol: true),
          translation: _getTranslation(surahNumber, i, translation),
          audioUrl: quran.getAudioURLByVerse(surahNumber, i),
        ),
      );
    }
    
    return verses;
  }
  
  // Get translation based on selected language
  String _getTranslation(int surahNumber, int verseNumber, QuranTranslation translation) {
    switch (translation) {
      // English translations
      case QuranTranslation.enSaheeh:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.enSaheeh
        );
      case QuranTranslation.enClearQuran:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.enClearQuran
        );
      
      // European languages
      case QuranTranslation.frHamidullah:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.frHamidullah
        );
      case QuranTranslation.trSaheeh:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.trSaheeh
        );
      case QuranTranslation.ruKuliev:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.ruKuliev
        );
      case QuranTranslation.ptPortuguese:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.portuguese
        );
      case QuranTranslation.itPiccardo:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.itPiccardo
        );
      case QuranTranslation.nlSiregar:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.nlSiregar
        );
      case QuranTranslation.sveSwedish:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.swedish
        );
      case QuranTranslation.esSpanish:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.spanish
        );
      
      // Asian languages
      case QuranTranslation.faFarsi:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.faHusseinDari
        );
      case QuranTranslation.urJalandhry:
        // Fall back to the new Urdu translation since urJalandhry might not be available in the latest package
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.urdu
        );
      case QuranTranslation.urUrdu:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.urdu
        );
      case QuranTranslation.bnBengali:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.bengali
        );
      case QuranTranslation.zhChinese:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.chinese
        );
      case QuranTranslation.idIndonesian:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.indonesian
        );
      case QuranTranslation.mlAbdulHameed:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.mlAbdulHameed
        );
      default:
        return quran.getVerseTranslation(
          surahNumber, 
          verseNumber, 
          verseEndSymbol: false, 
          translation: quran.Translation.enSaheeh
        );
    }
  }
  
  // Play audio for a verse
  Future<void> playVerseAudio(int surahNumber, int verseNumber) async {
    String audioUrl = quran.getAudioURLByVerse(surahNumber, verseNumber);
    await _audioPlayer.play(UrlSource(audioUrl));
  }
  
  // Play audio for entire surah
  Future<void> playSurahAudio(int surahNumber) async {
    String audioUrl = quran.getAudioURLBySurah(surahNumber);
    await _audioPlayer.play(UrlSource(audioUrl));
  }
  
  // Stop audio
  Future<void> stopAudio() async {
    await _audioPlayer.stop();
  }
  
  // Pause audio
  Future<void> pauseAudio() async {
    await _audioPlayer.pause();
  }
  
  // Resume audio
  Future<void> resumeAudio() async {
    await _audioPlayer.resume();
  }
  
  // Save reading progress
  Future<void> saveReadingProgress(int surahNumber, int verseNumber) async {
    final prefs = await SharedPreferences.getInstance();
    final progress = ReadingProgress(
      surahNumber: surahNumber,
      lastReadVerse: verseNumber,
      lastReadTime: DateTime.now(),
    );
    
    // Save current progress
    await prefs.setString('quran_reading_progress', jsonEncode({
      'surahNumber': progress.surahNumber,
      'lastReadVerse': progress.lastReadVerse,
      'lastReadTime': progress.lastReadTime.toIso8601String(),
    }));
    
    // Update reading history
    List<String> historyJson = prefs.getStringList('quran_reading_history') ?? [];
    List<Map<String, dynamic>> history = historyJson
        .map((json) => jsonDecode(json) as Map<String, dynamic>)
        .toList();
    
    // Check if this surah is already in history
    bool surahExists = false;
    for (var i = 0; i < history.length; i++) {
      if (history[i]['surahNumber'] == surahNumber) {
        // Update existing entry
        history[i] = {
          'surahNumber': progress.surahNumber,
          'lastReadVerse': progress.lastReadVerse,
          'lastReadTime': progress.lastReadTime.toIso8601String(),
        };
        surahExists = true;
        break;
      }
    }
    
    // Add new entry if not exists
    if (!surahExists) {
      history.add({
        'surahNumber': progress.surahNumber,
        'lastReadVerse': progress.lastReadVerse,
        'lastReadTime': progress.lastReadTime.toIso8601String(),
      });
    }
    
    // Limit history to 10 entries
    if (history.length > 10) {
      // Sort by date and keep most recent
      history.sort((a, b) => 
        DateTime.parse(b['lastReadTime']).compareTo(DateTime.parse(a['lastReadTime']))
      );
      history = history.take(10).toList();
    }
    
    // Save updated history
    await prefs.setStringList(
      'quran_reading_history',
      history.map((item) => jsonEncode(item)).toList(),
    );
  }
  
  // Get reading progress
  Future<ReadingProgress?> getReadingProgress() async {
    final prefs = await SharedPreferences.getInstance();
    final progressString = prefs.getString('quran_reading_progress');
    
    if (progressString != null) {
      final Map<String, dynamic> progressMap = jsonDecode(progressString);
      return ReadingProgress(
        surahNumber: progressMap['surahNumber'],
        lastReadVerse: progressMap['lastReadVerse'],
        lastReadTime: DateTime.parse(progressMap['lastReadTime']),
      );
    }
    
    return null;
  }
  
  // Alias for getReadingProgress for compatibility with refactored code
  Future<ReadingProgress?> getLastReadingProgress() async {
    return getReadingProgress();
  }
  
  // Get reading history
  Future<List<ReadingProgress>> getReadingHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyString = prefs.getStringList('quran_reading_history');
    
    if (historyString == null || historyString.isEmpty) {
      // If no history exists, try to at least return the last read position
      final lastRead = await getReadingProgress();
      return lastRead != null ? [lastRead] : [];
    }
    
    List<ReadingProgress> history = [];
    for (var entry in historyString) {
      final Map<String, dynamic> progressMap = jsonDecode(entry);
      history.add(ReadingProgress(
        surahNumber: progressMap['surahNumber'],
        lastReadVerse: progressMap['lastReadVerse'],
        lastReadTime: DateTime.parse(progressMap['lastReadTime']),
      ));
    }
    
    // Sort by most recent first
    history.sort((a, b) => b.lastReadTime.compareTo(a.lastReadTime));
    return history;
  }
  
  // Add bookmark
  Future<void> addBookmark(int surahNumber, int verseNumber, {String? note}) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> bookmarksJson = prefs.getStringList('quran_bookmarks') ?? [];
    List<QuranBookmark> bookmarks = bookmarksJson
        .map((json) => _bookmarkFromJson(jsonDecode(json)))
        .toList();
    
    // Generate new ID
    int newId = bookmarks.isEmpty ? 1 : bookmarks.map((b) => b.id).reduce((a, b) => a > b ? a : b) + 1;
    
    // Create new bookmark
    final newBookmark = QuranBookmark(
      id: newId,
      surahNumber: surahNumber,
      verseNumber: verseNumber,
      createdAt: DateTime.now(),
      note: note,
    );
    
    // Add to list
    bookmarks.add(newBookmark);
    
    // Save updated list
    await prefs.setStringList(
      'quran_bookmarks',
      bookmarks.map((bookmark) => jsonEncode({
        'id': bookmark.id,
        'surahNumber': bookmark.surahNumber,
        'verseNumber': bookmark.verseNumber,
        'createdAt': bookmark.createdAt.toIso8601String(),
        'note': bookmark.note,
      })).toList(),
    );
  }
  
  // Remove bookmark
  Future<void> removeBookmark(int id) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> bookmarksJson = prefs.getStringList('quran_bookmarks') ?? [];
    List<QuranBookmark> bookmarks = bookmarksJson
        .map((json) => _bookmarkFromJson(jsonDecode(json)))
        .toList();
    
    // Remove bookmark with matching id
    bookmarks.removeWhere((bookmark) => bookmark.id == id);
    
    // Save updated list
    await prefs.setStringList(
      'quran_bookmarks',
      bookmarks.map((bookmark) => jsonEncode({
        'id': bookmark.id,
        'surahNumber': bookmark.surahNumber,
        'verseNumber': bookmark.verseNumber,
        'createdAt': bookmark.createdAt.toIso8601String(),
        'note': bookmark.note,
      })).toList(),
    );
  }
  
  // Get all bookmarks
  Future<List<QuranBookmark>> getBookmarks() async {
    final prefs = await SharedPreferences.getInstance();
    List<String> bookmarksJson = prefs.getStringList('quran_bookmarks') ?? [];
    
    return bookmarksJson
        .map((json) => _bookmarkFromJson(jsonDecode(json)))
        .toList();
  }
  
  // Helper method to create bookmark from json
  QuranBookmark _bookmarkFromJson(Map<String, dynamic> json) {
    return QuranBookmark(
      id: json['id'],
      surahNumber: json['surahNumber'],
      verseNumber: json['verseNumber'],
      createdAt: DateTime.parse(json['createdAt']),
      note: json['note'],
    );
  }
  
  // Save selected translation preference
  Future<void> saveSelectedTranslation(QuranTranslation translation) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('selected_quran_translation', translation.index);
  }
  
  // Get selected translation preference
  Future<QuranTranslation> getSelectedTranslation() async {
    final prefs = await SharedPreferences.getInstance();
    final translationIndex = prefs.getInt('selected_quran_translation') ?? 0;
    return QuranTranslation.values[translationIndex];
  }
  
  // Get the display name of a translation
  String getTranslationDisplayName(QuranTranslation translation) {
    switch (translation) {
      // English translations
      case QuranTranslation.enSaheeh:
        return 'English (Saheeh International)';
      case QuranTranslation.enClearQuran:
        return 'English (Clear Quran)';
      
      // European languages
      case QuranTranslation.frHamidullah:
        return 'French (Muhammad Hamidullah)';
      case QuranTranslation.trSaheeh:
        return 'Turkish';
      case QuranTranslation.ruKuliev:
        return 'Russian (Elmir Kuliev)';
      case QuranTranslation.ptPortuguese:
        return 'Portuguese';
      case QuranTranslation.itPiccardo:
        return 'Italian (Piccardo)';
      case QuranTranslation.nlSiregar:
        return 'Dutch (Siregar)';
      case QuranTranslation.sveSwedish:
        return 'Swedish';
      case QuranTranslation.esSpanish:
        return 'Spanish';
      
      // Asian languages
      case QuranTranslation.faFarsi:
        return 'Farsi';
      case QuranTranslation.urJalandhry:
        return 'Urdu (Jalandhry)';
      case QuranTranslation.urUrdu:
        return 'Urdu';
      case QuranTranslation.bnBengali:
        return 'Bengali';
      case QuranTranslation.zhChinese:
        return 'Chinese';
      case QuranTranslation.idIndonesian:
        return 'Indonesian';
      case QuranTranslation.mlAbdulHameed:
        return 'Malayalam (Abdul Hameed)';
      default:
        return 'Unknown Translation';
    }
  }
} 