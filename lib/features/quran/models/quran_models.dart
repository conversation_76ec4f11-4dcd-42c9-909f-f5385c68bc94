import 'package:flutter/material.dart';

/// Model for Surah (chapter) information
class SurahInfo {
  final int number;
  final String nameArabic;
  final String nameEnglish;
  final String nameTransliteration;
  final int versesCount;
  final String placeOfRevelation;
  final String? description;
  
  SurahInfo({
    required this.number,
    required this.nameArabic,
    required this.nameEnglish,
    required this.nameTransliteration,
    required this.versesCount,
    required this.placeOfRevelation,
    this.description,
  });
  
  // Helper getter to access the revelation type directly
  String get revelationType => placeOfRevelation;
}

/// Model for Verse (ayah) information
class VerseInfo {
  final int surahNumber;
  final int verseNumber;
  final String arabicText;
  final String translation;
  final String? transliteration;
  final String? tafsir;
  final String? audioUrl;
  
  VerseInfo({
    required this.surahNumber,
    required this.verseNumber,
    required this.arabicText,
    required this.translation,
    this.transliteration,
    this.tafsir,
    this.audioUrl,
  });
}

/// Model for a Bookmark
class QuranBookmark {
  final int id;
  final int surahNumber;
  final int verseNumber;
  final DateTime createdAt;
  final String? note;
  
  QuranBookmark({
    required this.id,
    required this.surahNumber,
    required this.verseNumber,
    required this.createdAt,
    this.note,
  });
}

/// Model for Reading Progress
class ReadingProgress {
  final int surahNumber;
  final int lastReadVerse;
  final DateTime lastReadTime;
  
  ReadingProgress({
    required this.surahNumber,
    required this.lastReadVerse,
    required this.lastReadTime,
  });
}

/// Enum for available translations
enum QuranTranslation {
  // English translations
  enSaheeh,      // English (Saheeh International)
  enClearQuran,  // English (Clear Quran)
  
  // Other European languages
  frHamidullah,  // French (Muhammad Hamidullah)
  trSaheeh,      // Turkish
  ruKuliev,      // Russian (Elmir Kuliev)
  ptPortuguese,  // Portuguese
  itPiccardo,    // Italian
  nlSiregar,     // Dutch
  sveSwedish,    // Swedish
  esSpanish,     // Spanish
  
  // Asian languages
  faFarsi,       // Farsi
  urJalandhry,   // Urdu (older translation)
  urUrdu,        // Urdu (new translation)
  bnBengali,     // Bengali
  zhChinese,     // Chinese
  idIndonesian,  // Indonesian
  mlAbdulHameed, // Malayalam
}

/// Enum for reciter options
enum QuranReciter {
  abdulBasit,
  misharyRashidAlafasy,
  mahmoudKhalilAlHusary,
  minshawiyMurattal,
  minshawiyMujawwad,
} 