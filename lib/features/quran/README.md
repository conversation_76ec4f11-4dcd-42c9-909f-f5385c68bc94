# Enhanced Quran Feature

This directory contains a completely redesigned and enhanced Quran feature for the Islamic App.

## Features

- **Modern UI Design**: Beautiful, sleek, and user-friendly interface
- **Surah List**: View all 114 surahs with detailed information
- **Search Functionality**: Search surahs by name or number
- **Bookmarks**: Save your favorite verses for quick access
- **Reading Progress**: Continue from where you left off
- **Audio Playback**: Listen to Quranic recitations
- **Multiple Translations**: Switch between different translations
- **Font Size Control**: Adjust text size for comfortable reading
- **Verse Actions**: Copy, share, and bookmark individual verses

## Directory Structure

```
lib/features/quran/
├── models/             # Data models
├── screens/            # UI screens
├── services/           # Business logic and data handling
├── widgets/            # Reusable UI components
└── quran_module.dart   # Main entry point
```

## How to Use

### In Your App

The feature can be integrated into your app using the `QuranModule` class:

```dart
import 'package:new_islamic_app_uk/features/quran/quran_module.dart';

// Use as a widget
QuranModule()

// Or navigate to it
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => QuranModule()),
);
```

### Route Registration

You can also register the routes in your app:

```dart
import 'package:new_islamic_app_uk/features/quran/quran_module.dart';

// In your MaterialApp
MaterialApp(
  routes: {
    ...QuranRoutes.getRoutes(),
    // Your other routes
  },
);
```

## Dependencies

This feature relies on the following packages:

- `quran`: For Quranic text and metadata
- `audioplayers`: For audio playback
- `shared_preferences`: For storing user preferences
- Various Flutter UI packages

## Credits

- Quran text and translations courtesy of the `quran` package
- UI design inspired by modern Islamic app interfaces 