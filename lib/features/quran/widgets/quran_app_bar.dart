import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class QuranAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final String? subtitle;
  final Widget? leading;
  final List<Widget>? actions;
  final bool centerTitle;
  final double expandedHeight;
  final Color? backgroundColor;
  
  const QuranAppBar({
    Key? key,
    required this.title,
    this.subtitle,
    this.leading,
    this.actions,
    this.centerTitle = true,
    this.expandedHeight = 120.0,
    this.backgroundColor,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final bgColor = backgroundColor ?? (isDarkMode 
        ? Theme.of(context).appBarTheme.backgroundColor ?? Theme.of(context).colorScheme.surface
        : Colors.green.shade700);
        
    return Container(
      height: expandedHeight,
      color: bgColor,
      child: Safe<PERSON>rea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: centerTitle 
                ? CrossAxisAlignment.center 
                : CrossAxisAlignment.start,
            children: [
              // Navigation row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  leading ?? IconButton(
                    icon: const Icon(
                      Icons.arrow_back_ios_new_rounded,
                      color: Colors.white,
                      size: 20,
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.white.withOpacity(0.2),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                  
                  if (actions != null) 
                    Row(
                      children: actions!.map((action) {
                        // If the action is an IconButton, apply our custom style
                        if (action is IconButton) {
                          return Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: IconButton(
                              icon: action.icon,
                              onPressed: action.onPressed,
                              tooltip: action.tooltip,
                              style: IconButton.styleFrom(
                                backgroundColor: Colors.white.withOpacity(0.2),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                            ),
                          );
                        }
                        return action;
                      }).toList(),
                    ),
                ],
              ),
              
              const Spacer(),
              
              // Title and subtitle
              Padding(
                padding: const EdgeInsets.only(bottom: 24.0),
                child: Column(
                  crossAxisAlignment: centerTitle 
                      ? CrossAxisAlignment.center 
                      : CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.poppins(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    if (subtitle != null)
                      Text(
                        subtitle!,
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  @override
  Size get preferredSize => Size.fromHeight(expandedHeight);
} 