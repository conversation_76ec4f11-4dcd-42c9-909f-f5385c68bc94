import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import '../models/quran_models.dart';
import 'package:new_islamic_app_uk/URLs.dart';

class VerseCard extends StatelessWidget {
  final VerseInfo verse;
  final SurahInfo? surahInfo;
  final bool isBookmarked;
  final bool isExpanded;
  final Function(bool) onBookmarkChanged;
  final Function() onPlayAudio;
  final Function()? onTafsirToggled;
  final bool isBasmala;
  final double fontSize;
  final bool showArabicText;
  final bool showTranslation;
  final String arabicFontFamily;
  final int primaryColor;
  final bool isRtlTranslation;
  final TextAlign arabicTextAlignment;
  final TextAlign translationTextAlignment;

  const VerseCard({
    Key? key,
    required this.verse,
    this.surahInfo,
    this.isBookmarked = false,
    this.isExpanded = false,
    required this.onBookmarkChanged,
    required this.onPlayAudio,
    this.onTafsirToggled,
    this.isBasmala = false,
    this.fontSize = 24.0,
    this.showArabicText = true,
    this.showTranslation = true,
    this.arabicFontFamily = 'ScheherazadeNew',
    this.primaryColor = 0xFF4A5568,
    this.isRtlTranslation = false,
    this.arabicTextAlignment = TextAlign.center,
    this.translationTextAlignment = TextAlign.left,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final goldColor = Color(URLs().colorGold);
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: isDarkMode 
            ? Colors.grey[850] 
            : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 12,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
        border: isBookmarked
            ? Border.all(color: goldColor, width: 1.5)
            : null,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Verse header with number and actions
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (!isBasmala)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Color(primaryColor).withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Verse ${verse.verseNumber}',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Color(primaryColor),
                      ),
                    ),
                  ),
                
                if (isBookmarked)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      color: goldColor.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.bookmark,
                          size: 14,
                          color: goldColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Bookmarked',
                          style: GoogleFonts.poppins(
                            fontSize: 11,
                            fontWeight: FontWeight.w500,
                            color: goldColor,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Arabic text
            if (showArabicText)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? Color(0xFF1E2030).withOpacity(0.3)
                      : Color(0xFFF8F9FA),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: isDarkMode
                        ? Colors.grey[700]!.withOpacity(0.3)
                        : Colors.grey[300]!,
                    width: 0.5,
                  ),
                ),
                child: Directionality(
                  textDirection: TextDirection.rtl,
                  child: Text(
                    verse.arabicText,
                    style: _getArabicFontStyle(
                      fontSize: fontSize,
                      context: context,
                    ),
                    textAlign: arabicTextAlignment,
                  ),
                ),
              ),
              
            if (showArabicText && showTranslation)
              const SizedBox(height: 12),
            
            // Translation text
            if (showTranslation)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? Colors.grey[900]!.withOpacity(0.3)
                      : Colors.grey[100]!.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Directionality(
                  textDirection: isRtlTranslation ? TextDirection.rtl : TextDirection.ltr,
                  child: Text(
                    verse.translation,
                    style: GoogleFonts.poppins(
                      fontSize: fontSize * 0.7,
                      height: 1.6,
                      color: isDarkMode 
                          ? Colors.grey[200] 
                          : Colors.grey[800],
                    ),
                    textAlign: translationTextAlignment,
                  ),
                ),
              ),
            
            // Tafsir section (expandable)
            if (verse.tafsir != null && isExpanded)
              Column(
                children: [
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? Color(primaryColor).withOpacity(0.1)
                          : Color(primaryColor).withOpacity(0.05),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Color(primaryColor).withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.auto_stories_rounded,
                              size: 16,
                              color: Color(primaryColor),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Tafsir',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Color(primaryColor),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          verse.tafsir ?? '',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            height: 1.6,
                            color: isDarkMode 
                                ? Colors.grey[300] 
                                : Colors.grey[800],
                          ),
                          textAlign: TextAlign.justify,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            
            // Action buttons
            const SizedBox(height: 16),
            _buildActionButtons(context, isDarkMode, goldColor),
          ],
        ),
      ),
    );
  }
  
  // Arabic font style
  TextStyle _getArabicFontStyle({
    required double fontSize,
    required BuildContext context,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return TextStyle(
      fontFamily: arabicFontFamily,
      fontSize: fontSize,
      height: 1.8,
      color: isDarkMode ? Colors.white : Colors.black87,
    );
  }
  
  // Action buttons at the bottom of the card
  Widget _buildActionButtons(BuildContext context, bool isDarkMode, Color goldColor) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Bookmark indicator
        GestureDetector(
          onTap: () => onBookmarkChanged(!isBookmarked),
          child: Row(
            children: [
              Icon(
                isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                size: 18,
                color: isBookmarked ? goldColor : isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Text(
                isBookmarked ? 'Bookmarked' : 'Bookmark',
                style: GoogleFonts.poppins(
                  fontSize: 13,
                  fontWeight: isBookmarked ? FontWeight.w600 : FontWeight.w500,
                  color: isBookmarked ? goldColor : isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        
        // More options button
        IconButton(
          icon: Icon(
            Icons.more_horiz,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
          onPressed: () => _showActionsMenu(context, isDarkMode),
          splashRadius: 20,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ],
    );
  }
  
  // Compact action buttons for the verse
  Widget _buildVerseActions(BuildContext context, bool isDarkMode) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildCompactActionButton(
          context,
          Icons.play_circle_outline,
          'Play',
          onPlayAudio,
          Color(primaryColor),
        ),
        _buildCompactActionButton(
          context,
          isBookmarked ? Icons.bookmark : Icons.bookmark_outline,
          isBookmarked ? 'Bookmarked' : 'Bookmark',
          () => onBookmarkChanged(!isBookmarked),
          isBookmarked ? Color(URLs().colorGold) : null,
        ),
        _buildCompactActionButton(
          context,
          Icons.content_copy_outlined,
          'Copy',
          () => _copyVerse(context),
          null,
        ),
        _buildCompactActionButton(
          context,
          Icons.share_outlined,
          'Share',
          () => _shareVerse(context),
          null,
        ),
      ],
    );
  }
  
  // Helper to build a compact action button
  Widget _buildCompactActionButton(
    BuildContext context,
    IconData icon,
    String label,
    VoidCallback onTap,
    Color? color,
  ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final buttonColor = color ?? (isDarkMode ? Colors.grey[400] : Colors.grey[700]);
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: buttonColor, size: 22),
            const SizedBox(height: 4),
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: buttonColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Show a modal bottom sheet with actions
  void _showActionsMenu(BuildContext context, bool isDarkMode) {
    showModalBottomSheet(
      context: context,
      backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Color(primaryColor).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.menu,
                        size: 16,
                        color: Color(primaryColor),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Verse Options',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isDarkMode ? Colors.white : Colors.grey[800],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              _buildVerseActions(context, isDarkMode),
              
              // Show tafsir option if available
              if (verse.tafsir != null) ...[
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 8),
                ListTile(
                  leading: Icon(
                    isExpanded ? Icons.visibility_off : Icons.visibility,
                    color: Color(primaryColor),
                  ),
                  title: Text(
                    isExpanded ? 'Hide Tafsir' : 'Show Tafsir',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    if (onTafsirToggled != null) {
                      onTafsirToggled!();
                    }
                  },
                ),
              ],
            ],
          ),
        );
      },
    );
  }
  
  // Copy verse text to clipboard
  void _copyVerse(BuildContext context) {
    final String textToCopy = showArabicText && showTranslation
        ? '${verse.arabicText}\n\n${verse.translation}'
        : showArabicText
            ? verse.arabicText
            : verse.translation;
    
    Clipboard.setData(ClipboardData(text: textToCopy));
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Verse copied to clipboard'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
  
  // Share verse text
  void _shareVerse(BuildContext context) {
    final surahName = surahInfo?.nameEnglish ?? 'Quran';
    final verseRef = 'Surah $surahName, Verse ${verse.verseNumber}';
    
    final String textToShare = showArabicText && showTranslation
        ? '${verse.arabicText}\n\n${verse.translation}\n\n$verseRef'
        : showArabicText
            ? '${verse.arabicText}\n\n$verseRef'
            : '${verse.translation}\n\n$verseRef';
    
    Share.share(textToShare, subject: 'Quran Verse');
  }
} 