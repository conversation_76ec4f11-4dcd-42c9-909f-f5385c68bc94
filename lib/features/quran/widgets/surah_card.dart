import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';

class SurahCard extends StatelessWidget {
  final SurahInfo surah;
  final VoidCallback onTap;
  final VoidCallback onInfoTap;
  final int primaryColor;
  
  const SurahCard({
    Key? key,
    required this.surah,
    required this.onTap,
    required this.onInfoTap,
    required this.primaryColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              // Surah number circle
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Color(primaryColor).withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    '${surah.number}',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Color(primaryColor),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // Surah details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // First row with name and revelation type
                    Row(
                      children: [
                        // Surah name in English
                        Flexible(
                          child: Text(
                            surah.nameEnglish,
                            style: GoogleFonts.poppins(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).textTheme.titleLarge?.color,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Revelation type tag
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: surah.placeOfRevelation.toLowerCase() == 'meccan' 
                                ? Color(primaryColor).withOpacity(0.1) 
                                : Colors.blue.withOpacity(0.15),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            surah.placeOfRevelation,
                            style: GoogleFonts.poppins(
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                              color: surah.placeOfRevelation.toLowerCase() == 'meccan' 
                                  ? Color(primaryColor)
                                  : Colors.blue[700],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    // Second row with Arabic name and verses count
                    Row(
                      children: [
                        // Arabic name
                        Flexible(
                          child: Text(
                            surah.nameArabic,
                            style: GoogleFonts.scheherazadeNew(
                              fontSize: 16,
                              color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          ' • ${surah.versesCount} verses',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // Info button
              IconButton(
                icon: Icon(
                  Icons.info_outline_rounded,
                  size: 20,
                  color: Color(primaryColor),
                ),
                onPressed: onInfoTap,
                tooltip: 'Surah information',
                splashRadius: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }
} 