import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AudioControls extends StatelessWidget {
  final int currentPlayingVerse;
  final bool audioPaused;
  final VoidCallback onPause;
  final VoidCallback onResume;
  final VoidCallback onStop;
  final int primaryColor;

  const AudioControls({
    Key? key,
    required this.currentPlayingVerse,
    required this.audioPaused,
    required this.onPause,
    required this.onResume,
    required this.onStop,
    required this.primaryColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 20,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 14,
        ),
        decoration: BoxDecoration(
          color: Color(primaryColor),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Color(primaryColor).withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.music_note_rounded,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Playing Audio',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    'Verse $currentPlayingVerse',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ],
              ),
            ),
            Row(
              children: [
                IconButton(
                  icon: Icon(
                    audioPaused ? Icons.play_circle_fill_rounded : Icons.pause_circle_filled_rounded,
                    color: Colors.white,
                    size: 28,
                  ),
                  onPressed: audioPaused ? onResume : onPause,
                ),
                IconButton(
                  icon: const Icon(
                    Icons.stop_circle_rounded,
                    color: Colors.white,
                    size: 28,
                  ),
                  onPressed: onStop,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
} 