import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/features/quran/controllers/quran_settings_controller.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';

import 'settings/display_settings_tab.dart';
import 'settings/font_settings_tab.dart';
import 'settings/translation_settings_tab.dart';
import 'settings/about_settings_tab.dart';

class ReaderSettingsPage extends StatefulWidget {
  final int primaryColor;

  const ReaderSettingsPage({
    Key? key,
    required this.primaryColor,
  }) : super(key: key);

  @override
  State<ReaderSettingsPage> createState() => _ReaderSettingsPageState();
}

class _ReaderSettingsPageState extends State<ReaderSettingsPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late QuranSettingsController _settingsController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _settingsController = Get.find<QuranSettingsController>();
    
    // Add debug print statement to verify controller is found
    print('QuranSettingsController found: ${_settingsController.fontSize.value}');
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.white,
      appBar: AppBar(
        title: Text(
          'Reader Settings',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: isDarkMode ? Colors.grey[900] : Colors.white,
        elevation: 0,
        bottom: TabBar(
            controller: _tabController,
            indicatorColor: Color(widget.primaryColor),
            labelColor: Color(widget.primaryColor),
          unselectedLabelColor: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            labelStyle: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          tabs: const [
            Tab(
              icon: Icon(Icons.visibility_outlined),
              text: 'Display',
            ),
            Tab(
              icon: Icon(Icons.font_download_outlined),
              text: 'Font',
            ),
            Tab(
              icon: Icon(Icons.translate_outlined),
              text: 'Translation',
            ),
            Tab(
              icon: Icon(Icons.info_outline),
              text: 'About',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Display settings
          DisplaySettingsTab(
            primaryColor: widget.primaryColor,
          ),
          
          // Font settings
          FontSettingsTab(
            primaryColor: widget.primaryColor,
          ),
          
          // Translation settings
          TranslationSettingsTab(
            primaryColor: widget.primaryColor,
          ),
          
          // About settings
          AboutSettingsTab(
            primaryColor: widget.primaryColor,
          ),
        ],
      ),
    );
  }
} 