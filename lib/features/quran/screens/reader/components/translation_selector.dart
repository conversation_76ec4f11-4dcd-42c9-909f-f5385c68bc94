import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';
import 'package:new_islamic_app_uk/features/quran/services/quran_service.dart';

class TranslationSelector extends StatefulWidget {
  final QuranTranslation selectedTranslation;
  final Function(QuranTranslation) onTranslationChanged;
  final QuranService quranService;
  final int primaryColor;

  const TranslationSelector({
    Key? key,
    required this.selectedTranslation,
    required this.onTranslationChanged,
    required this.quranService,
    required this.primaryColor,
  }) : super(key: key);

  @override
  State<TranslationSelector> createState() => _TranslationSelectorState();
}

class _TranslationSelectorState extends State<TranslationSelector> {
  bool _englishExpanded = true;
  bool _europeanExpanded = false;
  bool _asianExpanded = false;

  @override
  void initState() {
    super.initState();
    // Expand the section containing the currently selected translation
    _setInitialExpandedSections();
  }

  void _setInitialExpandedSections() {
    switch (widget.selectedTranslation) {
      case QuranTranslation.enSaheeh:
      case QuranTranslation.enClearQuran:
        _englishExpanded = true;
        break;
      case QuranTranslation.frHamidullah:
      case QuranTranslation.esSpanish:
      case QuranTranslation.ptPortuguese:
      case QuranTranslation.itPiccardo:
      case QuranTranslation.nlSiregar:
      case QuranTranslation.ruKuliev:
      case QuranTranslation.sveSwedish:
      case QuranTranslation.trSaheeh:
        _europeanExpanded = true;
        break;
      case QuranTranslation.urUrdu:
      case QuranTranslation.bnBengali:
      case QuranTranslation.zhChinese:
      case QuranTranslation.faFarsi:
      case QuranTranslation.idIndonesian:
      case QuranTranslation.mlAbdulHameed:
        _asianExpanded = true;
        break;
      default:
        _englishExpanded = true;
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return ListView(
      children: [
        // Currently selected translation card
        Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF252736) : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Current Translation',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.white70 : Colors.grey[700],
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Color(widget.primaryColor).withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.language,
                      color: Color(widget.primaryColor),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.quranService.getTranslationDisplayName(widget.selectedTranslation),
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        
        // English translations section
        _buildTranslationSection(
          title: 'English',
          isExpanded: _englishExpanded,
          onExpansionChanged: (value) => setState(() => _englishExpanded = value),
          translations: [
            QuranTranslation.enSaheeh,
            QuranTranslation.enClearQuran,
          ],
          isDarkMode: isDarkMode,
        ),
        
        const SizedBox(height: 8),
        
        // European languages section
        _buildTranslationSection(
          title: 'European Languages',
          isExpanded: _europeanExpanded,
          onExpansionChanged: (value) => setState(() => _europeanExpanded = value),
          translations: [
            QuranTranslation.frHamidullah,
            QuranTranslation.esSpanish,
            QuranTranslation.ptPortuguese,
            QuranTranslation.itPiccardo,
            QuranTranslation.nlSiregar,
            QuranTranslation.ruKuliev,
            QuranTranslation.sveSwedish,
            QuranTranslation.trSaheeh,
          ],
          isDarkMode: isDarkMode,
        ),
        
        const SizedBox(height: 8),
        
        // Asian languages section
        _buildTranslationSection(
          title: 'Asian Languages',
          isExpanded: _asianExpanded,
          onExpansionChanged: (value) => setState(() => _asianExpanded = value),
          translations: [
            QuranTranslation.urUrdu,
            QuranTranslation.bnBengali,
            QuranTranslation.zhChinese,
            QuranTranslation.faFarsi,
            QuranTranslation.idIndonesian,
            QuranTranslation.mlAbdulHameed,
          ],
          isDarkMode: isDarkMode,
        ),
      ],
    );
  }

  Widget _buildTranslationSection({
    required String title,
    required bool isExpanded,
    required Function(bool) onExpansionChanged,
    required List<QuranTranslation> translations,
    required bool isDarkMode,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF252736) : Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
          colorScheme: Theme.of(context).colorScheme.copyWith(
                secondary: Color(widget.primaryColor),
              ),
        ),
        child: ExpansionTile(
          title: Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.grey[800],
            ),
          ),
          leading: Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Color(widget.primaryColor).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getLanguageIcon(title),
              size: 16,
              color: Color(widget.primaryColor),
            ),
          ),
          initiallyExpanded: isExpanded,
          onExpansionChanged: onExpansionChanged,
          tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          childrenPadding: const EdgeInsets.symmetric(horizontal: 8),
          children: [
            ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: translations.length,
              itemBuilder: (context, index) => _buildTranslationOption(
                translation: translations[index],
                isDarkMode: isDarkMode,
              ),
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  IconData _getLanguageIcon(String title) {
    switch (title) {
      case 'English':
        return Icons.language;
      case 'European Languages':
        return Icons.euro_rounded;
      case 'Asian Languages':
        return Icons.east;
      default:
        return Icons.language;
    }
  }

  Widget _buildTranslationOption({
    required QuranTranslation translation,
    required bool isDarkMode,
  }) {
    final bool isSelected = widget.selectedTranslation == translation;
    
    return ListTile(
      selected: isSelected,
      selectedTileColor: Color(widget.primaryColor).withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      dense: true,
      title: Text(
        widget.quranService.getTranslationDisplayName(translation),
        style: GoogleFonts.poppins(
          fontSize: 14,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          color: isSelected
              ? Color(widget.primaryColor)
              : isDarkMode ? Colors.white70 : Colors.grey[800],
        ),
      ),
      leading: Radio<QuranTranslation>(
        value: translation,
        groupValue: widget.selectedTranslation,
        activeColor: Color(widget.primaryColor),
        onChanged: (value) {
          if (value != null) {
            widget.onTranslationChanged(value);
          }
        },
      ),
      onTap: () => widget.onTranslationChanged(translation),
    );
  }
} 