import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/features/quran/controllers/quran_settings_controller.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';
import 'package:new_islamic_app_uk/features/quran/services/quran_service.dart';

import 'settings_widgets.dart';

class TranslationSettingsTab extends StatelessWidget {
  final int primaryColor;
  final QuranSettingsController controller = Get.find<QuranSettingsController>();
  final QuranService _quranService = Get.find<QuranService>();

  TranslationSettingsTab({
    Key? key,
    required this.primaryColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionTitle(
            title: 'Select Translation',
            primaryColor: primaryColor,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: 16),
          _buildTranslationsGrid(isDarkMode),
        ],
      ),
    );
  }
  
  Widget _buildTranslationsGrid(bool isDarkMode) {
    // Group translations by category
    final Map<String, List<QuranTranslation>> translationsByCategory = {
      'English': [
        QuranTranslation.enSaheeh,
        QuranTranslation.enClearQuran,
      ],
      'Asian Languages': [
        QuranTranslation.urUrdu,
        QuranTranslation.faFarsi,
        QuranTranslation.bnBengali,
        QuranTranslation.zhChinese,
        QuranTranslation.idIndonesian,
        QuranTranslation.mlAbdulHameed,
      ],
      'European Languages': [
        QuranTranslation.frHamidullah,
        QuranTranslation.trSaheeh,
        QuranTranslation.ruKuliev,
        QuranTranslation.ptPortuguese,
        QuranTranslation.itPiccardo,
        QuranTranslation.nlSiregar,
        QuranTranslation.sveSwedish,
        QuranTranslation.esSpanish,
      ],
    };
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: translationsByCategory.entries.map((entry) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 12.0),
              child: Text(
                entry.key,
                style: GoogleFonts.poppins(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.white70 : Colors.grey[700],
                ),
              ),
            ),
            _buildTranslationCategory(entry.value, isDarkMode),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
          ],
        );
      }).toList(),
    );
  }
  
  Widget _buildTranslationCategory(List<QuranTranslation> translations, bool isDarkMode) {
    return Column(
      children: translations.map((translation) {
        return Obx(() => _buildTranslationTile(
          translation,
          isDarkMode,
          controller.selectedTranslation.value == translation,
        ));
      }).toList(),
    );
  }
  
  Widget _buildTranslationTile(QuranTranslation translation, bool isDarkMode, bool isSelected) {
    final String displayName = _quranService.getTranslationDisplayName(translation);
    final String translatorName = _getTranslatorName(translation);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: TranslationCard(
        title: displayName,
        subtitle: translatorName,
        isSelected: isSelected,
        onTap: () {
          controller.updateTranslation(translation);
          // Add debug print to verify the controller's value was updated
          print('Translation selected: ${controller.selectedTranslation.value}');
          print('Is RTL translation: ${controller.isRtlTranslation.value}');
          
          Get.snackbar(
            'Translation Updated',
            'Changed to $displayName',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Color(primaryColor).withOpacity(0.9),
            colorText: Colors.white,
            duration: const Duration(seconds: 1),
            margin: const EdgeInsets.all(8),
            borderRadius: 10,
          );
        },
        primaryColor: primaryColor,
        isDarkMode: isDarkMode,
      ),
    );
  }
  
  // Get the translator name for UI display
  String _getTranslatorName(QuranTranslation translation) {
    switch (translation) {
      // English translations
      case QuranTranslation.enSaheeh:
        return 'Saheeh International';
      case QuranTranslation.enClearQuran:
        return 'Dr. Mustafa Khattab';
      
      // European languages
      case QuranTranslation.frHamidullah:
        return 'Muhammad Hamidullah';
      case QuranTranslation.trSaheeh:
        return 'Islamic Foundation';
      case QuranTranslation.ruKuliev:
        return 'Elmir Kuliev';
      case QuranTranslation.ptPortuguese:
        return 'Helmi Nasr';
      case QuranTranslation.itPiccardo:
        return 'Hamza Piccardo';
      case QuranTranslation.nlSiregar:
        return 'Sofian Siregar';
      case QuranTranslation.sveSwedish:
        return 'Knut Bernström';
      case QuranTranslation.esSpanish:
        return 'Raul Gonzalez Bórnez';
      
      // Asian languages
      case QuranTranslation.faFarsi:
        return 'Hussein Taji';
      case QuranTranslation.urJalandhry:
        return 'Fateh Muhammad Jalandhry';
      case QuranTranslation.urUrdu:
        return 'Ahmed Ali';
      case QuranTranslation.bnBengali:
        return 'Muhiuddin Khan';
      case QuranTranslation.zhChinese:
        return 'Muhammad Ma Jian';
      case QuranTranslation.idIndonesian:
        return 'Ministry of Religious Affairs';
      case QuranTranslation.mlAbdulHameed:
        return 'Abdul Hameed & Kunhi Mohammed';
      default:
        return 'Unknown Translator';
    }
  }
} 