import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/features/quran/controllers/quran_settings_controller.dart';

import 'settings_widgets.dart';
import 'preview_section.dart';

class FontSettingsTab extends StatelessWidget {
  final int primaryColor;
  final QuranSettingsController controller = Get.find<QuranSettingsController>();

  FontSettingsTab({
    Key? key,
    required this.primaryColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Font size section
          SectionTitle(
            title: 'Font Size',
            primaryColor: primaryColor,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: 16),
          _buildFontSizeControls(isDarkMode, context),
          const SizedBox(height: 24),
          
          // Arabic font style section
          SectionTitle(
            title: 'Arabic Font Style',
            primaryColor: primaryColor,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: 16),
          _buildArabicFontSelector(isDarkMode),
          const SizedBox(height: 24),
          
          // Text alignment section
          SectionTitle(
            title: 'Text Alignment',
            primaryColor: primaryColor,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: 16),
          _buildTextAlignmentSelector(isDarkMode, context),
          const SizedBox(height: 24),
          
          // Preview section
          Obx(() => PreviewSection(
            primaryColor: primaryColor,
            isDarkMode: isDarkMode,
            showArabicText: controller.showArabicText.value,
            showTranslation: controller.showTranslation.value,
            fontSize: controller.fontSize.value,
            arabicFontFamily: controller.arabicFontFamily.value,
            isRtlTranslation: controller.isRtlTranslation.value,
            arabicTextAlignment: controller.arabicTextAlignment.value,
            translationTextAlignment: controller.translationTextAlignment.value,
          )),
          
          // Add some bottom padding for scrolling
          const SizedBox(height: 24),
        ],
      ),
    );
  }
  
  Widget _buildFontSizeControls(bool isDarkMode, BuildContext context) {
    final List<Map<String, dynamic>> presets = [
      {'label': 'Small', 'value': 20.0, 'size': 12.0},
      {'label': 'Normal', 'value': 26.0, 'size': 14.0},
      {'label': 'Large', 'value': 32.0, 'size': 16.0},
      {'label': 'Extra Large', 'value': 38.0, 'size': 18.0},
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF252736) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Size presets
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: presets.map((preset) {
              return Obx(() {
                final bool isSelected = (controller.fontSize.value - preset['value']).abs() < 2;
                
                return GestureDetector(
                  onTap: () {
                    controller.updateFontSize(preset['value']);
                    Get.snackbar(
                      'Font Size Updated',
                      'Font size set to ${preset['label']}',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Color(primaryColor).withOpacity(0.9),
                      colorText: Colors.white,
                      duration: const Duration(seconds: 1),
                      margin: const EdgeInsets.all(8),
                      borderRadius: 10,
                    );
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: 70,
                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? Color(primaryColor)
                          : isDarkMode ? const Color(0xFF2A2C3D) : Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: isSelected 
                          ? [
                              BoxShadow(
                                color: Color(primaryColor).withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : null,
                      border: isSelected 
                          ? null
                          : Border.all(
                              color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                              width: 1,
                            ),
                    ),
                    child: Column(
                      children: [
                        Icon(
                          Icons.format_size,
                          size: 16,
                          color: isSelected ? Colors.white : Color(primaryColor),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          preset['label'],
                          style: GoogleFonts.poppins(
                            fontSize: preset['size'],
                            fontWeight: FontWeight.w500,
                            color: isSelected
                                ? Colors.white
                                : isDarkMode ? Colors.white70 : Colors.grey[800],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                );
              });
            }).toList(),
          ),
          
          const SizedBox(height: 24),
          
          // Custom size with improved slider
          Obx(() => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Custom Size',
                    style: GoogleFonts.poppins(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: isDarkMode ? Colors.white : Colors.grey[800],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Color(primaryColor).withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${controller.fontSize.value.round()}',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(primaryColor),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  InkWell(
                    onTap: () {
                      if (controller.fontSize.value > 18) {
                        controller.updateFontSize(controller.fontSize.value - 1);
                      }
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.remove,
                        size: 18,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                  ),
                  Expanded(
                    child: SliderTheme(
                      data: SliderThemeData(
                        trackHeight: 8,
                        activeTrackColor: Color(primaryColor),
                        inactiveTrackColor: isDarkMode ? Colors.grey[700] : Colors.grey[300],
                        thumbColor: Colors.white,
                        thumbShape: const RoundSliderThumbShape(
                          enabledThumbRadius: 12,
                          elevation: 4,
                        ),
                        overlayColor: Color(primaryColor).withOpacity(0.2),
                      ),
                      child: Slider(
                        value: controller.fontSize.value,
                        min: 18,
                        max: 42,
                        divisions: 24,
                        onChanged: (value) => controller.updateFontSize(value),
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      if (controller.fontSize.value < 42) {
                        controller.updateFontSize(controller.fontSize.value + 1);
                      }
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.add,
                        size: 18,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          )),
        ],
      ),
    );
  }
  
  Widget _buildArabicFontSelector(bool isDarkMode) {
    final fonts = [
      {'value': 'ScheherazadeNew', 'label': 'Scheherazade'},
      {'value': 'Amiri', 'label': 'Amiri'},
      {'value': 'Lateef', 'label': 'Lateef'},
      {'value': 'Noto Naskh Arabic', 'label': 'Noto Naskh'},
      {'value': 'Noto Sans Arabic', 'label': 'Noto Sans'},
      {'value': 'me_quran', 'label': 'Me Quran'},
      {'value': 'KFGQPC Uthmanic Script HAFS', 'label': 'Uthmanic HAFS'},
      {'value': 'Scheherazade-Regular', 'label': 'Scheherazade Classic'},
    ];

    // Create a grid of font options
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        mainAxisSpacing: 16,
        crossAxisSpacing: 16,
      ),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: fonts.length,
      itemBuilder: (context, index) {
        final font = fonts[index];
        
        return Obx(() {
          final bool isSelected = controller.arabicFontFamily.value == font['value'];
          
          return FontStyleCard(
            fontLabel: font['label']!,
            fontValue: font['value']!,
            previewText: 'بِسْمِ اللَّهِ',
            isSelected: isSelected,
            onTap: () {
              controller.updateArabicFont(font['value']!);
              Get.snackbar(
                'Font Updated',
                'Arabic font set to ${font['label']}',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Color(primaryColor).withOpacity(0.9),
                colorText: Colors.white,
                duration: const Duration(seconds: 1),
                margin: const EdgeInsets.all(8),
                borderRadius: 10,
              );
            },
            primaryColor: primaryColor,
            isDarkMode: isDarkMode,
          );
        });
      },
    );
  }
  
  Widget _buildTextAlignmentSelector(bool isDarkMode, BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF252736) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Arabic text alignment
          Text(
            'Arabic Text Alignment',
            style: GoogleFonts.poppins(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.grey[800],
            ),
          ),
          const SizedBox(height: 12),
          
          Obx(() => Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildAlignmentButton(
                isDarkMode,
                icon: Icons.format_align_left,
                alignment: TextAlign.left,
                currentAlignment: controller.arabicTextAlignment.value,
                onTap: () => controller.updateArabicTextAlignment(TextAlign.left),
              ),
              _buildAlignmentButton(
                isDarkMode,
                icon: Icons.format_align_center,
                alignment: TextAlign.center,
                currentAlignment: controller.arabicTextAlignment.value,
                onTap: () => controller.updateArabicTextAlignment(TextAlign.center),
              ),
              _buildAlignmentButton(
                isDarkMode,
                icon: Icons.format_align_right,
                alignment: TextAlign.right,
                currentAlignment: controller.arabicTextAlignment.value,
                onTap: () => controller.updateArabicTextAlignment(TextAlign.right),
              ),
              _buildAlignmentButton(
                isDarkMode,
                icon: Icons.format_align_justify,
                alignment: TextAlign.justify,
                currentAlignment: controller.arabicTextAlignment.value,
                onTap: () => controller.updateArabicTextAlignment(TextAlign.justify),
              ),
            ],
          )),
          
          const SizedBox(height: 24),
          const Divider(),
          const SizedBox(height: 16),
          
          // Translation text alignment
          Text(
            'Translation Text Alignment',
            style: GoogleFonts.poppins(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.grey[800],
            ),
          ),
          const SizedBox(height: 12),
          
          Obx(() => Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildAlignmentButton(
                isDarkMode,
                icon: Icons.format_align_left,
                alignment: TextAlign.left,
                currentAlignment: controller.translationTextAlignment.value,
                onTap: () => controller.updateTranslationTextAlignment(TextAlign.left),
                isDisabled: controller.isRtlTranslation.value,
              ),
              _buildAlignmentButton(
                isDarkMode,
                icon: Icons.format_align_center,
                alignment: TextAlign.center,
                currentAlignment: controller.translationTextAlignment.value,
                onTap: () => controller.updateTranslationTextAlignment(TextAlign.center),
              ),
              _buildAlignmentButton(
                isDarkMode,
                icon: Icons.format_align_right,
                alignment: TextAlign.right,
                currentAlignment: controller.translationTextAlignment.value,
                onTap: () => controller.updateTranslationTextAlignment(TextAlign.right),
                isDisabled: !controller.isRtlTranslation.value,
              ),
              _buildAlignmentButton(
                isDarkMode,
                icon: Icons.format_align_justify,
                alignment: TextAlign.justify,
                currentAlignment: controller.translationTextAlignment.value,
                onTap: () => controller.updateTranslationTextAlignment(TextAlign.justify),
              ),
            ],
          )),
          
          if (controller.isRtlTranslation.value) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.amber.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16,
                    color: Colors.amber[700],
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Some alignment options are disabled for RTL languages',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.amber[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
  
  Widget _buildAlignmentButton(
    bool isDarkMode, {
    required IconData icon,
    required TextAlign alignment,
    required TextAlign currentAlignment,
    required VoidCallback onTap,
    bool isDisabled = false,
  }) {
    final isSelected = alignment == currentAlignment;
    
    return GestureDetector(
      onTap: isDisabled ? null : onTap,
      child: Opacity(
        opacity: isDisabled ? 0.4 : 1.0,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: isSelected 
                ? Color(primaryColor)
                : isDarkMode ? const Color(0xFF2A2C3D) : Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            boxShadow: isSelected 
                ? [
                    BoxShadow(
                      color: Color(primaryColor).withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
            border: isSelected 
                ? null
                : Border.all(
                    color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                    width: 1,
                  ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 24,
                color: isSelected 
                    ? Colors.white
                    : isDarkMode ? Colors.white70 : Colors.grey[700],
              ),
              if (isSelected) ...[
                const SizedBox(height: 6),
                Container(
                  width: 16,
                  height: 3,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(1.5),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
} 