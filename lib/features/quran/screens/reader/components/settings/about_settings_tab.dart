import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/features/quran/controllers/quran_settings_controller.dart';

import 'settings_widgets.dart';

class AboutSettingsTab extends StatelessWidget {
  final int primaryColor;
  final QuranSettingsController controller = Get.find<QuranSettingsController>();

  AboutSettingsTab({
    Key? key,
    required this.primaryColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // App info section
          SectionTitle(
            title: 'About the App',
            primaryColor: primaryColor,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: 16),
          _buildInfoCard(
            isDarkMode,
            icon: Icons.info_outline,
            title: 'Version',
            content: '1.0.0',
          ),
          const SizedBox(height: 12),
          _buildInfoCard(
            isDarkMode,
            icon: Icons.code,
            title: 'Developed by',
            content: 'Islamic App UK Team',
          ),
          const SizedBox(height: 12),
          _buildInfoCard(
            isDarkMode,
            icon: Icons.book_outlined,
            title: 'Quran Source',
            content: 'Tanzil Project (Uthmanic Text)',
          ),
          
          const SizedBox(height: 24),
          
          // Credits section
          SectionTitle(
            title: 'Attributions',
            primaryColor: primaryColor,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: 16),
          _buildAttributionCard(
            isDarkMode,
            title: 'Quran Translations',
            content: 'Various translators credited in the translations section',
          ),
          const SizedBox(height: 12),
          _buildAttributionCard(
            isDarkMode,
            title: 'Arabic Fonts',
            content: 'ME_Quran, Scheherazade, Amiri and others provided under open licenses',
          ),
          const SizedBox(height: 12),
          _buildAttributionCard(
            isDarkMode,
            title: 'Audio Recitations',
            content: 'EveryAyah.com and MP3Quran.net',
          ),
          
          const SizedBox(height: 24),
          
          // Reset Settings Button
          _buildResetButton(isDarkMode, context),
          
          const SizedBox(height: 24),
        ],
      ),
    );
  }
  
  Widget _buildInfoCard(
    bool isDarkMode, {
    required IconData icon,
    required String title,
    required String content,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF252736) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Color(primaryColor).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              size: 24,
              color: Color(primaryColor),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Colors.grey[800],
                  ),
                ),
                Text(
                  content,
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    color: isDarkMode ? Colors.white70 : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildAttributionCard(
    bool isDarkMode, {
    required String title,
    required String content,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF252736) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.grey[800],
            ),
          ),
          const SizedBox(height: 6),
          Text(
            content,
            style: GoogleFonts.poppins(
              fontSize: 13,
              color: isDarkMode ? Colors.white70 : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildResetButton(bool isDarkMode, BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode 
            ? Colors.red.shade900.withOpacity(0.2) 
            : Colors.red.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDarkMode 
              ? Colors.red.shade800.withOpacity(0.5) 
              : Colors.red.shade200,
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Reset Settings',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.red.shade300 : Colors.red.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'This will reset all your reading preferences to default values.',
            style: GoogleFonts.poppins(
              fontSize: 13,
              color: isDarkMode ? Colors.red.shade100 : Colors.red.shade700,
            ),
          ),
          const SizedBox(height: 16),
          Center(
            child: ElevatedButton.icon(
              onPressed: () {
                _showResetConfirmation(context, isDarkMode);
              },
              icon: const Icon(Icons.restart_alt_rounded),
              label: Text(
                'Reset to Defaults',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w500,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: isDarkMode ? Colors.red.shade800 : Colors.red.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  void _showResetConfirmation(BuildContext context, bool isDarkMode) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Reset Settings?',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        content: Text(
          'This will reset all your reading preferences including font size, '
          'display options, and selected translation. This cannot be undone.',
          style: GoogleFonts.poppins(
            fontSize: 14,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(
                color: isDarkMode ? Colors.white70 : Colors.grey[700],
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              // Reset to defaults
              controller.resetToDefaults();
              Navigator.pop(context);
              
              // Show confirmation
              Get.snackbar(
                'Settings Reset',
                'All settings have been reset to defaults',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.green.withOpacity(0.9),
                colorText: Colors.white,
                duration: const Duration(seconds: 2),
                margin: const EdgeInsets.all(8),
                borderRadius: 10,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
              foregroundColor: Colors.white,
            ),
            child: Text(
              'Reset',
              style: GoogleFonts.poppins(),
            ),
          ),
        ],
      ),
    );
  }
} 