import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/quran/controllers/quran_settings_controller.dart';

import 'settings_widgets.dart';
import 'preview_section.dart';

class DisplaySettingsTab extends StatefulWidget {
  final int primaryColor;
  final QuranSettingsController controller = Get.find<QuranSettingsController>();

  DisplaySettingsTab({
    Key? key,
    required this.primaryColor,
  }) : super(key: key);

  @override
  State<DisplaySettingsTab> createState() => _DisplaySettingsTabState();
}

class _DisplaySettingsTabState extends State<DisplaySettingsTab> {
  final URLs _urls = URLs();
  final List<String> _readingModes = ['Standard', 'Focus Mode', 'Night Mode'];
  final RxString _selectedMode = 'Standard'.obs;

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Content Display Section
          SectionTitle(
            title: 'Content Display',
            primaryColor: widget.primaryColor,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: 16),
          _buildContentDisplayToggles(isDarkMode),
          const SizedBox(height: 24),
          
          // Reading Mode Section
          SectionTitle(
            title: 'Reading Mode',
            primaryColor: widget.primaryColor,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: 16),
          _buildReadingModeSelector(isDarkMode),
          const SizedBox(height: 24),
          
          // Font Size Section
          SectionTitle(
            title: 'Font Size',
            primaryColor: widget.primaryColor,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: 16),
          _buildFontSizeControls(isDarkMode),
          const SizedBox(height: 24),
          
          // Enhanced Preview Section
          SectionTitle(
            title: 'Preview',
            primaryColor: widget.primaryColor,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: 16),
          PreviewSection(
            primaryColor: widget.primaryColor,
            isDarkMode: isDarkMode,
            showArabicText: widget.controller.showArabicText.value,
            showTranslation: widget.controller.showTranslation.value,
            fontSize: widget.controller.fontSize.value,
            arabicFontFamily: widget.controller.arabicFontFamily.value,
            isRtlTranslation: widget.controller.isRtlTranslation.value,
            arabicTextAlignment: widget.controller.arabicTextAlignment.value,
            translationTextAlignment: widget.controller.translationTextAlignment.value,
          ),
          
          // Add some bottom padding for scrolling
          const SizedBox(height: 24),
        ],
      ),
    );
  }
  
  Widget _buildContentDisplayToggles(bool isDarkMode) {
    return Row(
      children: [
        Expanded(
          child: Obx(() => ToggleCard(
            icon: Icons.language_rounded,
            title: 'Arabic Text',
            subtitle: 'Original Quran text',
            isActive: widget.controller.showArabicText.value,
            onToggle: (value) {
              widget.controller.toggleArabicText(value);
              Get.snackbar(
                'Settings Updated',
                'Arabic text visibility has been ${value ? 'enabled' : 'disabled'}',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Color(widget.primaryColor).withOpacity(0.9),
                colorText: Colors.white,
                duration: const Duration(seconds: 1),
                margin: const EdgeInsets.all(8),
                borderRadius: 10,
              );
            },
            primaryColor: widget.primaryColor,
            isDarkMode: isDarkMode,
          )),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Obx(() => ToggleCard(
            icon: Icons.translate_rounded,
            title: 'Translation',
            subtitle: 'Translated meaning',
            isActive: widget.controller.showTranslation.value,
            onToggle: (value) {
              widget.controller.toggleTranslation(value);
              Get.snackbar(
                'Settings Updated',
                'Translation visibility has been ${value ? 'enabled' : 'disabled'}',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Color(widget.primaryColor).withOpacity(0.9),
                colorText: Colors.white,
                duration: const Duration(seconds: 1),
                margin: const EdgeInsets.all(8),
                borderRadius: 10,
              );
            },
            primaryColor: widget.primaryColor,
            isDarkMode: isDarkMode,
          )),
        ),
      ],
    );
  }
  
  Widget _buildReadingModeSelector(bool isDarkMode) {
    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF252736) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Text(
              'Choose your preferred reading experience',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
          ),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _readingModes.length,
            itemBuilder: (context, index) {
              final mode = _readingModes[index];
              
              // Get icon based on mode
              IconData modeIcon = Icons.menu_book_rounded;
              if (mode == 'Focus Mode') {
                modeIcon = Icons.center_focus_strong_rounded;
              } else if (mode == 'Night Mode') {
                modeIcon = Icons.nightlight_round;
              }
              
              // Get description based on mode
              String description = 'Default reading experience';
              if (mode == 'Focus Mode') {
                description = 'Distraction-free reading';
              } else if (mode == 'Night Mode') {
                description = 'Optimized for night reading';
              }
              
              return Obx(() {
                final isSelected = _selectedMode.value == mode;
                return InkWell(
                  onTap: () {
                    _selectedMode.value = mode;
                    
                    // Apply the selected reading mode
                    _applyReadingMode(mode);
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    margin: const EdgeInsets.symmetric(vertical: 4),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? Color(widget.primaryColor).withOpacity(0.1)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      child: Row(
                        children: [
                          Container(
                            width: 48,
                            height: 48,
                            decoration: BoxDecoration(
                              color: isSelected 
                                  ? Color(widget.primaryColor) 
                                  : Color(widget.primaryColor).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              modeIcon,
                              color: isSelected ? Colors.white : Color(widget.primaryColor),
                              size: 24,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  mode,
                                  style: GoogleFonts.poppins(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: isDarkMode ? Colors.white : Colors.grey[800],
                                  ),
                                ),
                                Text(
                                  description,
                                  style: GoogleFonts.poppins(
                                    fontSize: 12,
                                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Radio(
                            value: mode,
                            groupValue: _selectedMode.value,
                            activeColor: Color(widget.primaryColor),
                            onChanged: (value) {
                              if (value != null) {
                                _selectedMode.value = value.toString();
                                
                                // Apply the selected reading mode
                                _applyReadingMode(value.toString());
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              });
            },
          ),
        ],
      ),
    );
  }
  
  Widget _buildFontSizeControls(bool isDarkMode) {
    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF252736) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Font Size',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.white : Colors.grey[800],
                ),
              ),
              Obx(() => Text(
                '${widget.controller.fontSize.value.toInt()}',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(widget.primaryColor),
                ),
              )),
            ],
          ),
          const SizedBox(height: 16),
          Obx(() => Slider(
            value: widget.controller.fontSize.value,
            min: 14,
            max: 30,
            divisions: 16,
            activeColor: Color(widget.primaryColor),
            inactiveColor: Color(widget.primaryColor).withOpacity(0.2),
            onChanged: (value) {
              widget.controller.updateFontSize(value);
            },
          )),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Small',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
              Text(
                'Large',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  // Apply the selected reading mode
  void _applyReadingMode(String mode) {
    if (mode == 'Standard') {
      // Standard mode: Reset to default settings
      widget.controller.toggleArabicText(true);
      widget.controller.toggleTranslation(true);
      widget.controller.updateFontSize(22);
      widget.controller.updateArabicFont('ScheherazadeNew');
      widget.controller.updateArabicTextAlignment(TextAlign.center);
      widget.controller.updateTranslationTextAlignment(TextAlign.left);
      
      Get.snackbar(
        'Reading Mode Updated',
        'Standard mode applied',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Color(widget.primaryColor).withOpacity(0.9),
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
        margin: const EdgeInsets.all(8),
        borderRadius: 10,
      );
    } else if (mode == 'Focus Mode') {
      // Focus mode: Larger text, only Arabic
      widget.controller.toggleArabicText(true);
      widget.controller.toggleTranslation(false);
      widget.controller.updateFontSize(26);
      widget.controller.updateArabicFont('ScheherazadeNew');
      widget.controller.updateArabicTextAlignment(TextAlign.center);
      
      Get.snackbar(
        'Reading Mode Updated',
        'Focus mode applied - Arabic text only with larger font',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Color(widget.primaryColor).withOpacity(0.9),
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
        margin: const EdgeInsets.all(8),
        borderRadius: 10,
      );
    } else if (mode == 'Night Mode') {
      // Night mode: Both text types, smaller font
      widget.controller.toggleArabicText(true);
      widget.controller.toggleTranslation(true);
      widget.controller.updateFontSize(18);
      widget.controller.updateArabicFont('Amiri');
      widget.controller.updateArabicTextAlignment(TextAlign.right);
      widget.controller.updateTranslationTextAlignment(TextAlign.left);
      
      Get.snackbar(
        'Reading Mode Updated',
        'Night mode applied - Optimized for night reading',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Color(widget.primaryColor).withOpacity(0.9),
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
        margin: const EdgeInsets.all(8),
        borderRadius: 10,
      );
    }
  }
} 