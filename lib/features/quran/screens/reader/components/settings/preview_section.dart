import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class PreviewSection extends StatelessWidget {
  final int primaryColor;
  final bool isDarkMode;
  final bool showArabicText;
  final bool showTranslation;
  final double fontSize;
  final String arabicFontFamily;
  final bool isRtlTranslation;
  final TextAlign arabicTextAlignment;
  final TextAlign translationTextAlignment;

  const PreviewSection({
    Key? key,
    required this.primaryColor,
    required this.isDarkMode,
    required this.showArabicText,
    required this.showTranslation,
    required this.fontSize,
    required this.arabicFontFamily,
    required this.isRtlTranslation,
    this.arabicTextAlignment = TextAlign.center,
    this.translationTextAlignment = TextAlign.left,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF252736) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Color(primaryColor).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.remove_red_eye_rounded,
                  size: 16,
                  color: Color(primaryColor),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Preview',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(primaryColor),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Arabic text preview
          if (showArabicText)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              child: Directionality(
                textDirection: TextDirection.rtl,
                child: Text(
                  'وَإِذَا سَأَلَكَ عِبَادِي عَنِّي فَإِنِّي قَرِيبٌ',
                  style: TextStyle(
                    fontFamily: arabicFontFamily,
                    fontSize: fontSize,
                    height: 1.5,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                  textAlign: arabicTextAlignment,
                ),
              ),
            ),
            
          // Spacing if both are showing
          if (showArabicText && showTranslation)
            const SizedBox(height: 8),
            
          // Translation preview
          if (showTranslation)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              child: Directionality(
                textDirection: isRtlTranslation ? TextDirection.rtl : TextDirection.ltr,
                child: Text(
                  isRtlTranslation
                      ? 'اور جب میرے بندے میرے بارے میں آپ سے سوال کریں تو میں قریب ہوں'
                      : 'And when My servants ask you concerning Me - indeed I am near.',
                  style: GoogleFonts.poppins(
                    fontSize: fontSize * 0.7,
                    height: 1.5,
                    color: isDarkMode ? Colors.white70 : Colors.grey[800],
                  ),
                  textAlign: translationTextAlignment,
                ),
              ),
            ),
            
          // No content message when both are disabled
          if (!showArabicText && !showTranslation)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isDarkMode 
                    ? Colors.grey[800]!.withOpacity(0.3) 
                    : Colors.grey[200]!,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.visibility_off,
                    size: 32,
                    color: isDarkMode ? Colors.grey[600] : Colors.grey[500],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'No content visible',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  Text(
                    'Enable Arabic text or translation to see content',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: isDarkMode ? Colors.grey[500] : Colors.grey[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
} 