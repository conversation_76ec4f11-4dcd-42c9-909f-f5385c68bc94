import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/quran/controllers/quran_settings_controller.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';
import 'package:new_islamic_app_uk/features/quran/widgets/verse_card.dart';
import 'package:quran/quran.dart' as quran;

class VerseList extends StatelessWidget {
  final ScrollController scrollController;
  final List<VerseInfo> verses;
  final SurahInfo surahInfo;
  final Set<int> bookmarkedVerses;
  final Map<int, bool> expandedVerses;
  final Function(int, bool) onBookmarkChanged;
  final Function(int) onTafsirToggled;
  final Function(int) onPlayAudio;

  VerseList({
    Key? key,
    required this.scrollController,
    required this.verses,
    required this.surahInfo,
    required this.bookmarkedVerses,
    required this.expandedVerses,
    required this.onBookmarkChanged,
    required this.onTafsirToggled,
    required this.onPlayAudio,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get the QuranSettingsController
    final QuranSettingsController settingsController = Get.find<QuranSettingsController>();
    final urls = URLs();
    
    return ListView.builder(
      controller: scrollController,
      padding: const EdgeInsets.only(bottom: 100), // Add space for FAB
      itemCount: verses.length,
      itemBuilder: (context, index) {
        final verse = verses[index];
        final bool isBookmarked = bookmarkedVerses.contains(verse.verseNumber);
        final bool isExpanded = expandedVerses[verse.verseNumber] ?? false;
        final bool isBasmala = verse.verseNumber == 1 && surahInfo.number != 1 && surahInfo.number != 9;
        
        // Use Obx to reactively update the verse card when settings change
        return Obx(() => VerseCard(
          verse: verse,
          isBookmarked: isBookmarked,
          isExpanded: isExpanded,
          isBasmala: isBasmala,
          surahInfo: surahInfo,
          // Get settings from the controller
          fontSize: settingsController.fontSize.value,
          showArabicText: settingsController.showArabicText.value,
          showTranslation: settingsController.showTranslation.value,
          arabicFontFamily: settingsController.arabicFontFamily.value,
          arabicTextAlignment: settingsController.arabicTextAlignment.value,
          translationTextAlignment: settingsController.translationTextAlignment.value,
          onBookmarkChanged: (isBookmarked) => onBookmarkChanged(verse.verseNumber, isBookmarked),
          onTafsirToggled: () => onTafsirToggled(verse.verseNumber),
          onPlayAudio: () => onPlayAudio(verse.verseNumber),
          primaryColor: urls.primaryColor,
          isRtlTranslation: settingsController.isRtlTranslation.value,
        ));
      },
    );
  }
} 