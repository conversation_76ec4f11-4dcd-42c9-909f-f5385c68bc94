import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';

class ReaderAppBar extends StatelessWidget implements PreferredSizeWidget {
  final SurahInfo surahInfo;
  final String? progressIndicator;
  final ReadingProgress? lastSavedProgress;
  final VoidCallback onInfoPressed;
  final VoidCallback onSettingsPressed;
  final Function(int) onLastProgressPressed;
  final int primaryColor;
  final int colorGold;

  const ReaderAppBar({
    Key? key,
    required this.surahInfo,
    this.progressIndicator,
    this.lastSavedProgress,
    required this.onInfoPressed,
    required this.onSettingsPressed,
    required this.onLastProgressPressed,
    required this.primaryColor,
    required this.colorGold,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            surahInfo.nameEnglish,
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          if (progressIndicator != null)
            Text(
              progressIndicator!,
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Colors.grey[400],
              ),
            ),
        ],
      ),
      centerTitle: false,
      elevation: 0,
      scrolledUnderElevation: 0,
      actions: [
        if (lastSavedProgress != null)
          Tooltip(
            message: 'Last saved: Verse ${lastSavedProgress!.lastReadVerse} at ${DateFormat('MMM d, h:mm a').format(lastSavedProgress!.lastReadTime)}',
            child: IconButton(
              icon: Icon(Icons.bookmark, color: Color(colorGold)),
              onPressed: () => onLastProgressPressed(lastSavedProgress!.lastReadVerse),
            ),
          ),
        IconButton(
          onPressed: onSettingsPressed,
          icon: const Icon(Icons.settings_rounded),
          tooltip: 'Reading Settings',
        ),
        IconButton(
          onPressed: onInfoPressed,
          icon: const Icon(Icons.info_outline_rounded),
          tooltip: 'Surah Information',
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
} 