import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/quran/controllers/quran_settings_controller.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';
import 'package:new_islamic_app_uk/features/quran/services/quran_service.dart';
import 'package:quran/quran.dart' as quran;
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'components/reader_app_bar.dart';
import 'components/reader_settings_page.dart';
import 'components/verse_list.dart';
import 'components/audio_controls.dart';
import 'components/progress_save_dialog.dart';

class QuranReaderScreen extends StatefulWidget {
  final SurahInfo surahInfo;
  final int? initialVerseNumber;

  const QuranReaderScreen({
    Key? key,
    required this.surahInfo,
    this.initialVerseNumber,
  }) : super(key: key);

  @override
  State<QuranReaderScreen> createState() => _QuranReaderScreenState();
}

class _QuranReaderScreenState extends State<QuranReaderScreen> {
  final QuranService _quranService = Get.find<QuranService>();
  final URLs _urls = URLs();
  late final ScrollController _scrollController;
  late QuranSettingsController _settingsController;
  late List<VerseInfo> _verses;
  Set<int> _bookmarkedVerses = {};
  Map<int, bool> _expandedVerses = {};
  
  bool _isLoading = true;
  bool _isDarkMode = false;
  bool _showAudioControls = false;
  int? _currentPlayingVerse;
  bool _audioPaused = true;
  ReadingProgress? _lastSavedProgress;
  int? _currentVisibleVerse;
  bool _showJumpToLastReadButton = false;
  
  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    
    // Initialize the settings controller
    _settingsController = Get.find<QuranSettingsController>();
    
    // Listen for changes to translation to reload verses
    ever(_settingsController.selectedTranslation, (_) {
      if (!_isLoading) {
        _reloadVersesWithTranslation(_settingsController.selectedTranslation.value);
      }
    });
    
    _loadData();
    
    // Listen to scroll events to update current visible verse
    _scrollController.addListener(_updateCurrentVisibleVerse);
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _isDarkMode = Theme.of(context).brightness == Brightness.dark;
    _settingsController.updateTheme(context);
  }
  
  @override
  void dispose() {
    _scrollController.removeListener(_updateCurrentVisibleVerse);
    _scrollController.dispose();
    _quranService.stopAudio();
    super.dispose();
  }
  
  // Update the current visible verse based on scroll position
  void _updateCurrentVisibleVerse() {
    if (_scrollController.hasClients && _verses.isNotEmpty) {
      final currentOffset = _scrollController.offset;
      final currentVerseIndex = (currentOffset / 300.0).floor();
      if (currentVerseIndex >= 0 && currentVerseIndex < _verses.length) {
        setState(() {
          _currentVisibleVerse = _verses[currentVerseIndex].verseNumber;
        });
      }
    }
  }
  
  Future<void> _loadData() async {
    // Get selected translation from the controller
    final selectedTranslation = _settingsController.selectedTranslation.value;
    
    // Load verses with the selected translation
    _verses = _quranService.getVersesForSurah(
      widget.surahInfo.number,
      translation: selectedTranslation,
    );
    
    // Load bookmarks
    final bookmarks = await _quranService.getBookmarks();
    _bookmarkedVerses = bookmarks
        .where((b) => b.surahNumber == widget.surahInfo.number)
        .map((b) => b.verseNumber)
        .toSet();
    
    // Check if there's any saved progress for this surah
    final savedProgress = await _quranService.getReadingProgress();
    if (savedProgress != null && savedProgress.surahNumber == widget.surahInfo.number) {
      _lastSavedProgress = savedProgress;
      
      // If initialVerseNumber is not provided, show the jump button
      if (widget.initialVerseNumber == null) {
        setState(() {
          _showJumpToLastReadButton = true;
        });
      }
    }
    
    setState(() {
      _isLoading = false;
    });
    
    // Scroll to initial verse if specified
    if (widget.initialVerseNumber != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToVerse(widget.initialVerseNumber!);
      });
    }
  }
  
  void _scrollToVerse(int verseNumber) {
    if (_scrollController.hasClients && verseNumber > 0 && verseNumber <= _verses.length) {
      final index = verseNumber - 1;
      _scrollController.animateTo(
        index * 300.0, // Approximate height of a verse card
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }
  
  void _toggleBookmark(int verseNumber, bool isBookmarked) async {
    if (isBookmarked) {
      await _quranService.addBookmark(widget.surahInfo.number, verseNumber);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Verse bookmarked'),
          duration: const Duration(seconds: 2),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
      setState(() {
        _bookmarkedVerses.add(verseNumber);
      });
    } else {
      // Find the bookmark ID to remove
      final bookmarks = await _quranService.getBookmarks();
      final bookmarkToRemove = bookmarks.firstWhere(
        (b) => b.surahNumber == widget.surahInfo.number && b.verseNumber == verseNumber,
        orElse: () => QuranBookmark(
          id: -1,
          surahNumber: 0,
          verseNumber: 0,
          createdAt: DateTime.now(),
        ),
      );
      
      if (bookmarkToRemove.id != -1) {
        await _quranService.removeBookmark(bookmarkToRemove.id);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Bookmark removed'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.grey[700],
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
        setState(() {
          _bookmarkedVerses.remove(verseNumber);
        });
      }
    }
  }
  
  void _toggleTafsir(int verseNumber) {
    setState(() {
      _expandedVerses[verseNumber] = !(_expandedVerses[verseNumber] ?? false);
    });
  }
  
  void _playAudio(int verseNumber) async {
    setState(() {
      _currentPlayingVerse = verseNumber;
      _showAudioControls = true;
      _audioPaused = false;
    });
    
    await _quranService.playVerseAudio(widget.surahInfo.number, verseNumber);
    
    // Listen for completion
    _quranService.audioPlayer.onPlayerComplete.listen((event) {
      setState(() {
        _currentPlayingVerse = null;
        _audioPaused = true;
      });
    });
  }
  
  void _pauseAudio() async {
    await _quranService.pauseAudio();
    setState(() {
      _audioPaused = true;
    });
  }
  
  void _resumeAudio() async {
    await _quranService.resumeAudio();
    setState(() {
      _audioPaused = false;
    });
  }
  
  void _stopAudio() async {
    await _quranService.stopAudio();
    setState(() {
      _currentPlayingVerse = null;
      _showAudioControls = false;
      _audioPaused = true;
    });
  }
  
  void _showSettingsPage() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ReaderSettingsPage(
          primaryColor: _urls.primaryColor,
        ),
      ),
    );
  }
  
  // Reload verses when translation changes
  void _reloadVersesWithTranslation(QuranTranslation translation) {
    setState(() {
      _isLoading = true;
    });
    
    // Reload verses with the new translation
    final verses = _quranService.getVersesForSurah(
      widget.surahInfo.number,
      translation: translation,
    );
    
    setState(() {
      _verses = verses;
      _isLoading = false;
    });
    
    // Show notification
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Translation updated to ${_quranService.getTranslationDisplayName(translation)}'),
        duration: const Duration(seconds: 2),
        backgroundColor: Color(_urls.primaryColor),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  // Jump to last saved reading position
  void _jumpToLastReadPosition() {
    if (_lastSavedProgress != null) {
      _scrollToVerse(_lastSavedProgress!.lastReadVerse);
      setState(() {
        _showJumpToLastReadButton = false;
      });
      
      // Show a confirmation snackbar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Jumped to your last reading position'),
          duration: const Duration(seconds: 2),
          backgroundColor: Color(_urls.primaryColor),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }
  
  // Save current reading progress
  Future<void> _saveReadingProgress() async {
    if (_scrollController.hasClients && _verses.isNotEmpty) {
      final currentOffset = _scrollController.offset;
      final currentVerseIndex = (currentOffset / 300.0).floor();
      if (currentVerseIndex >= 0 && currentVerseIndex < _verses.length) {
        final verseNumber = _verses[currentVerseIndex].verseNumber;
        
        // Show confirmation dialog
        final result = await showDialog<Map<String, dynamic>>(
          context: context,
          builder: (context) => ProgressSaveDialog(
            surahInfo: widget.surahInfo,
            verseNumber: verseNumber,
            isDarkMode: _isDarkMode,
            primaryColor: _urls.primaryColor,
          ),
        );
        
        if (result != null && result['save'] == true) {
          // Save progress
          await _quranService.saveReadingProgress(
            widget.surahInfo.number,
            verseNumber,
          );
          
          // Update last saved progress
          setState(() {
            _lastSavedProgress = ReadingProgress(
              surahNumber: widget.surahInfo.number,
              lastReadVerse: verseNumber,
              lastReadTime: DateTime.now(),
            );
          });
          
          // If requested, also save as bookmark
          if (result['saveAsBookmark'] == true) {
            final note = result['note'] as String?;
            
            await _quranService.addBookmark(
              widget.surahInfo.number,
              verseNumber,
              note: note != null && note.isNotEmpty ? note : null,
            );
            
            // Update bookmarks
            final bookmarks = await _quranService.getBookmarks();
            setState(() {
              _bookmarkedVerses = bookmarks
                  .where((b) => b.surahNumber == widget.surahInfo.number)
                  .map((b) => b.verseNumber)
                  .toSet();
            });
          }
          
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['saveAsBookmark']
                  ? 'Reading progress and bookmark saved'
                  : 'Reading progress saved'),
              duration: const Duration(seconds: 2),
              backgroundColor: Color(_urls.primaryColor),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    // Get dark mode from the theme
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    if (_isDarkMode != isDarkMode) {
      setState(() {
        _isDarkMode = isDarkMode;
      });
      _settingsController.updateTheme(context);
    }
    
    // Calculate progress percentage
    String? progressIndicator;
    if (_currentVisibleVerse != null && _verses.isNotEmpty) {
      final percentage = (_currentVisibleVerse! / _verses.length * 100).toInt();
      progressIndicator = '$_currentVisibleVerse/${_verses.length} ($percentage%)';
    }
    
    return Scaffold(
      backgroundColor: _isDarkMode ? const Color(0xFF121212) : Colors.white,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: ReaderAppBar(
          surahInfo: widget.surahInfo,
          progressIndicator: progressIndicator,
          lastSavedProgress: _lastSavedProgress,
          onInfoPressed: _showSurahInfo,
          onSettingsPressed: _showSettingsPage,
          onLastProgressPressed: _scrollToVerse,
          primaryColor: _urls.primaryColor,
          colorGold: _urls.colorGold,
        ),
      ),
      body: Stack(
        children: [
          Column(
            children: [
              // Surah content
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : Stack(
                        children: [
                          VerseList(
                            scrollController: _scrollController,
                            verses: _verses,
                            surahInfo: widget.surahInfo,
                            bookmarkedVerses: _bookmarkedVerses,
                            expandedVerses: _expandedVerses,
                            onBookmarkChanged: _toggleBookmark,
                            onTafsirToggled: _toggleTafsir,
                            onPlayAudio: _playAudio,
                          ),
                          
                          // Jump to last read position button
                          if (_showJumpToLastReadButton && _lastSavedProgress != null)
                            Positioned(
                              top: 16,
                              left: 0,
                              right: 0,
                              child: Center(
                                child: Material(
                                  elevation: 4,
                                  borderRadius: BorderRadius.circular(20),
                                  color: Color(_urls.primaryColor),
                                  child: InkWell(
                                    onTap: _jumpToLastReadPosition,
                                    borderRadius: BorderRadius.circular(20),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(Icons.bookmark, color: Colors.white, size: 18),
                                          SizedBox(width: 8),
                                          Text(
                                            'Continue from verse ${_lastSavedProgress!.lastReadVerse}',
                                            style: GoogleFonts.poppins(
                                              fontSize: 14,
                                              color: Colors.white,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          SizedBox(width: 4),
                                          Icon(Icons.arrow_forward_ios, color: Colors.white, size: 14),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          
                          // Reading progress indicator at the bottom of the screen
                          if (_currentVisibleVerse != null && _verses.isNotEmpty)
                            Positioned(
                              bottom: 16,
                              left: 0,
                              right: 0,
                              child: Center(
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: _isDarkMode 
                                        ? Colors.grey[900]!.withOpacity(0.7)
                                        : Colors.white.withOpacity(0.8),
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                    border: Border.all(
                                      color: Color(_urls.primaryColor).withOpacity(0.2),
                                      width: 1,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.auto_stories_rounded,
                                        size: 14,
                                        color: Color(_urls.primaryColor),
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        'Verse $_currentVisibleVerse of ${_verses.length}',
                                        style: GoogleFonts.poppins(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                          color: _isDarkMode ? Colors.white : Colors.black87,
                                        ),
                                      ),
                                      const SizedBox(width: 6),
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                        decoration: BoxDecoration(
                                          color: Color(_urls.primaryColor).withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(10),
                                        ),
                                        child: Text(
                                          '${(_currentVisibleVerse! / _verses.length * 100).toInt()}%',
                                          style: GoogleFonts.poppins(
                                            fontSize: 10,
                                            fontWeight: FontWeight.w600,
                                            color: Color(_urls.primaryColor),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
              ),
            ],
          ),
          
          // Audio control panel if visible
          if (_showAudioControls && _currentPlayingVerse != null)
            AudioControls(
              currentPlayingVerse: _currentPlayingVerse!,
              audioPaused: _audioPaused,
              onPause: _pauseAudio,
              onResume: _resumeAudio,
              onStop: _stopAudio,
              primaryColor: _urls.primaryColor,
            ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }
  
  Widget _buildFloatingActionButton() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Save bookmark button
        FloatingActionButton(
          onPressed: _saveReadingProgress,
          heroTag: 'saveProgress',
          backgroundColor: Color(_urls.primaryColor),
          elevation: 4,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: 56, // Standard FAB size
                height: 56,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(_urls.primaryColor),
                      Color(_urls.primaryColor).withRed(
                        (Color(_urls.primaryColor).red + 20).clamp(0, 255)
                      ),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Color(_urls.primaryColor).withOpacity(0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.bookmark_add_rounded,
                color: Colors.white,
                size: 28,
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        // Reading progress mini card (only shows when scrolling)
        if (_currentVisibleVerse != null)
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            margin: const EdgeInsets.only(right: 8, bottom: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: _isDarkMode 
                  ? Colors.grey[900]!.withOpacity(0.9)
                  : Colors.white.withOpacity(0.9),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.bookmark_added_rounded,
                  size: 16,
                  color: Color(_urls.primaryColor),
                ),
                const SizedBox(width: 8),
                Text(
                  'Save current position',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: _isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
  
  void _showSurahInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Surah Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                widget.surahInfo.nameArabic,
                style: GoogleFonts.scheherazadeNew(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Surah Number', widget.surahInfo.number.toString()),
            _buildInfoRow('Total Verses', widget.surahInfo.versesCount.toString()),
            _buildInfoRow('Revealed in', widget.surahInfo.placeOfRevelation),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }
  
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
} 