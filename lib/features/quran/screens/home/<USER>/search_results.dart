import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';
import 'package:new_islamic_app_uk/features/quran/screens/reader/reader_screen.dart';
import 'package:new_islamic_app_uk/features/quran/widgets/surah_card.dart';
import 'package:get/get.dart';
import 'search_tips.dart';
import 'surah_info_modal.dart';

class SearchResults extends StatelessWidget {
  final List<SurahInfo> searchResults;
  final String searchQuery;
  final bool isSearching;
  final int primaryColor;

  const SearchResults({
    Key? key,
    required this.searchResults,
    required this.searchQuery,
    required this.isSearching,
    required this.primaryColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Show loading state
    if (isSearching) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(primaryColor)),
            ),
            const SizedBox(height: 16),
            Text(
              'Searching...',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
              ),
            ),
          ],
        ),
      );
    }

    // Show empty state when search is active but no results
    if (searchQuery.isNotEmpty && searchResults.isEmpty) {
      return _buildEmptyState(isDarkMode);
    }

    // Show search tips when no search is active
    if (searchQuery.isEmpty) {
      return SearchTips(primaryColor: primaryColor);
    }

    // Show search results
    return _buildResultsList(context, isDarkMode);
  }

  Widget _buildEmptyState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Color(primaryColor).withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.search_off_rounded,
              size: 40,
              color: Color(primaryColor),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'No surahs found',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.grey[800],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try a different search term',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              Get.back(); // Return to previous state
            },
            icon: const Icon(Icons.refresh),
            label: Text(
              'Clear Search',
              style: GoogleFonts.poppins(),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(primaryColor),
              foregroundColor: Colors.white,
              elevation: 0,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsList(BuildContext context, bool isDarkMode) {
    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? Color(0xFF1E1E2C) : Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
            child: Row(
              children: [
                Text(
                  'Found ${searchResults.length} ${searchResults.length == 1 ? 'surah' : 'surahs'}',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Colors.grey[800],
                  ),
                ),
                const Spacer(),
                if (searchResults.length > 1)
                  TextButton.icon(
                    onPressed: () {
                      // Future feature: Sort by relevance, name, etc.
                    },
                    icon: Icon(
                      Icons.sort,
                      size: 18,
                      color: Color(primaryColor),
                    ),
                    label: Text(
                      'Sort',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Color(primaryColor),
                      ),
                    ),
                    style: TextButton.styleFrom(
                      backgroundColor: Color(primaryColor).withOpacity(0.1),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              itemCount: searchResults.length,
              physics: const BouncingScrollPhysics(),
              itemBuilder: (context, index) {
                final surah = searchResults[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: SurahCard(
                    surah: surah,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => QuranReaderScreen(surahInfo: surah),
                      ),
                    ),
                    onInfoTap: () {
                      // Show surah info modal bottom sheet
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder: (context) => SurahInfoModal(
                          surah: surah,
                          primaryColor: primaryColor,
                        ),
                      );
                    },
                    primaryColor: primaryColor,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
} 