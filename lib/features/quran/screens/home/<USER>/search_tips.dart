import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class SearchTips extends StatelessWidget {
  final int primaryColor;
  
  const SearchTips({
    Key? key,
    required this.primaryColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: Color(primaryColor).withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.search_rounded,
              size: 50,
              color: Color(primaryColor),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Search the Quran',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.grey[800],
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 48),
            child: Text(
              'Find surahs by name, number, or revelation type',
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
            ),
          ),
          const SizedBox(height: 32),
          _buildSearchTips(isDarkMode),
        ],
      ),
    );
  }
  
  Widget _buildSearchTips(bool isDarkMode) {
    final tips = [
      {'icon': Icons.format_list_numbered, 'text': 'Search by surah number (e.g., "1" for Al-Fatiha)'},
      {'icon': Icons.translate, 'text': 'Search by name in English or transliteration'},
      {'icon': Icons.place, 'text': 'Filter by revelation place (Meccan or Medinan)'},
      {'icon': Icons.format_size, 'text': 'Find by verse count or length category'},
    ];
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 32),
      elevation: 0,
      color: isDarkMode ? Color(0xFF252736) : Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isDarkMode ? Colors.grey[800]! : Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  size: 20,
                  color: Color(primaryColor),
                ),
                const SizedBox(width: 8),
                Text(
                  'Search Tips:',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...tips.map((tip) => Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    tip['icon'] as IconData,
                    size: 18,
                    color: Color(primaryColor),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      tip['text'] as String,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }
} 