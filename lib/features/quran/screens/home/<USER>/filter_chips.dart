import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class QuranFilterChips extends StatelessWidget {
  final int primaryColor;
  final Function(String) onChipSelected;
  
  const QuranFilterChips({
    Key? key,
    required this.primaryColor,
    required this.onChipSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      physics: const BouncingScrollPhysics(),
      child: Row(
        children: [
          Text(
            'Quick Filters:',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
          const SizedBox(width: 12),
          Wrap(
            spacing: 10,
            children: [
              _buildFilterChip('Meccan', isDarkMode, context),
              _buildFilterChip('Medinan', isDarkMode, context),
              _buildFilterChip('Juz 1', isDarkMode, context),
              _buildFilterChip('Juz 30', isDarkMode, context),
              _buildFilterChip('Short', isDarkMode, context),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildFilterChip(String label, bool isDarkMode, BuildContext context) {
    return ActionChip(
      backgroundColor: isDarkMode 
          ? Color(0xFF252736)
          : Colors.white,
      side: BorderSide(
        color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
        width: 1,
      ),
      label: Text(
        label,
        style: GoogleFonts.poppins(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
        ),
      ),
      avatar: Icon(
        _getIconForFilter(label),
        size: 14,
        color: Color(primaryColor),
      ),
      elevation: 0,
      pressElevation: 2,
      shadowColor: Color(primaryColor).withOpacity(0.3),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      onPressed: () => onChipSelected(label),
    );
  }
  
  IconData _getIconForFilter(String filter) {
    switch (filter) {
      case 'Meccan':
        return Icons.location_city;
      case 'Medinan':
        return Icons.mosque;
      case 'Juz 1':
      case 'Juz 30':
        return Icons.book;
      case 'Short':
        return Icons.short_text;
      default:
        return Icons.filter_list;
    }
  }
} 