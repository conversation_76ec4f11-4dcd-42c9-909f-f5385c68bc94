import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:get/get.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';
import 'package:new_islamic_app_uk/features/quran/screens/reader/reader_screen.dart';
import 'package:new_islamic_app_uk/features/quran/services/quran_service.dart';
import 'package:share_plus/share_plus.dart';

class DailyVerseTab extends StatefulWidget {
  final int primaryColor;
  
  const DailyVerseTab({
    Key? key,
    required this.primaryColor,
  }) : super(key: key);

  @override
  State<DailyVerseTab> createState() => _DailyVerseTabState();
}

class _DailyVerseTabState extends State<DailyVerseTab> with AutomaticKeepAliveClientMixin {
  final QuranService _quranService = Get.find<QuranService>();
  final RxList<SurahInfo> _surahs = <SurahInfo>[].obs;
  final RxList<VerseInfo> _verses = <VerseInfo>[].obs;
  final RxBool _isLoading = true.obs;
  final URLs _urls = URLs();
  
  final Random _random = Random();
  
  VerseInfo? _dailyVerse;
  VerseInfo? _inspirationalVerse;
  VerseInfo? _randomVerse;
  
  @override
  bool get wantKeepAlive => true;
  
  @override
  void initState() {
    super.initState();
    _loadSurahs();
  }
  
  Future<void> _loadSurahs() async {
    _isLoading.value = true;
    try {
      final surahs = await _quranService.getSurahs();
      _surahs.value = surahs;
      _loadVerses();
    } catch (e) {
      print('Error loading surahs: $e');
      _isLoading.value = false;
    }
  }
  
  Future<void> _loadVerses() async {
    _isLoading.value = true;
    
    try {
      // For daily verse - use predetermined verse (for demo, using Surah Al-Fatiha verse 1)
      final dailySurah = _surahs.value.firstWhere((s) => s.number == 1);
      final dailyVerses = _quranService.getVersesForSurah(dailySurah.number);
      _dailyVerse = dailyVerses.first;
      
      // For inspirational verse - hardcoded inspirational verses from the Quran
      final inspirationalVerses = [
        {'surah': 2, 'verse': 286}, // "Allah does not burden a soul beyond it can bear"
        {'surah': 94, 'verse': 5}, // "For indeed, with hardship will be ease"
        {'surah': 3, 'verse': 139}, // "Do not lose heart nor fall into despair"
        {'surah': 65, 'verse': 3}, // "And whoever relies upon Allah - then He is sufficient for him"
        {'surah': 2, 'verse': 216}, // "But perhaps you hate a thing and it is good for you"
      ];
      
      final randomInspirationIndex = _random.nextInt(inspirationalVerses.length);
      final inspirationSurah = inspirationalVerses[randomInspirationIndex]['surah']!;
      final inspirationVerse = inspirationalVerses[randomInspirationIndex]['verse']!;
      
      final inspirationSurahInfo = _surahs.value.firstWhere((s) => s.number == inspirationSurah);
      final inspirationVerses = _quranService.getVersesForSurah(inspirationSurahInfo.number);
      _inspirationalVerse = inspirationVerses.firstWhere((v) => v.verseNumber == inspirationVerse);
      
      // For random verse - pick a truly random verse
      final randomSurahIndex = _random.nextInt(_surahs.value.length);
      final randomSurah = _surahs.value[randomSurahIndex];
      
      final randomVerses = _quranService.getVersesForSurah(randomSurah.number);
      final randomVerseIndex = _random.nextInt(randomVerses.length);
      _randomVerse = randomVerses[randomVerseIndex];
      
      _isLoading.value = false;
    } catch (e) {
      print('Error loading verses: $e');
      _isLoading.value = false;
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    if (_isLoading.value) {
      return Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(widget.primaryColor)),
        ),
      );
    }
    
    if (_dailyVerse == null || _inspirationalVerse == null || _randomVerse == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline_rounded,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load verses',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: isDarkMode ? Colors.white : Colors.grey[800],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadVerses,
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(widget.primaryColor),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: const Text('Try Again'),
            ),
          ],
        ),
      );
    }
    
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Daily verse section
        _buildSectionTitle('Verse of the Day', Icons.today_rounded, isDarkMode),
        const SizedBox(height: 12),
        _buildVerseCard(
          _dailyVerse!,
          _surahs.value.firstWhere((s) => s.number == _dailyVerse!.surahNumber),
          Color(_urls.primaryGradient[0]),
          Color(_urls.primaryGradient[1]),
          isDarkMode,
        ),
        const SizedBox(height: 24),
        
        // Inspirational verse section
        _buildSectionTitle('Inspirational Verse', Icons.light_mode_rounded, isDarkMode),
        const SizedBox(height: 12),
        _buildVerseCard(
          _inspirationalVerse!,
          _surahs.value.firstWhere((s) => s.number == _inspirationalVerse!.surahNumber),
          Color(_urls.accentGradient[0]),
          Color(_urls.accentGradient[1]),
          isDarkMode,
        ),
        const SizedBox(height: 24),
        
        // Random verse section
        _buildSectionTitle('Random Verse', Icons.shuffle_rounded, isDarkMode),
        const SizedBox(height: 12),
        _buildVerseCard(
          _randomVerse!,
          _surahs.value.firstWhere((s) => s.number == _randomVerse!.surahNumber),
          Color(_urls.warmGradient[0]),
          Color(_urls.warmGradient[1]),
          isDarkMode,
        ),
        
        // More options
        const SizedBox(height: 32),
        Card(
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: isDarkMode ? Colors.grey[800]! : Colors.grey[200]!,
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'More Options',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 16),
                ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Color(widget.primaryColor).withOpacity(0.1),
                    child: Icon(
                      Icons.refresh_rounded,
                      color: Color(widget.primaryColor),
                    ),
                  ),
                  title: Text(
                    'Refresh Verses',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  subtitle: Text(
                    'Get new inspirational and random verses',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                  onTap: _loadVerses,
                ),
                const Divider(),
                ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Colors.amber.withOpacity(0.1),
                    child: const Icon(
                      Icons.notifications_rounded,
                      color: Colors.amber,
                    ),
                  ),
                  title: Text(
                    'Daily Notifications',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  subtitle: Text(
                    'Enable daily verse notifications',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                    ),
                  ),
                  trailing: Switch(
                    value: false, // This would be connected to a real setting
                    activeColor: Color(widget.primaryColor),
                    onChanged: (value) {
                      // This would update a real setting
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Daily notifications coming soon!'),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    },
                  ),
                  onTap: () {
                    // This would toggle the switch
                  },
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
  
  Widget _buildSectionTitle(String title, IconData icon, bool isDarkMode) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: Color(widget.primaryColor),
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: isDarkMode ? Colors.white : Colors.grey[800],
          ),
        ),
      ],
    );
  }
  
  Widget _buildVerseCard(
    VerseInfo verse, 
    SurahInfo surah, 
    Color startColor, 
    Color endColor, 
    bool isDarkMode
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [startColor, endColor],
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with surah info
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      surah.number.toString(),
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          surah.nameEnglish,
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          'Verse ${verse.verseNumber}',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(
                      Icons.share_rounded,
                      color: Colors.white,
                    ),
                    onPressed: () async {
                      final text = '${verse.arabicText}\n\n${verse.translation}\n\n- Surah ${surah.nameEnglish} (${verse.verseNumber})';
                      await Share.share(text);
                    },
                  ),
                ],
              ),
            ),
            
            // Arabic text
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  verse.arabicText,
                  style: GoogleFonts.scheherazadeNew(
                    fontSize: 24,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            
            // Translation
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  verse.translation,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[800],
                  ),
                  textAlign: TextAlign.left,
                ),
              ),
            ),
            
            // Action button
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => QuranReaderScreen(
                        surahInfo: surah,
                        initialVerseNumber: verse.verseNumber,
                      ),
                    ),
                  );
                },
                icon: const Icon(Icons.menu_book_rounded),
                label: const Text('Read Full Surah'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: startColor,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  minimumSize: const Size(double.infinity, 0),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 