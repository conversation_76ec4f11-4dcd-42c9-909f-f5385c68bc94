import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:get/get.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';
import 'package:new_islamic_app_uk/features/quran/screens/reader/reader_screen.dart';
import 'package:new_islamic_app_uk/features/quran/services/quran_service.dart';
import 'package:new_islamic_app_uk/features/quran/widgets/surah_card.dart';

class AllSurahsTab extends StatefulWidget {
  final List<SurahInfo> filteredSurahs;
  final bool isLoading;
  final Function(SurahInfo) showSurahInfo;
  final int primaryColor;
  
  const AllSurahsTab({
    Key? key,
    required this.filteredSurahs,
    required this.isLoading,
    required this.showSurahInfo,
    required this.primaryColor,
  }) : super(key: key);

  @override
  State<AllSurahsTab> createState() => _AllSurahsTabState();
}

class _AllSurahsTabState extends State<AllSurahsTab> with SingleTickerProviderStateMixin {
  // Get instance of QuranService for bookmark data
  final QuranService _quranService = Get.find<QuranService>();
  final URLs _urls = URLs();
  final RxList<ReadingProgress> _recentlyRead = <ReadingProgress>[].obs;
  final RxBool _isLoadingHistory = true.obs;
  
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    _loadRecentlyRead();
    
    // Set up animation for staggered list
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 800),
    );
    
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    _animationController.forward();
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  Future<void> _loadRecentlyRead() async {
    try {
      final history = await _quranService.getReadingHistory();
      _recentlyRead.value = history.take(3).toList(); // Take last 3 read surahs
      _isLoadingHistory.value = false;
    } catch (e) {
      print('Error loading reading history: $e');
      _isLoadingHistory.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    if (widget.isLoading) {
      return Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(widget.primaryColor)),
        ),
      );
    }
    
    if (widget.filteredSurahs.isEmpty) {
      return _buildEmptyState(isDarkMode);
    }
    
    // Featured surahs - commonly read
    final featuredSurahs = widget.filteredSurahs.where((s) => 
      s.number == 1 || s.number == 36 || s.number == 55 || s.number == 67 || s.number == 18
    ).toList();
    
    return ListView(
      physics: const BouncingScrollPhysics(),
      children: [
        // Featured surahs with modern design
        _buildSectionHeader('Featured Surahs', Icons.star_rounded, isDarkMode),
        _buildFeaturedSurahsGrid(featuredSurahs, isDarkMode),
        
        // Recently read section with reactive Obx wrapper
        Obx(() => _isLoadingHistory.value
          ? SizedBox(
              height: 100,
              child: Center(
                child: SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Color(widget.primaryColor)),
                  ),
                ),
              ),
            )
          : _recentlyRead.isNotEmpty
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionHeader('Recently Read', Icons.history_rounded, isDarkMode),
                    _buildRecentlyReadList(isDarkMode),
                  ],
                )
              : const SizedBox.shrink()
        ),
        
        // All Surahs with modern card design
        _buildSectionHeader('All Surahs', Icons.menu_book_rounded, isDarkMode),
        _buildAllSurahsList(widget.filteredSurahs, isDarkMode),
        
        // Bottom padding
        const SizedBox(height: 80),
      ],
    );
  }
  
  Widget _buildEmptyState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off_rounded,
            size: 72,
            color: isDarkMode ? Colors.grey[700] : Colors.grey[300],
          ),
          const SizedBox(height: 24),
          Text(
            'No surahs found',
            style: GoogleFonts.poppins(
              fontSize: 22,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.grey[800],
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Try a different search term',
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () {
              // Get back to full list (could be handled by parent)
            },
            icon: const Icon(Icons.refresh_rounded),
            label: const Text('Reset Search'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(widget.primaryColor),
              foregroundColor: Colors.white,
              elevation: 0,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSectionHeader(String title, IconData icon, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 24, 20, 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Color(widget.primaryColor).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              size: 20,
              color: Color(widget.primaryColor),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFeaturedSurahsGrid(List<SurahInfo> surahs, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.4,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: surahs.length,
        itemBuilder: (context, index) {
          final surah = surahs[index];
          // Animate each card with a staggered effect
          return AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              final delay = index * 0.2;
              final value = _animation.value > delay
                  ? (_animation.value - delay) / (1 - delay)
                  : 0.0;
              return Transform.scale(
                scale: 0.8 + (0.2 * value),
                child: Opacity(
                  opacity: value,
                  child: child,
                ),
              );
            },
            child: _buildFeaturedSurahCard(surah, isDarkMode),
          );
        },
      ),
    );
  }
  
  Widget _buildFeaturedSurahCard(SurahInfo surah, bool isDarkMode) {
    // Get gradient colors based on surah number
    List<Color> gradientColors;
    
    switch (surah.number) {
      case 1:
        gradientColors = [
          Color(_urls.primaryGradient[0]),
          Color(_urls.primaryGradient[1]),
        ];
        break;
      case 36:
        gradientColors = [
          Color(_urls.secondaryGradient[0]),
          Color(_urls.secondaryGradient[1]),
        ];
        break;
      case 55:
        gradientColors = [
          Color(_urls.accentGradient[0]),
          Color(_urls.accentGradient[1]),
        ];
        break;
      case 67:
        gradientColors = [
          Color(_urls.warmGradient[0]),
          Color(_urls.warmGradient[1]),
        ];
        break;
      default:
        gradientColors = [
          Color(_urls.coolGradient[0]),
          Color(_urls.coolGradient[1]),
        ];
    }
    
    return GestureDetector(
      onTap: () => Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => QuranReaderScreen(surahInfo: surah),
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: gradientColors,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: gradientColors[0].withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Decorative elements for depth
            Positioned(
              right: -30,
              top: -30,
              child: CircleAvatar(
                radius: 50,
                backgroundColor: Colors.white.withOpacity(0.1),
              ),
            ),
            Positioned(
              left: -20,
              bottom: -20,
              child: CircleAvatar(
                radius: 40,
                backgroundColor: Colors.white.withOpacity(0.1),
              ),
            ),
            
            // Content with better spacing
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${surah.number}',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.format_list_numbered,
                              size: 10,
                              color: Colors.white,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${surah.versesCount}',
                              style: GoogleFonts.poppins(
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        surah.nameArabic,
                        style: GoogleFonts.scheherazadeNew(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        surah.nameEnglish,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Play indicator
            Positioned(
              right: 12,
              bottom: 12,
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.play_arrow_rounded,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildRecentlyReadList(bool isDarkMode) {
    return Obx(() => Container(
      margin: const EdgeInsets.only(bottom: 16),
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: 20),
        itemCount: _recentlyRead.length,
        itemBuilder: (context, index) {
          final readProgress = _recentlyRead[index];
          final surah = widget.filteredSurahs.firstWhere(
            (s) => s.number == readProgress.surahNumber,
            orElse: () => widget.filteredSurahs.first,
          );
          
          // Animate each card with a staggered effect
          return AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              final delay = 0.3 + (index * 0.1);
              final value = _animation.value > delay
                  ? (_animation.value - delay) / (1 - delay)
                  : 0.0;
              return Transform.translate(
                offset: Offset(20 * (1 - value), 0),
                child: Opacity(
                  opacity: value,
                  child: child,
                ),
              );
            },
            child: _buildRecentReadCard(surah, readProgress, isDarkMode),
          );
        },
      ),
    ));
  }
  
  Widget _buildRecentReadCard(SurahInfo surah, ReadingProgress progress, bool isDarkMode) {
    return GestureDetector(
      onTap: () => Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => QuranReaderScreen(
            surahInfo: surah,
            initialVerseNumber: progress.lastReadVerse,
          ),
        ),
      ),
      child: Container(
        width: 260,
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.grey.shade800 : Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(
            color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200,
            width: 1,
          ),
        ),
        child: Stack(
          children: [
            // Circle decoration
            Positioned(
              right: -20,
              bottom: -20,
              child: CircleAvatar(
                radius: 40,
                backgroundColor: Color(widget.primaryColor).withOpacity(0.05),
              ),
            ),
            
            // Main content
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  // Surah number with gradient background
                  Container(
                    width: 54,
                    height: 54,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Color(widget.primaryColor).withOpacity(0.8),
                          Color(widget.primaryColor),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Color(widget.primaryColor).withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Center(
                      child: Text(
                        '${surah.number}',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  
                  // Surah details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          surah.nameEnglish,
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.bookmark_rounded,
                              size: 14,
                              color: Color(widget.primaryColor),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Verse ${progress.lastReadVerse}',
                              style: GoogleFonts.poppins(
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: Color(widget.primaryColor),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.access_time_rounded,
                              size: 14,
                              color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              progress.lastReadTime.toString().substring(0, 10),
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  // Play button
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: Color(widget.primaryColor).withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.play_arrow_rounded,
                      color: Color(widget.primaryColor),
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAllSurahsList(List<SurahInfo> surahs, bool isDarkMode) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: surahs.length,
      itemBuilder: (context, index) {
        final surah = surahs[index];
        
        // Animate each item with a staggered effect
        return AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            final delay = 0.5 + (index * 0.02);
            final value = _animation.value > delay
                ? (_animation.value - delay) / (1 - delay)
                : 0.0;
            return Transform.translate(
              offset: Offset(0, 20 * (1 - value)),
              child: Opacity(
                opacity: value,
                child: child,
              ),
            );
          },
          child: _buildSurahListItem(surah, index, isDarkMode),
        );
      },
    );
  }
  
  Widget _buildSurahListItem(SurahInfo surah, int index, bool isDarkMode) {
    final bool isEven = index % 2 == 0;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isDarkMode 
            ? (isEven ? Colors.grey.shade800 : Colors.grey.shade800.withOpacity(0.7))
            : (isEven ? Colors.white : Colors.grey.shade50),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => QuranReaderScreen(surahInfo: surah),
          ),
        ),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              // Number badge with custom style based on index
              Container(
                width: 42,
                height: 42,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      _getGradientColor(index, 0),
                      _getGradientColor(index, 1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: _getGradientColor(index, 0).withOpacity(0.2),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    '${surah.number}',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              
              // Surah details with modern typography
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            surah.nameEnglish,
                            style: GoogleFonts.poppins(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: isDarkMode ? Colors.white : Colors.black87,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                          decoration: BoxDecoration(
                            color: _getGradientColor(index, 0).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${surah.versesCount} verses',
                            style: GoogleFonts.poppins(
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                              color: _getGradientColor(index, 0),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          surah.nameArabic,
                          style: GoogleFonts.scheherazadeNew(
                            fontSize: 18,
                            color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(width: 8),
                        Container(
                          width: 4,
                          height: 4,
                          decoration: BoxDecoration(
                            color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade400,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          surah.placeOfRevelation,
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Action buttons with visual hierarchy
              IconButton(
                icon: Icon(
                  Icons.info_outline_rounded,
                  color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade700,
                  size: 20,
                ),
                onPressed: () => widget.showSurahInfo(surah),
                tooltip: 'Surah Information',
                splashRadius: 24,
              ),
              IconButton(
                icon: Icon(
                  Icons.play_circle_outline_rounded,
                  color: Color(widget.primaryColor),
                  size: 24,
                ),
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => QuranReaderScreen(surahInfo: surah),
                  ),
                ),
                tooltip: 'Read Surah',
                splashRadius: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  // Helper to get gradient colors based on index
  Color _getGradientColor(int index, int position) {
    final gradients = [
      [Color(_urls.primaryGradient[0]), Color(_urls.primaryGradient[1])],
      [Color(_urls.secondaryGradient[0]), Color(_urls.secondaryGradient[1])],
      [Color(_urls.accentGradient[0]), Color(_urls.accentGradient[1])],
      [Color(_urls.warmGradient[0]), Color(_urls.warmGradient[1])],
      [Color(_urls.coolGradient[0]), Color(_urls.coolGradient[1])],
    ];
    
    final gradientIndex = index % gradients.length;
    return gradients[gradientIndex][position];
  }
} 