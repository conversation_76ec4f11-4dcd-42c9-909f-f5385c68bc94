import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:get/get.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';
import 'package:new_islamic_app_uk/features/quran/screens/reader/reader_screen.dart';
import 'package:new_islamic_app_uk/features/quran/services/quran_service.dart';
import 'search/surah_info_modal.dart';

class BookmarksTab extends StatefulWidget {
  final int primaryColor;
  
  const BookmarksTab({
    Key? key,
    required this.primaryColor,
  }) : super(key: key);

  @override
  State<BookmarksTab> createState() => _BookmarksTabState();
}

class _BookmarksTabState extends State<BookmarksTab> with AutomaticKeepAliveClientMixin {
  final QuranService _quranService = Get.find<QuranService>();
  final RxList<QuranBookmark> _bookmarks = <QuranBookmark>[].obs;
  final RxBool _isLoading = true.obs;
  final RxList<SurahInfo> _surahs = <SurahInfo>[].obs;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadSurahs();
    _loadBookmarks();
  }

  Future<void> _loadSurahs() async {
    try {
      final surahs = await _quranService.getSurahs();
      _surahs.value = surahs;
    } catch (e) {
      print('Error loading surahs: $e');
    }
  }

  Future<void> _loadBookmarks() async {
    _isLoading.value = true;
    
    try {
      final bookmarks = await _quranService.getBookmarks();
      _bookmarks.value = bookmarks;
      _isLoading.value = false;
    } catch (e) {
      print('Error loading bookmarks: $e');
      _isLoading.value = false;
    }
  }

  void _showSurahInfo(SurahInfo surah) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SurahInfoModal(
        surah: surah,
        primaryColor: widget.primaryColor,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      color: isDarkMode ? const Color(0xFF121212) : Colors.grey[50],
      child: Obx(() {
        if (_isLoading.value) {
          return Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(widget.primaryColor)),
            ),
          );
        }
        
        if (_bookmarks.isEmpty) {
          return _buildEmptyState(isDarkMode);
        }
        
        return RefreshIndicator(
          onRefresh: _loadBookmarks,
          color: Color(widget.primaryColor),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _bookmarks.length,
            itemBuilder: (context, index) {
              final bookmark = _bookmarks[index];
              final surah = _surahs.firstWhere(
                (s) => s.number == bookmark.surahNumber,
                orElse: () => _surahs.isNotEmpty ? _surahs.first : SurahInfo(
                  number: 1,
                  nameArabic: "الفاتحة",
                  nameEnglish: "Al-Fatiha",
                  nameTransliteration: "Al-Fatihah",
                  versesCount: 7,
                  placeOfRevelation: "Meccan",
                ),
              );
              
              return Dismissible(
                key: Key('bookmark-${bookmark.id}'),
                direction: DismissDirection.endToStart,
                background: Container(
                  alignment: Alignment.centerRight,
                  padding: const EdgeInsets.only(right: 20),
                  decoration: BoxDecoration(
                    color: Colors.red.shade700,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Icon(
                    Icons.delete_outline_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                onDismissed: (_) async {
                  await _quranService.removeBookmark(bookmark.id);
                  _bookmarks.removeWhere((b) => b.id == bookmark.id);
                  
                  Get.snackbar(
                    'Bookmark Removed',
                    'Verse from Surah ${surah.nameEnglish} removed from bookmarks',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Color(widget.primaryColor).withOpacity(0.9),
                    colorText: Colors.white,
                    borderRadius: 10,
                    margin: const EdgeInsets.all(8),
                    duration: const Duration(seconds: 3),
                    mainButton: TextButton(
                      child: const Text(
                        'UNDO',
                        style: TextStyle(color: Colors.white),
                      ),
                      onPressed: () async {
                        await _quranService.addBookmark(
                          bookmark.surahNumber,
                          bookmark.verseNumber,
                          note: bookmark.note,
                        );
                        await _loadBookmarks();
                        Get.closeCurrentSnackbar();
                      },
                    ),
                  );
                },
                child: Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: isDarkMode ? const Color(0xFF252736) : Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header section with surah info
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Color(widget.primaryColor).withOpacity(0.1),
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(16),
                            topRight: Radius.circular(16),
                          ),
                        ),
                        child: Row(
                          children: [
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: Color(widget.primaryColor).withOpacity(0.2),
                                shape: BoxShape.circle,
                              ),
                              child: Center(
                                child: Text(
                                  surah.number.toString(),
                                  style: GoogleFonts.poppins(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Color(widget.primaryColor),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  surah.nameEnglish,
                                  style: GoogleFonts.poppins(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: isDarkMode ? Colors.white : Colors.grey.shade800,
                                  ),
                                ),
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        'Verse ${bookmark.verseNumber}',
                                        style: GoogleFonts.poppins(
                                          fontSize: 12,
                                          color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      bookmark.createdAt.toString().substring(0, 10),
                                      style: GoogleFonts.poppins(
                                        fontSize: 12,
                                        color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade500,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            const Spacer(),
                            Icon(
                              Icons.bookmark,
                              color: Color(widget.primaryColor),
                              size: 20,
                            ),
                          ],
                        ),
                      ),
                      
                      // Note if available
                      if (bookmark.note != null && bookmark.note!.isNotEmpty)
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: isDarkMode ? Colors.grey.shade800.withOpacity(0.5) : Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200,
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(
                                      Icons.note_alt_outlined,
                                      size: 16,
                                      color: Color(widget.primaryColor),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Note',
                                      style: GoogleFonts.poppins(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                        color: Color(widget.primaryColor),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  bookmark.note!,
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      
                      // Action buttons
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Expanded(
                              child: OutlinedButton.icon(
                                icon: const Icon(Icons.info_outline, size: 16),
                                label: const Text('Surah Info'),
                                onPressed: () => _showSurahInfo(surah),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Color(widget.primaryColor),
                                  side: BorderSide(color: Color(widget.primaryColor).withOpacity(0.5)),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: ElevatedButton.icon(
                                icon: const Icon(Icons.book_rounded, size: 16),
                                label: const Text('Continue Reading'),
                                onPressed: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => QuranReaderScreen(
                                        surahInfo: surah,
                                        initialVerseNumber: bookmark.verseNumber,
                                      ),
                                    ),
                                  );
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Color(widget.primaryColor),
                                  foregroundColor: Colors.white,
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      }),
    );
  }
  
  Widget _buildEmptyState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Color(widget.primaryColor).withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.bookmark_border_rounded,
              size: 60,
              color: Color(widget.primaryColor),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No Bookmarks Yet',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 48),
            child: Text(
              'Add bookmarks while reading Quran to save verses for later reference',
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
              ),
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () {
              if (_surahs.isNotEmpty) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => QuranReaderScreen(
                      surahInfo: _surahs.first,
                    ),
                  ),
                );
              }
            },
            icon: const Icon(Icons.menu_book_rounded),
            label: const Text('Start Reading'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(widget.primaryColor),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
          ),
        ],
      ),
    );
  }
} 