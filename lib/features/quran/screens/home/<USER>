import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';
import 'package:new_islamic_app_uk/features/quran/screens/reader/reader_screen.dart';
import 'package:new_islamic_app_uk/features/quran/services/quran_service.dart';

import 'search/search_bar.dart';
import 'search/filter_chips.dart';
import 'search/search_results.dart';

class SearchTab extends StatefulWidget {
  final int primaryColor;
  
  const SearchTab({
    Key? key,
    required this.primaryColor,
  }) : super(key: key);

  @override
  State<SearchTab> createState() => _SearchTabState();
}

class _SearchTabState extends State<SearchTab> with AutomaticKeepAliveClientMixin {
  final TextEditingController _searchController = TextEditingController();
  final QuranService _quranService = Get.find<QuranService>();
  
  // Reactive variables
  final RxList<SurahInfo> _searchResults = <SurahInfo>[].obs;
  final RxBool _isSearching = false.obs;
  final RxString _searchQuery = ''.obs;
  
  @override
  bool get wantKeepAlive => true;
  
  @override
  void initState() {
    super.initState();
    // Load all surahs initially
    _loadAllSurahs();
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
  
  void _loadAllSurahs() async {
    _isSearching.value = true;
    try {
      final surahs = await _quranService.getAllSurahs();
      _searchResults.value = [];
      _isSearching.value = false;
    } catch (e) {
      _isSearching.value = false;
      Get.snackbar(
        'Error',
        'Failed to load surahs: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  void _performSearch(String query) async {
    _searchQuery.value = query;
    
    if (query.isEmpty) {
      _searchResults.value = [];
      return;
    }
    
    _isSearching.value = true;
    try {
      final results = await _quranService.searchSurahs(query);
      _searchResults.value = results;
      _isSearching.value = false;
    } catch (e) {
      _isSearching.value = false;
      Get.snackbar(
        'Error',
        'Search failed: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  void _clearSearch() {
    _searchController.clear();
    _searchQuery.value = '';
    _searchResults.value = [];
  }
  
  @override
  Widget build(BuildContext context) {
    super.build(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      color: isDarkMode ? const Color(0xFF121212) : Colors.grey[50],
      child: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: QuranSearchBar(
              controller: _searchController,
              onChanged: _performSearch,
              onClear: _clearSearch,
              primaryColor: widget.primaryColor,
            ),
          ),
          
          // Filter chips
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: QuranFilterChips(
              primaryColor: widget.primaryColor,
              onChipSelected: (filter) {
                _searchController.text = filter;
                _performSearch(filter);
              },
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Search results
          Expanded(
            child: Obx(() => SearchResults(
              searchResults: _searchResults.value,
              searchQuery: _searchQuery.value,
              isSearching: _isSearching.value,
              primaryColor: widget.primaryColor,
            )),
          ),
        ],
      ),
    );
  }
} 