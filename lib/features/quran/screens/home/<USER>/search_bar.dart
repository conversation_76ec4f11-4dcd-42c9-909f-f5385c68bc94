import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class QuranSearchBar extends StatefulWidget {
  final TextEditingController controller;
  final Function(String) onChanged;
  final Function() onClear;
  final int primaryColor;
  
  const QuranSearchBar({
    Key? key,
    required this.controller,
    required this.onChanged,
    required this.onClear,
    required this.primaryColor,
  }) : super(key: key);

  @override
  State<QuranSearchBar> createState() => _QuranSearchBarState();
}

class _QuranSearchBarState extends State<QuranSearchBar> {
  final FocusNode _searchFocus = FocusNode();
  bool _hasFocus = false;
  
  @override
  void initState() {
    super.initState();
    _searchFocus.addListener(() {
      setState(() {
        _hasFocus = _searchFocus.hasFocus;
      });
    });
  }
  
  @override
  void dispose() {
    _searchFocus.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[800] : Colors.grey[100],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _hasFocus 
              ? Color(widget.primaryColor) 
              : isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
          width: 1.5,
        ),
        boxShadow: [
          if (_hasFocus)
            BoxShadow(
              color: Color(widget.primaryColor).withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
        ],
      ),
      child: TextField(
        controller: widget.controller,
        focusNode: _searchFocus,
        onChanged: widget.onChanged,
        style: GoogleFonts.poppins(
          fontSize: 16,
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
        decoration: InputDecoration(
          hintText: 'Search surah name or number...',
          hintStyle: GoogleFonts.poppins(
            fontSize: 16,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
          prefixIcon: Icon(
            Icons.search_rounded,
            color: _hasFocus 
                ? Color(widget.primaryColor) 
                : isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
          suffixIcon: widget.controller.text.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    Icons.clear_rounded,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  ),
                  onPressed: widget.onClear,
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }
} 