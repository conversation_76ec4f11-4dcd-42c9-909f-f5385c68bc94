import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/quran/controllers/quran_settings_controller.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';
import 'package:new_islamic_app_uk/features/quran/screens/home/<USER>';
import 'package:new_islamic_app_uk/features/quran/screens/home/<USER>';
import 'package:new_islamic_app_uk/features/quran/screens/home/<USER>';
import 'package:new_islamic_app_uk/features/quran/screens/home/<USER>';
import 'package:new_islamic_app_uk/features/quran/screens/reader/reader_screen.dart';
import 'package:new_islamic_app_uk/features/quran/services/quran_service.dart';
import 'package:new_islamic_app_uk/features/quran/screens/home/<USER>/surah_info_modal.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:new_islamic_app_uk/theme/app_theme.dart';
import 'package:new_islamic_app_uk/theme/app_colors.dart';
import 'package:new_islamic_app_uk/theme/app_text_styles.dart';
import 'package:new_islamic_app_uk/widgets/common/modern_app_bar.dart';

class QuranHomeScreen extends StatefulWidget {
  final int? primaryColor;

  const QuranHomeScreen({Key? key, this.primaryColor}) : super(key: key);

  @override
  State<QuranHomeScreen> createState() => _QuranHomeScreenState();
}

class _QuranHomeScreenState extends State<QuranHomeScreen> with SingleTickerProviderStateMixin {
  final QuranService _quranService = Get.find<QuranService>();
  final QuranSettingsController _settingsController = Get.find<QuranSettingsController>();

  late TabController _tabController;
  final URLs _urls = URLs();

  // Use RxList for reactive updates with Obx
  final RxList<SurahInfo> _surahs = <SurahInfo>[].obs;
  final RxList<SurahInfo> _filteredSurahs = <SurahInfo>[].obs;
  final Rx<ReadingProgress?> _lastRead = Rx<ReadingProgress?>(null);

  // Use RxBool for reactive updates with Obx
  final RxBool _isLoading = true.obs;

  int get primaryColor => widget.primaryColor ?? _urls.primaryColor;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadSurahs();

    // Load last read position
    _loadLastReadSurah();

    // Add listener for tab changes
    _tabController.addListener(_handleTabChanged);
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSurahs() async {
    _isLoading.value = true;

    try {
      final surahs = await _quranService.getSurahs();

      _surahs.value = surahs;
      _filteredSurahs.value = surahs;
      _isLoading.value = false;
    } catch (e) {
      print('Error loading surahs: $e');
      _isLoading.value = false;

      // Show error snackbar
      Get.snackbar(
        'Error',
        'Failed to load Quran data: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _loadLastReadSurah() async {
    try {
      final progress = await _quranService.getLastReadingProgress();
      _lastRead.value = progress;
    } catch (e) {
      print('Error loading last read position: $e');
    }
  }

  void _showSurahInfo(SurahInfo surah) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SurahInfoModal(
        surah: surah,
        primaryColor: primaryColor,
      ),
    );
  }

  // Handle tab changes to update UI
  void _handleTabChanged() {
    if (!mounted) return;
    // Just trigger a rebuild when tab changes
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: ModernAppBar(
        title: 'Quran',
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Modern tab bar
          Container(
            margin: const EdgeInsets.all(AppTheme.spacing16),
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant,
              borderRadius: BorderRadius.circular(AppTheme.radiusCircular),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(AppTheme.radiusCircular),
                color: AppColors.primary,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.3),
                    blurRadius: AppTheme.elevationMedium * 2,
                    offset: const Offset(0, AppTheme.elevationMedium),
                  ),
                ],
              ),
              labelColor: Colors.white,
              unselectedLabelColor: AppColors.textSecondary,
              labelStyle: AppTextStyles.labelMedium,
              unselectedLabelStyle: AppTextStyles.labelMedium,
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              tabs: const [
                Tab(
                  icon: Icon(Icons.menu_book_rounded, size: AppTheme.iconSmall),
                  text: 'Surahs',
                ),
                Tab(
                  icon: Icon(Icons.search_rounded, size: AppTheme.iconSmall),
                  text: 'Search',
                ),
                Tab(
                  icon: Icon(Icons.bookmark_rounded, size: AppTheme.iconSmall),
                  text: 'Bookmarks',
                ),
                Tab(
                  icon: Icon(Icons.auto_stories_rounded, size: AppTheme.iconSmall),
                  text: 'Daily',
                ),
              ],
            ),
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // All Surahs Tab
                Obx(() => AllSurahsTab(
                  filteredSurahs: _filteredSurahs,
                  isLoading: _isLoading.value,
                  showSurahInfo: _showSurahInfo,
                  primaryColor: primaryColor,
                )),

                // Search Tab
                SearchTab(
                  primaryColor: primaryColor,
                ),

                // Bookmarks Tab
                BookmarksTab(
                  primaryColor: primaryColor,
                ),

                // Daily Verse Tab
                DailyVerseTab(
                  primaryColor: primaryColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionSheet() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Obx(() {
      final actions = [
        {
          'icon': Icons.auto_stories_rounded,
          'title': 'Continue Reading',
          'subtitle': _lastRead.value != null
              ? 'Surah ${_surahs.firstWhere((s) => s.number == _lastRead.value!.surahNumber, orElse: () => _surahs[0]).nameEnglish}, Verse ${_lastRead.value!.lastReadVerse}'
              : 'Start reading Quran',
          'onTap': () {
            Navigator.pop(context);
            if (_lastRead.value != null) {
              final selectedSurah = _surahs.firstWhere(
                (s) => s.number == _lastRead.value!.surahNumber,
                orElse: () => _surahs[0],
              );
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => QuranReaderScreen(
                    surahInfo: selectedSurah,
                    initialVerseNumber: _lastRead.value!.lastReadVerse,
                  ),
                ),
              );
            } else if (_surahs.isNotEmpty) {
              // If no last read, start with Al-Fatiha
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => QuranReaderScreen(
                    surahInfo: _surahs[0],
                  ),
                ),
              );
            }
          },
        },
        {
          'icon': Icons.shuffle_rounded,
          'title': 'Random Surah',
          'subtitle': 'Open a random surah',
          'onTap': () {
            Navigator.pop(context);
            if (_surahs.isNotEmpty) {
              final randomSurah = _surahs[DateTime.now().millisecond % _surahs.length];
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => QuranReaderScreen(
                    surahInfo: randomSurah,
                  ),
                ),
              );
            }
          },
        },
        {
          'icon': Icons.settings_rounded,
          'title': 'Reader Settings',
          'subtitle': 'Configure display options',
          'onTap': () {
            Navigator.pop(context);
            // Navigate to settings page
          },
        },
      ];

      return Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.grey[900] : Colors.white,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 20, right: 20, bottom: 16),
              child: Row(
                children: [
                  Text(
                    'Quick Actions',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: isDarkMode ? Colors.white : Colors.grey[800],
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                    color: isDarkMode ? Colors.white70 : Colors.grey[700],
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
            Flexible(
              child: ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: actions.length,
                itemBuilder: (context, index) {
                  final action = actions[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Color(primaryColor).withOpacity(0.1),
                      foregroundColor: Color(primaryColor),
                      child: Icon(action['icon'] as IconData),
                    ),
                    title: Text(
                      action['title'] as String,
                      style: GoogleFonts.poppins(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    subtitle: Text(
                      action['subtitle'] as String,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                    onTap: action['onTap'] as Function(),
                  );
                },
              ),
            ),
          ],
        ),
      );
    });
  }
}