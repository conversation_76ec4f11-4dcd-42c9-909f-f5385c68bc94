import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../theme/app_theme.dart';
import '../../../widgets/common/modern_card.dart';
import '../../../widgets/common/modern_search_bar.dart';

class ModernQuranHomeScreen extends StatefulWidget {
  const ModernQuranHomeScreen({Key? key}) : super(key: key);

  @override
  State<ModernQuranHomeScreen> createState() => _ModernQuranHomeScreenState();
}

class _ModernQuranHomeScreenState extends State<ModernQuranHomeScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _selectedTabIndex = 0;
  
  final List<Map<String, dynamic>> _surahs = [
    {'number': 1, 'nameArabic': 'الفاتحة', 'nameEnglish': 'Al-<PERSON><PERSON><PERSON>', 'verses': 7, 'type': 'Meccan'},
    {'number': 2, 'nameArabic': 'البقرة', 'nameEnglish': 'Al-Baqarah', 'verses': 286, 'type': 'Medinan'},
    {'number': 3, 'nameArabic': 'آل عمران', 'nameEnglish': 'Ali \'Imran', 'verses': 200, 'type': 'Medinan'},
    {'number': 4, 'nameArabic': 'النساء', 'nameEnglish': 'An-Nisa', 'verses': 176, 'type': 'Medinan'},
    {'number': 5, 'nameArabic': 'المائدة', 'nameEnglish': 'Al-Ma\'idah', 'verses': 120, 'type': 'Medinan'},
    {'number': 6, 'nameArabic': 'الأنعام', 'nameEnglish': 'Al-An\'am', 'verses': 165, 'type': 'Meccan'},
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _selectedTabIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildSurahsTab(),
                _buildSearchTab(),
                _buildBookmarksTab(),
                _buildDailyVerseTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(
          Icons.arrow_back_ios,
          color: AppTheme.textPrimary,
        ),
      ),
      title: Text(
        'Quran',
        style: AppTheme.textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        IconButton(
          onPressed: () {
            // Settings
          },
          icon: const Icon(
            Icons.settings_outlined,
            color: AppTheme.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppTheme.primaryGreen,
          borderRadius: BorderRadius.circular(12),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondary,
        labelStyle: GoogleFonts.poppins(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: GoogleFonts.poppins(
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
        tabs: const [
          Tab(
            icon: Icon(FontAwesomeIcons.bookQuran, size: 16),
            text: 'Surahs',
          ),
          Tab(
            icon: Icon(Icons.search, size: 16),
            text: 'Search',
          ),
          Tab(
            icon: Icon(Icons.bookmark_outline, size: 16),
            text: 'Bookmarks',
          ),
          Tab(
            icon: Icon(Icons.auto_stories, size: 16),
            text: 'Daily Verse',
          ),
        ],
      ),
    );
  }

  Widget _buildSurahsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Continue Reading Card
          _buildContinueReadingCard(),
          
          const SizedBox(height: 24),
          
          // Search bar
          ModernSearchBar(
            hintText: 'Search surahs...',
            margin: const EdgeInsets.only(bottom: 20),
          ),
          
          // Surahs List
          Text(
            'All Surahs',
            style: AppTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 16),
          
          ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: _surahs.length,
            itemBuilder: (context, index) {
              final surah = _surahs[index];
              return _buildSurahCard(surah);
            },
          ),
          
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  Widget _buildContinueReadingCard() {
    return GradientCard(
      gradient: AppTheme.primaryGradient,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.auto_stories_rounded,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Continue Reading',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Surah Al-Baqarah, Verse 255',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Last read 2 hours ago',
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: Colors.white.withOpacity(0.9),
            ),
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Continue',
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSurahCard(Map<String, dynamic> surah) {
    return ModernCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          // Surah number
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.primaryGreen.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                '${surah['number']}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryGreen,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Surah info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  surah['nameEnglish'],
                  style: AppTheme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${surah['type']} • ${surah['verses']} verses',
                  style: AppTheme.textTheme.bodySmall,
                ),
              ],
            ),
          ),
          
          // Arabic name
          Text(
            surah['nameArabic'],
            style: GoogleFonts.amiri(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryGreen,
            ),
          ),
        ],
      ),
      onTap: () => _navigateToSurah(surah),
    );
  }

  Widget _buildSearchTab() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          ModernSearchBar(
            hintText: 'Search verses, surahs...',
            margin: EdgeInsets.zero,
          ),
          const SizedBox(height: 20),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.search,
                    size: 64,
                    color: AppTheme.textLight,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Search the Quran',
                    style: AppTheme.textTheme.titleLarge?.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Find verses, surahs, and topics',
                    style: AppTheme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textLight,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookmarksTab() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bookmark_outline,
              size: 64,
              color: AppTheme.textLight,
            ),
            const SizedBox(height: 16),
            Text(
              'No bookmarks yet',
              style: AppTheme.textTheme.titleLarge?.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Bookmark your favorite verses',
              style: AppTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.textLight,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyVerseTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Daily Verse',
            style: AppTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          GradientCard(
            gradient: const LinearGradient(
              colors: [Color(0xFF4CAF50), Color(0xFF8BC34A)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
                  style: GoogleFonts.amiri(
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                    height: 1.8,
                  ),
                  textAlign: TextAlign.right,
                ),
                const SizedBox(height: 16),
                Text(
                  'And whoever fears Allah - He will make for him a way out.',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    color: Colors.white,
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Quran 65:2',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToSurah(Map<String, dynamic> surah) {
    // Navigate to surah reading screen
    print('Navigate to surah: ${surah['nameEnglish']}');
  }
}
