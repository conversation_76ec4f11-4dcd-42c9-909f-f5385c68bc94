import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';
import 'package:new_islamic_app_uk/features/quran/screens/quran_home_screen.dart';
import 'package:new_islamic_app_uk/features/quran/screens/reader/reader_screen.dart';
import 'package:new_islamic_app_uk/features/quran/services/quran_service.dart';

/// Main entry point for the Quran feature module.
/// This can be used as a navigator destination in the main app.
class QuranModule extends StatefulWidget {
  /// If true, the module will automatically navigate to the last read position
  /// if there's any saved reading progress.
  final bool continueReading;
  
  const QuranModule({
    Key? key,
    this.continueReading = false,
  }) : super(key: key);
  
  @override
  State<QuranModule> createState() => _QuranModuleState();
}

class _QuranModuleState extends State<QuranModule> {
  final QuranService _quranService = Get.find<QuranService>();
  bool _isLoading = true;
  
  @override
  void initState() {
    super.initState();
    if (widget.continueReading) {
      _checkReadingProgress();
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  Future<void> _checkReadingProgress() async {
    final progress = await _quranService.getReadingProgress();
    if (progress != null && mounted) {
      // Get surah info
      final surah = _quranService.getAllSurahs().firstWhere(
        (s) => s.number == progress.surahNumber,
        orElse: () => SurahInfo(
          number: 1,
          nameArabic: '',
          nameEnglish: 'Al-Fatiha',
          nameTransliteration: '',
          versesCount: 7,
          placeOfRevelation: 'Mecca',
        ),
      );
      
      // Navigate to reader screen with last read position
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => QuranReaderScreen(
              surahInfo: surah,
              initialVerseNumber: progress.lastReadVerse,
            ),
          ),
        );
      });
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }
    
    return const QuranHomeScreen();
  }
}

/// Route generator for Quran feature.
/// Use this to register Quran feature routes in your main app.
class QuranRoutes {
  static const String home = '/quran';
  static const String reader = '/quran/reader';
  static const String continueReading = '/quran/continue';
  
  /// Register the Quran feature routes in your main app.
  static Map<String, WidgetBuilder> getRoutes() {
    return {
      home: (context) => const QuranModule(),
      continueReading: (context) => const QuranModule(continueReading: true),
    };
  }
} 