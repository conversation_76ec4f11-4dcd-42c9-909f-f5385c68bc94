import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';
import 'package:new_islamic_app_uk/features/quran/services/quran_service.dart';

/// Controller responsible for managing all Quran reader settings using GetX
class QuranSettingsController extends GetxController {
  late final QuranService _quranService;
  
  // Observable settings variables
  final fontSize = 26.0.obs;
  final showArabicText = true.obs;
  final showTranslation = true.obs; 
  final arabicFontFamily = 'ScheherazadeNew'.obs;
  final selectedTranslation = QuranTranslation.enSaheeh.obs;
  final isRtlTranslation = false.obs;
  final isDarkMode = false.obs;
  
  // New settings for text alignment
  final arabicTextAlignment = TextAlign.center.obs;
  final translationTextAlignment = TextAlign.left.obs;
  
  // Keys for SharedPreferences
  static const String _fontSizeKey = 'quran_font_size';
  static const String _showArabicTextKey = 'quran_show_arabic';
  static const String _showTranslationKey = 'quran_show_translation';
  static const String _arabicFontFamilyKey = 'quran_arabic_font';
  static const String _selectedTranslationKey = 'quran_translation_type';
  static const String _isRtlTranslationKey = 'quran_translation_rtl';
  static const String _isDarkModeKey = 'quran_dark_mode';
  static const String _arabicTextAlignmentKey = 'quran_arabic_alignment';
  static const String _translationTextAlignmentKey = 'quran_translation_alignment';
  
  @override
  void onInit() {
    super.onInit();
    _quranService = Get.put(QuranService(), permanent: true);
    loadSettings();
    
    // Set up a listener for translation changes to update RTL status
    ever(selectedTranslation, (_) => updateRtlStatus());
  }
  
  /// Loads all settings from SharedPreferences
  Future<void> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load font size
      final savedFontSize = prefs.getDouble(_fontSizeKey);
      if (savedFontSize != null) {
        fontSize.value = savedFontSize;
      }
      
      // Load show Arabic text setting
      final savedShowArabic = prefs.getBool(_showArabicTextKey);
      if (savedShowArabic != null) {
        showArabicText.value = savedShowArabic;
      }
      
      // Load show translation setting
      final savedShowTranslation = prefs.getBool(_showTranslationKey);
      if (savedShowTranslation != null) {
        showTranslation.value = savedShowTranslation;
      }
      
      // Load Arabic font family
      final savedArabicFont = prefs.getString(_arabicFontFamilyKey);
      if (savedArabicFont != null) {
        arabicFontFamily.value = savedArabicFont;
      }
      
      // Load selected translation
      final savedTranslation = await _quranService.getSelectedTranslation();
      selectedTranslation.value = savedTranslation;
      
      // Load text alignments
      final savedArabicAlign = prefs.getString(_arabicTextAlignmentKey);
      if (savedArabicAlign != null) {
        arabicTextAlignment.value = _textAlignFromString(savedArabicAlign);
      }
      
      final savedTranslationAlign = prefs.getString(_translationTextAlignmentKey);
      if (savedTranslationAlign != null) {
        translationTextAlignment.value = _textAlignFromString(savedTranslationAlign);
      }
      
      // Update RTL status based on translation
      updateRtlStatus();
      
      print('QuranSettingsController: Settings loaded successfully');
      print('Font size: ${fontSize.value}');
      print('Arabic font: ${arabicFontFamily.value}');
      print('Translation: ${selectedTranslation.value}');
      print('RTL: ${isRtlTranslation.value}');
      print('Arabic alignment: ${arabicTextAlignment.value}');
      print('Translation alignment: ${translationTextAlignment.value}');
    } catch (e) {
      print('Error loading settings: $e');
      // Keep default values if loading fails
    }
  }
  
  /// Updates the font size and saves to SharedPreferences
  Future<void> updateFontSize(double newSize) async {
    try {
      fontSize.value = newSize;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_fontSizeKey, newSize);
      print('QuranSettingsController: Font size updated to $newSize');
    } catch (e) {
      print('Error saving font size: $e');
    }
  }
  
  /// Updates showing Arabic text setting and saves to SharedPreferences
  Future<void> toggleArabicText(bool show) async {
    try {
      showArabicText.value = show;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_showArabicTextKey, show);
      print('QuranSettingsController: Arabic text visibility set to $show');
    } catch (e) {
      print('Error saving Arabic text setting: $e');
    }
  }
  
  /// Updates showing translation setting and saves to SharedPreferences
  Future<void> toggleTranslation(bool show) async {
    try {
      showTranslation.value = show;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_showTranslationKey, show);
      print('QuranSettingsController: Translation visibility set to $show');
    } catch (e) {
      print('Error saving translation setting: $e');
    }
  }
  
  /// Updates Arabic font family and saves to SharedPreferences
  Future<void> updateArabicFont(String fontFamily) async {
    try {
      arabicFontFamily.value = fontFamily;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_arabicFontFamilyKey, fontFamily);
      print('QuranSettingsController: Arabic font updated to $fontFamily');
    } catch (e) {
      print('Error saving Arabic font: $e');
    }
  }
  
  /// Updates selected translation and saves to SharedPreferences
  Future<void> updateTranslation(QuranTranslation translation) async {
    try {
      selectedTranslation.value = translation;
      await _quranService.saveSelectedTranslation(translation);
      print('QuranSettingsController: Translation updated to $translation');
      
      // updateRtlStatus is called automatically via the ever() listener
    } catch (e) {
      print('Error saving translation: $e');
    }
  }
  
  /// Updates Arabic text alignment and saves to SharedPreferences
  Future<void> updateArabicTextAlignment(TextAlign alignment) async {
    try {
      arabicTextAlignment.value = alignment;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_arabicTextAlignmentKey, _textAlignToString(alignment));
      print('QuranSettingsController: Arabic text alignment updated to $alignment');
    } catch (e) {
      print('Error saving Arabic text alignment: $e');
    }
  }
  
  /// Updates translation text alignment and saves to SharedPreferences
  Future<void> updateTranslationTextAlignment(TextAlign alignment) async {
    try {
      translationTextAlignment.value = alignment;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_translationTextAlignmentKey, _textAlignToString(alignment));
      print('QuranSettingsController: Translation alignment updated to $alignment');
    } catch (e) {
      print('Error saving translation alignment: $e');
    }
  }
  
  /// Updates RTL status based on the selected translation
  void updateRtlStatus() {
    final rtlTranslations = [
      QuranTranslation.urUrdu,
      QuranTranslation.urJalandhry,
      QuranTranslation.faFarsi
    ];
    
    isRtlTranslation.value = rtlTranslations.contains(selectedTranslation.value);
    print('QuranSettingsController: RTL status updated to ${isRtlTranslation.value}');
    
    // Auto-update text alignment based on language direction
    if (isRtlTranslation.value) {
      translationTextAlignment.value = TextAlign.right;
    } else {
      translationTextAlignment.value = TextAlign.left;
    }
  }
  
  /// Updates theme based on device theme
  void updateTheme(BuildContext context) {
    isDarkMode.value = Theme.of(context).brightness == Brightness.dark;
  }
  
  /// Resets all settings to defaults
  Future<void> resetToDefaults() async {
    await updateFontSize(26.0);
    await toggleArabicText(true);
    await toggleTranslation(true);
    await updateArabicFont('ScheherazadeNew');
    await updateTranslation(QuranTranslation.enSaheeh);
    await updateArabicTextAlignment(TextAlign.center);
    await updateTranslationTextAlignment(TextAlign.left);
    print('QuranSettingsController: Settings reset to defaults');
  }
  
  /// Helper to convert TextAlign to string for storage
  String _textAlignToString(TextAlign align) {
    switch (align) {
      case TextAlign.left:
        return 'left';
      case TextAlign.center:
        return 'center';
      case TextAlign.right:
        return 'right';
      case TextAlign.justify:
        return 'justify';
      default:
        return 'center'; // Default for Arabic
    }
  }
  
  /// Helper to convert string to TextAlign from storage
  TextAlign _textAlignFromString(String align) {
    switch (align) {
      case 'left':
        return TextAlign.left;
      case 'center':
        return TextAlign.center;
      case 'right':
        return TextAlign.right;
      case 'justify':
        return TextAlign.justify;
      default:
        return TextAlign.center; // Default
    }
  }
} 