
import 'package:audioplayers/audioplayers.dart';
import 'package:clipboard/clipboard.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:quran/quran.dart' as quran;
import 'package:quran/surah_data.dart';
import 'package:share_plus/share_plus.dart';


class readQuranPage extends StatefulWidget {
  var player;
  var id;
  var surahName;

  readQuranPage({Key? key, this.id, this.surahName, this.player})
      : super(key: key);
  @override
  State<readQuranPage> createState() => _readQuranPageState();
}

class _readQuranPageState extends State<readQuranPage> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // aya();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: InkWell(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                margin: EdgeInsets.only(left:10, top: 5, bottom: 5),
                padding: EdgeInsets.only(left:8),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12)
                ),
                child: Icon(Icons.arrow_back_ios, color: Colors.white,),
              ),
            ),
      ),
      body: Container(
        decoration: BoxDecoration(
          image: DecorationImage(image: AssetImage("assets/bg.jpeg"), fit: BoxFit.cover)
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                width: MediaQuery.of(context).size.width,
                padding: EdgeInsets.only(top: 70, bottom: 150),
                decoration: BoxDecoration(
                  image: DecorationImage(
                      image: AssetImage("assets/dua_header.png"),
                      fit: BoxFit.cover),
                ),
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 2.0),
                  child: Column(
                    children: [
                      // Text("${englishVerse.length}"),
                      Text(
                        '${quran.getSurahNameArabic(widget.id)}',
                        style: GoogleFonts.quicksand(
                          fontSize: 40,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${quran.getSurahNameEnglish(widget.id)}',
                        style: GoogleFonts.quicksand(
                          fontSize: 20,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      // Container(
                      //   child: ListTile(
                      //     leading: Text(
                      //         "Verses: ${quran.getVerseCount(widget.id)}",
                      //         style: GoogleFonts.quicksand(
                      //             fontSize: 20, color: Colors.white)),
                      //     trailing: Text("Surah: ${widget.id}",
                      //         style: GoogleFonts.quicksand(
                      //             fontSize: 20, color: Colors.white)),
                      //   ),
                      // ),
                    ],
                  ),
                ),
              ),
        
              Visibility(
                visible: widget.id != 1,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: Card(
                    elevation: 0,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 0),
                      child: Column(
                        children: [
                          ListTile(
                            onTap: () {},
                            title: Text(
                              "${quran.basmala}",
                              style: TextStyle(fontSize: 28),
                              textAlign: TextAlign.right,
                            ),
                            subtitle: Text(
                                  "${quran.getVerseTranslation(1, 1, verseEndSymbol: false, translation: quran.Translation.enSaheeh)}"),
                            
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              // TextButton(
                              //   onPressed: () {
                              //     Fluttertoast.showToast(
                              //         msg: "Fetching Data Please Wait...",
                              //         toastLength: Toast.LENGTH_SHORT,
                              //         gravity: ToastGravity.BOTTOM,
                              //         timeInSecForIosWeb: 1,
                              //         backgroundColor: Colors.black87,
                              //         textColor: Colors.white,
                              //         fontSize: 16.0);
                              //     _pause();
                              //     widget.player.play(DeviceFileSource(
                              //         "${quran.getAudioURLByVerse(1, 1)}"));
                              //   },
                              //   child: Icon(Icons.volume_down),
                              // ),
                              TextButton(
                                onPressed: () {
                                  Share.share("${quran.basmala}");
                                },
                                child: Icon(Icons.share),
                              ),
                              TextButton(
                                onPressed: () {
                                  FlutterClipboard.copy("${quran.basmala}").then(
                                      (value) => ());
                                },
                                child: Icon(Icons.copy),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.all(4.0),
                child: ListView.builder(
                  padding: EdgeInsets.all(0),
                  itemCount: quran.getVerseCount(widget.id),
                  shrinkWrap: true,
                  physics: PageScrollPhysics(),
                  itemBuilder: (context, index) {
                    return Card(
                      elevation: 0,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        child: Column(
                          children: [
                            ListTile(
                              onTap: () {},
                              title: Text(
                                "${quran.getVerse(widget.id, index + 1, verseEndSymbol: true)}",
                                style: TextStyle(fontSize: 28),
                                textAlign: TextAlign.right,
                              ),
                              subtitle: Text(
                                  "${quran.getVerseTranslation(widget.id, index + 1, verseEndSymbol: false, translation: quran.Translation.enSaheeh)}"),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                // TextButton(
                                //   onPressed: () {
                                //     Fluttertoast.showToast(
                                //         msg: "Fetching Data Please Wait...",
                                //         toastLength: Toast.LENGTH_SHORT,
                                //         gravity: ToastGravity.BOTTOM,
                                //         timeInSecForIosWeb: 1,
                                //         backgroundColor: Colors.black87,
                                //         textColor: Colors.white,
                                //         fontSize: 16.0);
        
                                //     _pause();
        
                                //     widget.player.play(DeviceFileSource(
                                //         "${quran.getAudioURLByVerse(widget.id, index + 1)}"));
                                //   },
                                //   child: Icon(Icons.volume_down),
                                // ),
                                TextButton(
                                  onPressed: () {
                                    Share.share(
                                        "${quran.getVerse(widget.id, index + 1, verseEndSymbol: true)}");
                                  },
                                  child: Icon(Icons.share),
                                ),
                                TextButton(
                                  onPressed: () {
                                    FlutterClipboard.copy(
                                            "${quran.getVerse(widget.id, index + 1, verseEndSymbol: true)}")
                                        .then((value) => ());
                                  },
                                  child: Icon(Icons.copy),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              // Text(
              //   "${widget.title}",
              //   style: TextStyle(fontSize: 22),
              // ),
              // Padding(
              //   padding: const EdgeInsets.all(22.0),
              //   child: Container(
              //     child: HtmlWidget("${widget.content}",
              //         // textStyle: TextStyle(fontSize: 30),
              //         customStylesBuilder: (e) {
              //       if (e.localName == 'p') {
              //         return {'font-size': "26px", 'text-align': 'right'};
              //       }
              //     }
              //         // style: TextStyle(fontSize: 18),
              //         ),
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _pause() async {
    await widget.player.pause();
    // setState(() => _playerState = PlayerState.paused);
  }

  // loadSurah(String translationLang) async {
//     var surah = await getSurahData(
//         surahNumber: defaultSurah,
//         translationLang: withTranslation ? translationLang : null,
//         transliterationLang: withTransliteration ? translationLang : null,
//         tafseer: withTafseer ? tafseer[defaultLang] : null);
//     var surahList = await getSurahList();
//     var aya = await getAyaData(
//         surahNumber: defaultSurah,
//         ayaNumber: defaultAyah,
//         translationLang: withTranslation ? translationLang : null,
//         transliterationLang: withTransliteration ? translationLang : null,
//         tafseer: withTafseer ? tafseer[defaultLang] : null);
//     setState(() {
//       surahData = surah;
//       surahListData = surahList;
//       aya11 = aya;
//     });
  // }
  // var surah = SurahContentModel();

  // void getSurahContent() async {
  //   var surahData = await getSurahData(
  //     surahNumber: 1,
  //     translationLang: "english",
  //   );
  //   setState(() {
  //     surah = surahData;
  //     // isLoadingSurahContent = false;
  //   });
  // }

  // aya() {
  //   return getAyaData(surahNumber: 1, ayaNumber: 1, translationLang: 'english');
  // }

  // List<EnglishVerse> englishVerse = [];

  // Future<List<EnglishVerse>> getHttp() async {
  //   try {
  //     var response = await Dio().get(
  //         'https://api.quran.com/api/v4/verses/by_chapter/${widget.id}?language=en&words=true');
  //     var parsedData =
  //         response.data.map((res) => EnglishVerse.fromJson(res)).toList();
  //     parsedData.forEach(
  //       (f) => englishVerse.add(f),
  //     );
  //   } catch (e) {
  //     print(e);
  //   }
  //   return englishVerse;
  //   // setState(() {
  //   //   return englishVerse;
  //   // });
  // }
}



// import 'package:flutter/material.dart';
// import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:quran/quran.dart' as quran;

// import 'URLs.dart';

// class readQuranPage extends StatefulWidget {
//   // var title;
//   // var content;
//   var id;
//   var surahName;

//   readQuranPage({Key? key, this.id, this.surahName}) : super(key: key);
//   @override
//   State<readQuranPage> createState() => _readQuranPageState();
// }

// class _readQuranPageState extends State<readQuranPage> {
//   @override
//   Widget build(BuildContext context) {
//     // TODO: implement build
//     return Scaffold(
//       appBar: AppBar(
//         elevation: 0,
//         backgroundColor: Color(URLs().colorScheme),
//       ),
//       body: SingleChildScrollView(
//         child: Column(
//           children: [
//             Container(
//               width: MediaQuery.of(context).size.width,
//               height: 160,
//               padding: EdgeInsets.all(20),
//               // margin: EdgeInsets.symmetric(horizontal: 5.0),
//               decoration: BoxDecoration(
//                 image: DecorationImage(
//                     image: AssetImage("assets/dua_header.png"),
//                     fit: BoxFit.cover),
//               ),
//               child: Padding(
//                 padding: const EdgeInsets.only(bottom: 72.0),
//                 child: Center(
//                   child: Text(
//                     '${widget.surahName}',
//                     style: GoogleFonts.quicksand(
//                       fontSize: 30,
//                       color: Colors.white,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                 ),
//               ),
//             ),

//             Container(
//               child: ListTile(
//                 leading: Text("Verses: ${quran.getVerseCount(widget.id)}",
//                     style: GoogleFonts.montserrat(fontSize: 20)),
//                 trailing: Text("Surah: ${widget.id}",
//                     style: GoogleFonts.montserrat(fontSize: 20)),
//               ),
//             ),

//             Visibility(
//               visible: widget.id != 1,
//               child: Padding(
//                 padding: const EdgeInsets.symmetric(horizontal: 15),
//                 child: Card(
//                   child: Center(
//                     child: Padding(
//                       padding: const EdgeInsets.all(15.0),
//                       child: Text(
//                         "${quran.basmala}",
//                         style: TextStyle(fontSize: 26),
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//             Padding(
//               padding: EdgeInsets.all(15.0),
//               child: ListView.builder(
//                 itemCount: quran.getVerseCount(widget.id),
//                 shrinkWrap: true,
//                 physics: PageScrollPhysics(),
//                 itemBuilder: (context, index) {
//                   return Card(
//                     elevation: 0,
//                     child: Padding(
//                       padding: const EdgeInsets.symmetric(vertical: 10),
//                       child: ListTile(
//                         onTap: () {},
//                         trailing: Container(
//                           height: 28,
//                           width: 28,
//                           decoration: BoxDecoration(
//                               color: Color(URLs().colorScheme).withOpacity(0.2),
//                               borderRadius: BorderRadius.circular(15)),
//                           child: Padding(
//                             padding: const EdgeInsets.only(top: 6.0),
//                             child: Text(
//                               "${index + 1}",
//                               textAlign: TextAlign.center,
//                             ),
//                           ),
//                         ),
//                         title: Text(
//                           quran.getVerse(widget.id, index + 1,
//                               verseEndSymbol: true),
//                           style: TextStyle(fontSize: 30),
//                           textAlign: TextAlign.right,
//                         ),
//                       ),
//                     ),
//                   );
//                 },
//               ),
//             ),
//             // Text(
//             //   "${widget.title}",
//             //   style: TextStyle(fontSize: 22),
//             // ),
//             // Padding(
//             //   padding: const EdgeInsets.all(22.0),
//             //   child: Container(
//             //     child: HtmlWidget("${widget.content}",
//             //         // textStyle: TextStyle(fontSize: 30),
//             //         customStylesBuilder: (e) {
//             //       if (e.localName == 'p') {
//             //         return {'font-size': "26px", 'text-align': 'right'};
//             //       }
//             //     }
//             //         // style: TextStyle(fontSize: 18),
//             //         ),
//             //   ),
//             // ),
//           ],
//         ),
//       ),
//     );
//   }
// }
