import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'URLs.dart';

class AboutPage extends StatefulWidget {
  @override
  _AboutPageState createState() => _AboutPageState();
}

class _AboutPageState extends State<AboutPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: URLs.primaryColorObj,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          "About Us",
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [URLs.primaryColorObj, Colors.white],
            stops: [0.0, 0.3],
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Mosque Logo/Image
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(60),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 10,
                            offset: Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.mosque,
                        size: 60,
                        color: URLs.primaryColorObj,
                      ),
                    ),
                    SizedBox(height: 20),

                    // Mosque Name
                    Text(
                      "Masjid Sunnah Nelson",
                      style: GoogleFonts.poppins(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      "Nelson, Lancashire, UK",
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),

              // Main Content Card
              Container(
                margin: EdgeInsets.all(20),
                padding: EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: Offset(0, 5),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // About Section
                    Text(
                      "About Our Mosque",
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: URLs.primaryColorObj,
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      "Welcome to Masjid Sunnah Nelson, your local Islamic center serving the Muslim community in Nelson, UK. Our mosque provides daily prayers, Friday Jumuah services, Islamic education, and community support. We strive to follow the authentic teachings of Islam according to the Quran and Sunnah.",
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        height: 1.6,
                        color: Colors.grey[700],
                      ),
                    ),
                    SizedBox(height: 24),

                    // Services Section
                    Text(
                      "Our Services",
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: URLs.primaryColorObj,
                      ),
                    ),
                    SizedBox(height: 12),
                    _buildServiceItem("Daily Five Prayers"),
                    _buildServiceItem("Friday Jumuah Services"),
                    _buildServiceItem("Islamic Education Classes"),
                    _buildServiceItem("Community Events & Programs"),
                    _buildServiceItem("Marriage & Funeral Services"),
                    _buildServiceItem("Charity & Outreach Programs"),

                    SizedBox(height: 24),

                    // Contact Section
                    Text(
                      "Contact Information",
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: URLs.primaryColorObj,
                      ),
                    ),
                    SizedBox(height: 12),
                    _buildContactItem(Icons.location_on, "Nelson, Lancashire, UK"),
                    _buildContactItem(Icons.phone, "Contact: Available in mosque"),
                    _buildContactItem(Icons.email, "Email: Available in mosque"),

                    SizedBox(height: 32),

                    // App Info
                    Center(
                      child: Column(
                        children: [
                          Text(
                            "Islamic App for Masjid Sunnah Nelson",
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: URLs.primaryColorObj,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            "© ${DateTime.now().year} Masjid Sunnah Nelson",
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            "All rights reserved",
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildServiceItem(String service) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: URLs.primaryColorObj,
            size: 20,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              service,
              style: GoogleFonts.poppins(
                fontSize: 15,
                color: Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactItem(IconData icon, String text) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Icon(
            icon,
            color: URLs.primaryColorObj,
            size: 20,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.poppins(
                fontSize: 15,
                color: Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
