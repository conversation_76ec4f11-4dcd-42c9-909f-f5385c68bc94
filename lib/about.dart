// import 'package:assorted_layout_widgets/assorted_layout_widgets.dart';
// import 'package:assorted_layout_widgets/assorted_layout_widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:html_unescape/html_unescape.dart';
import 'package:url_launcher/url_launcher.dart';
import 'URLs.dart';

class AboutMeApp extends StatefulWidget {
   var id;
   var name;
   var image;

  AboutMeApp({Key? key, this.id, this.name, this.image}) : super(key: key);

  // This widget is the root of your application.
  @override
  AboutMe createState() => AboutMe();
}

class AboutMe extends State<AboutMeApp> {
  var item = 1;
  var connectivityResult;
  static var unescape = new HtmlUnescape();

  @override
  Widget build(BuildContext context) {
    var appBarHeight = AppBar().preferredSize.height;
    var tabBarHeight = TabBar(tabs: []).preferredSize.height;
    double statusBarHeight = MediaQuery.of(context).padding.top;

    var totalHeight = appBarHeight + tabBarHeight + statusBarHeight - 100;

    return Material(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Color(URLs().colorScheme),
          elevation: 0,
        ),
        body: Container(
          height: MediaQuery.of(context).size.height - totalHeight,
          child: SingleChildScrollView(
              child: Column(
            children: [
              Container(
                height: 110,
                color: Color(URLs().colorScheme),
                child: Center(
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 30.0),
                      ),
                      Text(
                        "",
                        style: TextStyle(color: Colors.white, fontSize: 24),
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                    left: 12.0, top: 0, right: 12, bottom: 12),
                child: Column(
                  children: [
                    Column(
                      // innerDistance: 8.0,
                      // outerDistance: -84.0,
                      children: [
                        Center(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(100),
                            child: Container(
                              height: 160,
                              width: 160,
                              color: Colors.white,
                              child: Padding(
                                padding: const EdgeInsets.all(20.0),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(70),
                                  child: Image(
                                    image: AssetImage('assets/logo.png'),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 10.0,
                          ),
                          child: Text("Welcome to Islamic App",
                              style: GoogleFonts.comfortaa(
                                  fontSize: 22, fontWeight: FontWeight.w700)),
                        ),
                        // Text(
                        //   "Islamic App",
                        //   style: GoogleFonts.comfortaa(fontSize: 12),
                        // ),
                        Padding(
                          padding: const EdgeInsets.only(
                              top: 20.0, left: 10, right: 10, bottom: 20),
                          child: Text(
                            "“Islam is an Abrahamic, monotheistic religion teaching that Muhammad is the final prophet and messenger of God. It is the world's second-largest religion with approximately 1.9 billion adherents, comprising 24.9% of the world's population, known as Muslims; Muslims make up a majority of the population in 47 countries.”",
                            style: GoogleFonts.comfortaa(fontSize: 18),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Container(
                          height: 2,
                          width: 80,
                          color: Colors.blue,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 20.0, bottom: 10),
                          child: Text("Follow Us",
                              style: GoogleFonts.comfortaa(
                                  fontSize: 20, fontWeight: FontWeight.w700)),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            InkWell(
                              onTap: () {
                                _launchURL('https://www.facebook.com/');
                              },
                              child: Container(
                                width: 45,
                                padding: EdgeInsets.all(8),
                                child: Image(
                                    image: AssetImage('assets/facebook.png')),
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                _launchURL('https://www.instagram.com');
                              },
                              child: Container(
                                width: 48,
                                padding: EdgeInsets.all(8),
                                child: Image(
                                    image: AssetImage('assets/instagram.png')),
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                _launchURL('https://twitter.com/');
                              },
                              child: Container(
                                width: 48,
                                padding: EdgeInsets.all(8),
                                child: Image(
                                    image: AssetImage('assets/twitter.png')),
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                _launchURL('https://www.linkedin.com/');
                              },
                              child: Container(
                                width: 48,
                                padding: EdgeInsets.all(8),
                                child: Image(
                                    image: AssetImage('assets/linkedin.png')),
                              ),
                            ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                              top: 20, left: 10, right: 10, bottom: 5),
                          child: Text(
                            "© 2021 Islaimic App, All rights reserved",
                            style: TextStyle(
                                fontSize: 14, color: Color(0xFF888888)),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Container(
                          height: 170,
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ],
          )),
        ),
      ),
    );
  }

  static _launchURL(url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}
