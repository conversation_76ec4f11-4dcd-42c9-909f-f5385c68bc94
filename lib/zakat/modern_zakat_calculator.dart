import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../theme/app_theme.dart';
import '../widgets/common/modern_card.dart';

class ModernZakatCalculator extends StatefulWidget {
  const ModernZakatCalculator({Key? key}) : super(key: key);

  @override
  State<ModernZakatCalculator> createState() => _ModernZakatCalculatorState();
}

class _ModernZakatCalculatorState extends State<ModernZakatCalculator> {
  final _goldController = TextEditingController();
  final _silverController = TextEditingController();
  final _cashController = TextEditingController();
  final _businessController = TextEditingController();
  
  double _totalZakat = 0.0;
  double _nisabValue = 4500.0; // Current Nisab value in USD
  
  @override
  void dispose() {
    _goldController.dispose();
    _silverController.dispose();
    _cashController.dispose();
    _businessController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header info
            _buildHeaderInfo(),
            
            const SizedBox(height: 24),
            
            // Input fields
            _buildInputSection(),
            
            const SizedBox(height: 24),
            
            // Nisab information
            _buildNisabInfo(),
            
            const SizedBox(height: 24),
            
            // Results
            _buildResults(),
            
            const SizedBox(height: 24),
            
            // Calculate button
            _buildCalculateButton(),
            
            const SizedBox(height: 24),
            
            // Educational content
            _buildEducationalContent(),
            
            const SizedBox(height: 100),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(
          Icons.arrow_back_ios,
          color: AppTheme.textPrimary,
        ),
      ),
      title: Text(
        'Zakat Calculator',
        style: AppTheme.textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildHeaderInfo() {
    return GradientCard(
      gradient: AppTheme.primaryGradient,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                FontAwesomeIcons.coins,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Calculate Your Zakat',
                style: GoogleFonts.poppins(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Zakat is one of the Five Pillars of Islam. Calculate your obligatory charity based on your wealth.',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.white.withOpacity(0.9),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Enter Your Assets',
          style: AppTheme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        // Gold
        _buildInputCard(
          title: 'Gold',
          hint: 'Enter gold weight (grams)',
          controller: _goldController,
          icon: FontAwesomeIcons.ring,
          color: const Color(0xFFFFD700),
        ),
        
        const SizedBox(height: 12),
        
        // Silver
        _buildInputCard(
          title: 'Silver',
          hint: 'Enter silver weight (grams)',
          controller: _silverController,
          icon: FontAwesomeIcons.coins,
          color: const Color(0xFFC0C0C0),
        ),
        
        const SizedBox(height: 12),
        
        // Cash
        _buildInputCard(
          title: 'Cash',
          hint: 'Enter cash amount',
          controller: _cashController,
          icon: FontAwesomeIcons.dollarSign,
          color: const Color(0xFF4CAF50),
        ),
        
        const SizedBox(height: 12),
        
        // Business Assets
        _buildInputCard(
          title: 'Business Assets',
          hint: 'Enter business assets value',
          controller: _businessController,
          icon: FontAwesomeIcons.briefcase,
          color: const Color(0xFF2196F3),
        ),
      ],
    );
  }

  Widget _buildInputCard({
    required String title,
    required String hint,
    required TextEditingController controller,
    required IconData icon,
    required Color color,
  }) {
    return ModernCard(
      margin: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: FaIcon(
                  icon,
                  color: color,
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: AppTheme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          TextField(
            controller: controller,
            keyboardType: TextInputType.number,
            decoration: AppTheme.getInputDecoration(hint),
            onChanged: (value) => _calculateZakat(),
          ),
        ],
      ),
    );
  }

  Widget _buildNisabInfo() {
    return ModernCard(
      backgroundColor: AppTheme.surfaceColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.info_outline,
                color: AppTheme.primaryGreen,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Nisab Value',
                style: AppTheme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Current Nisab value: \$${_nisabValue.toStringAsFixed(0)}',
            style: AppTheme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Zakat is obligatory if your wealth exceeds the Nisab threshold.',
            style: AppTheme.textTheme.bodySmall,
          ),
        ],
      ),
    );
  }

  Widget _buildResults() {
    return ModernCard(
      backgroundColor: _totalZakat > 0 ? AppTheme.primaryGreen.withOpacity(0.1) : AppTheme.surfaceColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.calculator,
                color: _totalZakat > 0 ? AppTheme.primaryGreen : AppTheme.textSecondary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Results',
                style: AppTheme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Total Zakat Due: \$${_totalZakat.toStringAsFixed(2)}',
            style: AppTheme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: _totalZakat > 0 ? AppTheme.primaryGreen : AppTheme.textSecondary,
            ),
          ),
          if (_totalZakat > 0) ...[
            const SizedBox(height: 8),
            Text(
              'Your wealth exceeds the Nisab threshold. Zakat is obligatory.',
              style: AppTheme.textTheme.bodySmall?.copyWith(
                color: AppTheme.primaryGreen,
                fontWeight: FontWeight.w500,
              ),
            ),
          ] else ...[
            const SizedBox(height: 8),
            Text(
              'Enter your assets above to calculate zakat.',
              style: AppTheme.textTheme.bodySmall,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCalculateButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _calculateZakat,
        style: AppTheme.primaryButtonStyle,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Text(
            'Calculate Zakat',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEducationalContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Learn more about Zakat',
          style: AppTheme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        ModernCard(
          margin: EdgeInsets.zero,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Zakat Rules',
                style: AppTheme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '• Zakat is 2.5% of your qualifying wealth\n'
                '• Wealth must be held for one lunar year\n'
                '• Must exceed the Nisab threshold\n'
                '• Calculated on gold, silver, cash, and business assets',
                style: AppTheme.textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _calculateZakat() {
    double gold = double.tryParse(_goldController.text) ?? 0;
    double silver = double.tryParse(_silverController.text) ?? 0;
    double cash = double.tryParse(_cashController.text) ?? 0;
    double business = double.tryParse(_businessController.text) ?? 0;
    
    // Convert gold and silver to USD (approximate values)
    double goldValue = gold * 65; // $65 per gram (approximate)
    double silverValue = silver * 0.8; // $0.8 per gram (approximate)
    
    double totalWealth = goldValue + silverValue + cash + business;
    
    setState(() {
      if (totalWealth >= _nisabValue) {
        _totalZakat = totalWealth * 0.025; // 2.5%
      } else {
        _totalZakat = 0;
      }
    });
  }
}
