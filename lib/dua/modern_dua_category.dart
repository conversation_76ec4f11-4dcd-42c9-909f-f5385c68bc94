import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../theme/app_theme.dart';
import '../widgets/common/modern_card.dart';
import '../widgets/common/modern_search_bar.dart';

class ModernDuaCategory extends StatefulWidget {
  const ModernDuaCategory({Key? key}) : super(key: key);

  @override
  State<ModernDuaCategory> createState() => _ModernDuaCategoryState();
}

class _ModernDuaCategoryState extends State<ModernDuaCategory>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _selectedTabIndex = 0;
  bool _isLoading = false;
  
  final List<Map<String, dynamic>> _duaCategories = [
    {
      'id': 'morning_evening',
      'name': 'Morning & Evening',
      'count': 15,
      'icon': FontAwesomeIcons.sun,
      'color': Color(0xFFFF9800),
    },
    {
      'id': 'prayer',
      'name': 'Prayer',
      'count': 12,
      'icon': FontAwesomeIcons.mosque,
      'color': Color(0xFF4CAF50),
    },
    {
      'id': 'home',
      'name': 'Home',
      'count': 8,
      'icon': FontAwesomeIcons.house,
      'color': Color(0xFF2196F3),
    },
    {
      'id': 'travel',
      'name': 'Travel',
      'count': 10,
      'icon': FontAwesomeIcons.plane,
      'color': Color(0xFF9C27B0),
    },
    {
      'id': 'health',
      'name': 'Health',
      'count': 6,
      'icon': FontAwesomeIcons.heartPulse,
      'color': Color(0xFFE91E63),
    },
    {
      'id': 'protection',
      'name': 'Protection',
      'count': 9,
      'icon': FontAwesomeIcons.shield,
      'color': Color(0xFF607D8B),
    },
  ];

  final List<Map<String, dynamic>> _azkarCategories = [
    {
      'id': 'morning_azkar',
      'name': 'Morning Azkar',
      'count': 25,
      'icon': FontAwesomeIcons.sunrise,
      'color': Color(0xFFFF9800),
    },
    {
      'id': 'evening_azkar',
      'name': 'Evening Azkar',
      'count': 22,
      'icon': FontAwesomeIcons.moon,
      'color': Color(0xFF3F51B5),
    },
    {
      'id': 'after_prayer',
      'name': 'After Prayer',
      'count': 18,
      'icon': FontAwesomeIcons.handsPraying,
      'color': Color(0xFF4CAF50),
    },
    {
      'id': 'sleeping',
      'name': 'Before Sleep',
      'count': 12,
      'icon': FontAwesomeIcons.bed,
      'color': Color(0xFF9C27B0),
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _selectedTabIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildDuasTab(),
                _buildAzkarTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(
          Icons.arrow_back_ios,
          color: AppTheme.textPrimary,
        ),
      ),
      title: Text(
        'Duas & Azkar',
        style: AppTheme.textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        IconButton(
          onPressed: () {
            // Search functionality
          },
          icon: const Icon(
            Icons.search,
            color: AppTheme.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppTheme.primaryGreen,
          borderRadius: BorderRadius.circular(12),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondary,
        labelStyle: GoogleFonts.poppins(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: GoogleFonts.poppins(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        tabs: const [
          Tab(text: 'Duas'),
          Tab(text: 'Azkar'),
        ],
      ),
    );
  }

  Widget _buildDuasTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search bar
          ModernSearchBar(
            hintText: 'Search duas...',
            margin: const EdgeInsets.only(bottom: 20),
          ),
          
          // Quick Access Section
          _buildQuickAccessSection(),
          
          const SizedBox(height: 24),
          
          // Categories Section
          Text(
            'Categories',
            style: AppTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Categories Grid
          GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.1,
            ),
            itemCount: _duaCategories.length,
            itemBuilder: (context, index) {
              final category = _duaCategories[index];
              return CategoryCard(
                title: category['name'],
                subtitle: '${category['count']} duas',
                icon: category['icon'],
                iconColor: category['color'],
                backgroundColor: Colors.white,
                margin: EdgeInsets.zero,
                onTap: () => _navigateToDuaList(category),
              );
            },
          ),
          
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  Widget _buildAzkarTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search bar
          ModernSearchBar(
            hintText: 'Search azkar...',
            margin: const EdgeInsets.only(bottom: 20),
          ),
          
          // Featured Azkar
          _buildFeaturedAzkar(),
          
          const SizedBox(height: 24),
          
          // Categories Section
          Text(
            'Categories',
            style: AppTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Categories List
          ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: _azkarCategories.length,
            itemBuilder: (context, index) {
              final category = _azkarCategories[index];
              return FeatureCard(
                title: category['name'],
                description: '${category['count']} azkar',
                icon: category['icon'],
                iconColor: category['color'],
                onTap: () => _navigateToAzkarList(category),
              );
            },
          ),
          
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  Widget _buildQuickAccessSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Access',
          style: AppTheme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        SizedBox(
          height: 120,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: [
              _buildQuickAccessCard(
                'Dua for entering the mosque',
                'assets/images/mosque_enter.png',
                () {},
              ),
              _buildQuickAccessCard(
                'Dua for leaving the mosque',
                'assets/images/mosque_leave.png',
                () {},
              ),
              _buildQuickAccessCard(
                'Dua before eating',
                'assets/images/eating.png',
                () {},
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuickAccessCard(String title, String imagePath, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 140,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: const LinearGradient(
            colors: [Color(0xFF4CAF50), Color(0xFF8BC34A)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Icon(
                FontAwesomeIcons.handsPraying,
                color: Colors.white,
                size: 24,
              ),
              const Spacer(),
              Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturedAzkar() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Featured Azkar',
          style: AppTheme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: GradientCard(
                gradient: const LinearGradient(
                  colors: [Color(0xFFFF9800), Color(0xFFFFC107)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                margin: const EdgeInsets.only(right: 6),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(
                      FontAwesomeIcons.sunrise,
                      color: Colors.white,
                      size: 24,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Morning Azkar',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '25 azkar',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
                onTap: () => _navigateToAzkarList(_azkarCategories[0]),
              ),
            ),
            Expanded(
              child: GradientCard(
                gradient: const LinearGradient(
                  colors: [Color(0xFF3F51B5), Color(0xFF5C6BC0)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                margin: const EdgeInsets.only(left: 6),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(
                      FontAwesomeIcons.moon,
                      color: Colors.white,
                      size: 24,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Evening Azkar',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '22 azkar',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
                onTap: () => _navigateToAzkarList(_azkarCategories[1]),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _navigateToDuaList(Map<String, dynamic> category) {
    // Navigate to dua list screen
    print('Navigate to dua category: ${category['name']}');
  }

  void _navigateToAzkarList(Map<String, dynamic> category) {
    // Navigate to azkar list screen
    print('Navigate to azkar category: ${category['name']}');
  }
}
