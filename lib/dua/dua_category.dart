import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import 'package:new_islamic_app_uk/dua/azkar_list.dart';
import 'package:new_islamic_app_uk/dua/dua_detail.dart';
import 'package:new_islamic_app_uk/dua/dua_list.dart';
import 'package:new_islamic_app_uk/dua/dua_search.dart';
import 'package:new_islamic_app_uk/dua/favorite_duas.dart';
import 'package:new_islamic_app_uk/dua/services/dua_service.dart';
import 'package:share_plus/share_plus.dart';
import 'package:new_islamic_app_uk/theme/app_theme.dart';
import 'package:new_islamic_app_uk/widgets/common/modern_app_bar.dart';
import 'package:new_islamic_app_uk/widgets/common/modern_card.dart';

class duaCategory extends StatefulWidget {
  const duaCategory({Key? key}) : super(key: key);

  @override
  State<duaCategory> createState() => _duaCategoryState();
}

class _duaCategoryState extends State<duaCategory> with SingleTickerProviderStateMixin {
  final DuaService _duaService = DuaService();
  late TabController _tabController;
  bool _isLoading = true;
  List<Map<String, dynamic>> _duaCategories = [];
  List<Map<String, dynamic>> _azkarCategories = [];
  int _recentlyViewedCount = 0;
  int _favoritesCount = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load dua categories
      _duaCategories = await _duaService.getDuaCategories();

      // Load azkar categories
      _azkarCategories = await _duaService.getAzkarCategories();

      // Get counts for badges
      final recentlyViewed = await _duaService.getRecentlyViewedDuas();
      final favorites = await _duaService.getFavoriteDuas();

      setState(() {
        _recentlyViewedCount = recentlyViewed.length;
        _favoritesCount = favorites.length;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: ModernSearchAppBar(
        title: 'Duas & Azkar',
        hintText: 'Search duas and azkar...',
        onSearchChanged: (query) {
          // Implement search functionality
        },
        actions: [
          IconButton(
            icon: Icon(
              Icons.favorite_outline,
              color: AppColors.primary,
              size: AppTheme.iconMedium,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => FavoriteDuasScreen(),
                ),
              ).then((_) => _loadData());
            },
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: AppColors.primary,
              ),
            )
          : Column(
              children: [
                // Modern Tab Bar
                Container(
                  margin: const EdgeInsets.all(AppTheme.spacing16),
                  decoration: BoxDecoration(
                    color: AppColors.surfaceVariant,
                    borderRadius: BorderRadius.circular(AppTheme.radiusCircular),
                  ),
                  child: TabBar(
                    controller: _tabController,
                    labelColor: Colors.white,
                    unselectedLabelColor: AppColors.textSecondary,
                    labelStyle: AppTextStyles.labelLarge,
                    unselectedLabelStyle: AppTextStyles.labelLarge,
                    indicator: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppTheme.radiusCircular),
                      color: AppColors.primary,
                    ),
                    indicatorSize: TabBarIndicatorSize.tab,
                    dividerColor: Colors.transparent,
                    tabs: const [
                      Tab(text: 'Duas'),
                      Tab(text: 'Azkar'),
                    ],
                  ),
                ),
                // Tab Content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildDuasTab(),
                      _buildAzkarTab(),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildDuasTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Quick access section
          Row(
            children: [
              Expanded(
                child: QuickAccessCard(
                  title: 'Recently Viewed',
                  subtitle: 'View recent duas',
                  icon: FontAwesomeIcons.clockRotateLeft,
                  color: AppColors.getCategoryColor(0),
                  badge: _recentlyViewedCount,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => DuaListScreen(
                          title: 'Recently Viewed',
                          categoryId: 'recently_viewed',
                          isRecentlyViewed: true,
                        ),
                      ),
                    ).then((_) => _loadData());
                  },
                ),
              ),
              const SizedBox(width: AppTheme.spacing16),
              Expanded(
                child: QuickAccessCard(
                  title: 'Favorites',
                  subtitle: 'Saved duas',
                  icon: FontAwesomeIcons.solidHeart,
                  color: AppColors.getCategoryColor(1),
                  badge: _favoritesCount,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => FavoriteDuasScreen(),
                      ),
                    ).then((_) => _loadData());
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacing32),

          // Categories section
          Text(
            'Categories',
            style: AppTextStyles.headlineMedium,
          ),
          const SizedBox(height: AppTheme.spacing16),

          GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: AppTheme.spacing16,
              mainAxisSpacing: AppTheme.spacing16,
              childAspectRatio: 1.1,
            ),
            itemCount: _duaCategories.length,
            itemBuilder: (context, index) {
              final category = _duaCategories[index];
              return CategoryCard(
                title: category['name'],
                subtitle: '${category['count']} duas',
                icon: _getCategoryIcon(category['id']),
                color: AppColors.getCategoryColor(index + 2),
                count: category['count'],
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => DuaListScreen(
                        title: category['name'],
                        categoryId: category['id'],
                      ),
                    ),
                  ).then((_) => _loadData());
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAzkarTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Daily Azkar section
          Text(
            'Daily Azkar',
            style: AppTextStyles.headlineMedium,
          ),
          const SizedBox(height: AppTheme.spacing16),

          // Morning & Evening Azkar Cards
          ModernCard(
            gradient: LinearGradient(
              colors: [
                AppColors.getCategoryColor(2).withOpacity(0.8),
                AppColors.getCategoryColor(2),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AzkarListScreen(
                    title: 'Morning Azkar',
                    categoryId: 'morning_azkar',
                  ),
                ),
              );
            },
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacing12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                  ),
                  child: const Icon(
                    FontAwesomeIcons.sun,
                    color: Colors.white,
                    size: AppTheme.iconMedium,
                  ),
                ),
                const SizedBox(width: AppTheme.spacing16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Morning Azkar',
                        style: AppTextStyles.titleLarge.copyWith(color: Colors.white),
                      ),
                      const SizedBox(height: AppTheme.spacing4),
                      Text(
                        'Azkar to recite in the morning',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white,
                  size: AppTheme.iconSmall,
                ),
              ],
            ),
          ),

          const SizedBox(height: AppTheme.spacing16),

          ModernCard(
            gradient: LinearGradient(
              colors: [
                AppColors.getCategoryColor(3).withOpacity(0.8),
                AppColors.getCategoryColor(3),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AzkarListScreen(
                    title: 'Evening Azkar',
                    categoryId: 'evening_azkar',
                  ),
                ),
              );
            },
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacing12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                  ),
                  child: const Icon(
                    FontAwesomeIcons.moon,
                    color: Colors.white,
                    size: AppTheme.iconMedium,
                  ),
                ),
                const SizedBox(width: AppTheme.spacing16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Evening Azkar',
                        style: AppTextStyles.titleLarge.copyWith(color: Colors.white),
                      ),
                      const SizedBox(height: AppTheme.spacing4),
                      Text(
                        'Azkar to recite in the evening',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white,
                  size: AppTheme.iconSmall,
                ),
              ],
            ),
          ),

          const SizedBox(height: AppTheme.spacing32),

          // Other Categories section
          Text(
            'Other Categories',
            style: AppTextStyles.headlineMedium,
          ),
          const SizedBox(height: AppTheme.spacing16),

          ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: _azkarCategories.length,
            itemBuilder: (context, index) {
              final category = _azkarCategories[index];
              // Skip morning and evening azkar as they are already shown above
              if (category['id'] == 'morning_azkar' || category['id'] == 'evening_azkar') {
                return const SizedBox.shrink();
              }
              return FeatureCard(
                title: category['name'],
                description: '${category['count']} azkar',
                icon: _getAzkarCategoryIcon(category['name']),
                color: AppColors.getCategoryColor(index + 4),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AzkarListScreen(
                        title: category['name'],
                        categoryId: category['id'],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }




  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'morning_evening':
        return FontAwesomeIcons.sun;
      case 'prayer':
        return FontAwesomeIcons.personPraying;
      case 'quran':
        return FontAwesomeIcons.bookQuran;
      case 'travel':
        return FontAwesomeIcons.plane;
      case 'food_drink':
        return FontAwesomeIcons.utensils;
      case 'home':
        return FontAwesomeIcons.house;
      case 'protection':
        return FontAwesomeIcons.shieldHalved;
      case 'forgiveness':
        return FontAwesomeIcons.handsPraying;
      case 'hardship':
        return FontAwesomeIcons.heartCrack;
      case 'family':
        return FontAwesomeIcons.peopleGroup;
      default:
        return FontAwesomeIcons.solidStar;
    }
  }

  IconData _getAzkarCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'prayer azkar':
        return FontAwesomeIcons.personPraying;
      case 'sleep azkar':
        return FontAwesomeIcons.bed;
      case 'mosque azkar':
        return FontAwesomeIcons.mosque;
      case 'wudu azkar':
        return FontAwesomeIcons.droplet;
      case 'food azkar':
        return FontAwesomeIcons.utensils;
      case 'travel azkar':
        return FontAwesomeIcons.plane;
      default:
        return FontAwesomeIcons.solidStar;
    }
  }


}
