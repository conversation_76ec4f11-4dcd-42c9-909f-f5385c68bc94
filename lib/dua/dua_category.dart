import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/dua/azkar_list.dart';
import 'package:new_islamic_app_uk/dua/dua_detail.dart';
import 'package:new_islamic_app_uk/dua/dua_list.dart';
import 'package:new_islamic_app_uk/dua/dua_search.dart';
import 'package:new_islamic_app_uk/dua/favorite_duas.dart';
import 'package:new_islamic_app_uk/dua/services/dua_service.dart';
import 'package:share_plus/share_plus.dart';

class duaCategory extends StatefulWidget {
  const duaCategory({Key? key}) : super(key: key);

  @override
  State<duaCategory> createState() => _duaCategoryState();
}

class _duaCategoryState extends State<duaCategory> with SingleTickerProviderStateMixin {
  final DuaService _duaService = DuaService();
  late TabController _tabController;
  bool _isLoading = true;
  List<Map<String, dynamic>> _duaCategories = [];
  List<Map<String, dynamic>> _azkarCategories = [];
  int _recentlyViewedCount = 0;
  int _favoritesCount = 0;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Load dua categories
      _duaCategories = await _duaService.getDuaCategories();
      
      // Load azkar categories
      _azkarCategories = await _duaService.getAzkarCategories();
      
      // Get counts for badges
      final recentlyViewed = await _duaService.getRecentlyViewedDuas();
      final favorites = await _duaService.getFavoriteDuas();
      
      setState(() {
        _recentlyViewedCount = recentlyViewed.length;
        _favoritesCount = favorites.length;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Duas & Azkar',
          style: GoogleFonts.poppins(
            fontSize: 22,
            fontWeight: FontWeight.w600,
            color: Color(URLs().colorScheme),
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Color(URLs().colorScheme)),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.search, color: Color(URLs().colorScheme)),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => DuaSearchScreen(),
                ),
              ).then((_) => _loadData());
            },
          ),
          IconButton(
            icon: Icon(Icons.favorite, color: Color(URLs().colorScheme)),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => FavoriteDuasScreen(),
                ),
              ).then((_) => _loadData());
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Color(URLs().colorScheme),
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: Color(URLs().colorScheme),
          labelStyle: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          tabs: [
            Tab(text: 'Duas'),
            Tab(text: 'Azkar'),
          ],
        ),
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: Color(URLs().colorScheme),
              ),
            )
          : TabBarView(
              controller: _tabController,
              children: [
                _buildDuasTab(),
                _buildAzkarTab(),
              ],
            ),
    );
  }

  Widget _buildDuasTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Quick access section
          Container(
            margin: EdgeInsets.only(bottom: 24),
            child: Row(
              children: [
                Expanded(
                  child: _buildQuickAccessCard(
                    title: 'Recently Viewed',
                    icon: FontAwesomeIcons.clockRotateLeft,
                    color: Colors.blue,
                    count: _recentlyViewedCount,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => DuaListScreen(
                            title: 'Recently Viewed',
                            categoryId: 'recently_viewed',
                            isRecentlyViewed: true,
                          ),
                        ),
                      ).then((_) => _loadData());
                    },
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: _buildQuickAccessCard(
                    title: 'Favorites',
                    icon: FontAwesomeIcons.solidHeart,
                    color: Colors.red,
                    count: _favoritesCount,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => FavoriteDuasScreen(),
                        ),
                      ).then((_) => _loadData());
                    },
                  ),
                ),
              ],
            ),
          ),
          
          // Categories section
          Text(
            'Categories',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 16),
          GridView.builder(
            physics: NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.1,
            ),
            itemCount: _duaCategories.length,
            itemBuilder: (context, index) {
              final category = _duaCategories[index];
              return _buildCategoryCard(
                title: category['name'],
                icon: _getCategoryIcon(category['id']),
                color: _getCategoryColor(index),
                count: category['count'],
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => DuaListScreen(
                        title: category['name'],
                        categoryId: category['id'],
                      ),
                    ),
                  ).then((_) => _loadData());
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAzkarTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Daily Azkar section
          Text(
            'Daily Azkar',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                colors: [
                  Colors.orange.shade300,
                  Colors.orange.shade600,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.orange.withOpacity(0.3),
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AzkarListScreen(
                        title: 'Morning Azkar',
                        categoryId: 'morning_azkar',
                      ),
                    ),
                  );
                },
                borderRadius: BorderRadius.circular(20),
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          FontAwesomeIcons.sun,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Morning Azkar',
                              style: GoogleFonts.poppins(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'Azkar to recite in the morning',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: Colors.white.withOpacity(0.9),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.white,
                        size: 16,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                colors: [
                  Colors.indigo.shade300,
                  Colors.indigo.shade600,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.indigo.withOpacity(0.3),
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AzkarListScreen(
                        title: 'Evening Azkar',
                        categoryId: 'evening_azkar',
                      ),
                    ),
                  );
                },
                borderRadius: BorderRadius.circular(20),
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          FontAwesomeIcons.moon,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Evening Azkar',
                              style: GoogleFonts.poppins(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'Azkar to recite in the evening',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: Colors.white.withOpacity(0.9),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.white,
                        size: 16,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 32),
          
          // Categories section
          Text(
            'Other Categories',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 16),
          ListView.builder(
            physics: NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: _azkarCategories.length,
            itemBuilder: (context, index) {
              final category = _azkarCategories[index];
              // Skip morning and evening azkar as they are already shown above
              if (category['id'] == 'morning_azkar' || category['id'] == 'evening_azkar') {
                return SizedBox.shrink();
              }
              return _buildAzkarCategoryItem(
                title: category['name'],
                icon: _getAzkarCategoryIcon(category['name']),
                color: _getCategoryColor(index),
                count: category['count'],
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AzkarListScreen(
                        title: category['name'],
                        categoryId: category['id'],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAccessCard({
    required String title,
    required IconData icon,
    required Color color,
    required int count,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: [
            color.withOpacity(0.7),
            color,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        icon,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        count.toString(),
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'View all',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryCard({
    required String title,
    required IconData icon,
    required Color color,
    required int count,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                SizedBox(height: 16),
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[800],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4),
                Text(
                  '$count duas',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAzkarCategoryItem({
    required String title,
    required IconData icon,
    required Color color,
    required int count,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[800],
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '$count azkar',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'View',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: color,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'morning_evening':
        return FontAwesomeIcons.sun;
      case 'prayer':
        return FontAwesomeIcons.personPraying;
      case 'quran':
        return FontAwesomeIcons.bookQuran;
      case 'travel':
        return FontAwesomeIcons.plane;
      case 'food_drink':
        return FontAwesomeIcons.utensils;
      case 'home':
        return FontAwesomeIcons.house;
      case 'protection':
        return FontAwesomeIcons.shieldHalved;
      case 'forgiveness':
        return FontAwesomeIcons.handsPraying;
      case 'hardship':
        return FontAwesomeIcons.heartCrack;
      case 'family':
        return FontAwesomeIcons.peopleGroup;
      default:
        return FontAwesomeIcons.solidStar;
    }
  }

  IconData _getAzkarCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'prayer azkar':
        return FontAwesomeIcons.personPraying;
      case 'sleep azkar':
        return FontAwesomeIcons.bed;
      case 'mosque azkar':
        return FontAwesomeIcons.mosque;
      case 'wudu azkar':
        return FontAwesomeIcons.droplet;
      case 'food azkar':
        return FontAwesomeIcons.utensils;
      case 'travel azkar':
        return FontAwesomeIcons.plane;
      default:
        return FontAwesomeIcons.solidStar;
    }
  }

  Color _getCategoryColor(int index) {
    List<Color> colors = [
      Color(0xFF5E72E4), // Indigo
      Color(0xFF11CDEF), // Cyan
      Color(0xFF2DCE89), // Green
      Color(0xFFFB6340), // Orange
      Color(0xFFF5365C), // Pink
      Color(0xFF8965E0), // Purple
      Color(0xFFFFD600), // Yellow
      Color(0xFF5603AD), // Deep Purple
      Color(0xFF8898AA), // Gray Blue
      Color(0xFF1A174D), // Dark Blue
      Color(0xFF5E72E4), // Indigo
      Color(0xFF11CDEF), // Cyan
    ];
    
    return colors[index % colors.length];
  }
}
