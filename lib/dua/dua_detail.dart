import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/dua/services/dua_service.dart';
import 'package:share_plus/share_plus.dart';

class DuaDetailScreen extends StatefulWidget {
  final Map<String, dynamic> dua;
  
  const DuaDetailScreen({
    Key? key,
    required this.dua,
  }) : super(key: key);

  @override
  State<DuaDetailScreen> createState() => _DuaDetailScreenState();
}

class _DuaDetailScreenState extends State<DuaDetailScreen> {
  final DuaService _duaService = DuaService();
  bool _isFavorite = false;
  
  @override
  void initState() {
    super.initState();
    _checkFavoriteStatus();
  }
  
  Future<void> _checkFavoriteStatus() async {
    final isFavorite = await _duaService.isDuaFavorite(widget.dua['id']);
    setState(() {
      _isFavorite = isFavorite;
    });
  }
  
  Future<void> _toggleFavorite() async {
    await _duaService.toggleFavoriteDua(widget.dua['id']);
    setState(() {
      _isFavorite = !_isFavorite;
    });
    
    // Show a snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_isFavorite ? 'Added to favorites' : 'Removed from favorites'),
        backgroundColor: _isFavorite ? Colors.green : Colors.grey[700],
        duration: Duration(seconds: 2),
      ),
    );
  }
  
  void _copyDua() {
    final String textToCopy = 
        '${widget.dua['arabic']}\n\n'
        '${widget.dua['transliteration']}\n\n'
        '${widget.dua['translation']}\n\n'
        'Reference: ${widget.dua['reference']}';
    
    Clipboard.setData(ClipboardData(text: textToCopy));
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Dua copied to clipboard'),
        backgroundColor: Color(URLs().colorScheme),
        duration: Duration(seconds: 2),
      ),
    );
  }
  
  void _shareDua() {
    final String textToShare = 
        '${widget.dua['arabic']}\n\n'
        '${widget.dua['transliteration']}\n\n'
        '${widget.dua['translation']}\n\n'
        'Reference: ${widget.dua['reference']}\n\n'
        'Shared from Masjid Sunnah App';
    
    Share.share(textToShare);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Dua Details',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Color(URLs().colorScheme),
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Color(URLs().colorScheme)),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _isFavorite ? Icons.favorite : Icons.favorite_border,
              color: _isFavorite ? Colors.red : Color(URLs().colorScheme),
            ),
            onPressed: _toggleFavorite,
            tooltip: _isFavorite ? 'Remove from favorites' : 'Add to favorites',
          ),
          IconButton(
            icon: Icon(Icons.content_copy, color: Color(URLs().colorScheme)),
            onPressed: _copyDua,
            tooltip: 'Copy dua',
          ),
          IconButton(
            icon: Icon(Icons.share, color: Color(URLs().colorScheme)),
            onPressed: _shareDua,
            tooltip: 'Share dua',
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Arabic text section with decorative background
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xFF5E72E4).withOpacity(0.9),
                    Color(0xFF11CDEF).withOpacity(0.9),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.3),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // Decorative pattern
                  Positioned.fill(
                    child: Opacity(
                      opacity: 0.05,
                      child: Image.asset(
                        'assets/pattern.jpg',
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  // Content
                  Padding(
                    padding: EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          widget.dua['arabic'],
                          textAlign: TextAlign.right,
                          style: GoogleFonts.scheherazadeNew(
                            fontSize: 32,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                            height: 1.6,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            // Content section
            Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Transliteration section
                  _buildSection(
                    title: 'Transliteration',
                    icon: FontAwesomeIcons.language,
                    content: widget.dua['transliteration'],
                    isItalic: true,
                  ),
                  SizedBox(height: 24),
                  
                  // Translation section
                  _buildSection(
                    title: 'Translation',
                    icon: FontAwesomeIcons.language,
                    content: widget.dua['translation'],
                  ),
                  SizedBox(height: 24),
                  
                  // Reference section
                  _buildSection(
                    title: 'Reference',
                    icon: FontAwesomeIcons.book,
                    content: widget.dua['reference'],
                    isSmaller: true,
                  ),
                  SizedBox(height: 32),
                  
                  // Action buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildActionButton(
                        icon: _isFavorite ? Icons.favorite : Icons.favorite_border,
                        label: _isFavorite ? 'Favorited' : 'Favorite',
                        color: _isFavorite ? Colors.red : Color(0xFF5E72E4),
                        onTap: _toggleFavorite,
                      ),
                      _buildActionButton(
                        icon: Icons.content_copy,
                        label: 'Copy',
                        color: Color(0xFF2DCE89),
                        onTap: _copyDua,
                      ),
                      _buildActionButton(
                        icon: Icons.share,
                        label: 'Share',
                        color: Color(0xFFFB6340),
                        onTap: _shareDua,
                      ),
                    ],
                  ),
                  SizedBox(height: 24),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSection({
    required String title,
    required IconData icon,
    required String content,
    bool isItalic = false,
    bool isSmaller = false,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 18,
                color: Color(URLs().colorScheme),
              ),
              SizedBox(width: 8),
              Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(URLs().colorScheme),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Text(
            content,
            style: GoogleFonts.poppins(
              fontSize: isSmaller ? 14 : 16,
              fontStyle: isItalic ? FontStyle.italic : FontStyle.normal,
              color: Colors.grey[800],
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: [
            color.withOpacity(0.8),
            color,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Column(
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: 24,
                ),
                SizedBox(height: 8),
                Text(
                  label,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 