import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/dua/dua_detail.dart';
import 'package:new_islamic_app_uk/dua/services/dua_service.dart';
import 'package:share_plus/share_plus.dart';

class DuaListScreen extends StatefulWidget {
  final String title;
  final String categoryId;
  final bool isRecentlyViewed;
  
  const DuaListScreen({
    Key? key,
    required this.title,
    required this.categoryId,
    this.isRecentlyViewed = false,
  }) : super(key: key);

  @override
  State<DuaListScreen> createState() => _DuaListScreenState();
}

class _DuaListScreenState extends State<DuaListScreen> {
  final DuaService _duaService = DuaService();
  bool _isLoading = true;
  List<Map<String, dynamic>> _duas = [];
  Map<String, bool> _favoriteStatus = {};
  
  @override
  void initState() {
    super.initState();
    _loadDuas();
  }
  
  Future<void> _loadDuas() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      List<Map<String, dynamic>> duas;
      
      if (widget.isRecentlyViewed) {
        duas = await _duaService.getRecentlyViewedDuas();
      } else {
        duas = await _duaService.getDuasByCategory(widget.categoryId);
      }
      
      // Initialize favorite status for each dua
      Map<String, bool> favoriteStatus = {};
      for (var dua in duas) {
        favoriteStatus[dua['id']] = await _duaService.isDuaFavorite(dua['id']);
      }
      
      setState(() {
        _duas = duas;
        _favoriteStatus = favoriteStatus;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading duas: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  Future<void> _toggleFavorite(String duaId) async {
    final newStatus = await _duaService.toggleFavoriteDua(duaId);
    setState(() {
      _favoriteStatus[duaId] = newStatus;
    });
  }
  
  void _copyDua(Map<String, dynamic> dua) {
    final String textToCopy = 
        '${dua['arabic']}\n\n'
        '${dua['transliteration']}\n\n'
        '${dua['translation']}\n\n'
        'Reference: ${dua['reference']}';
    
    Clipboard.setData(ClipboardData(text: textToCopy));
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Dua copied to clipboard'),
        backgroundColor: Color(URLs().colorScheme),
        duration: Duration(seconds: 2),
      ),
    );
  }
  
  void _shareDua(Map<String, dynamic> dua) {
    final String textToShare = 
        '${dua['arabic']}\n\n'
        '${dua['transliteration']}\n\n'
        '${dua['translation']}\n\n'
        'Reference: ${dua['reference']}\n\n'
        'Shared from Masjid Sunnah App';
    
    Share.share(textToShare);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          widget.title,
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Color(URLs().colorScheme),
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Color(URLs().colorScheme)),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: Color(URLs().colorScheme),
              ),
            )
          : _duas.isEmpty
              ? _buildEmptyState()
              : ListView.builder(
                  padding: EdgeInsets.all(16),
                  itemCount: _duas.length,
                  itemBuilder: (context, index) {
                    final dua = _duas[index];
                    final isFavorite = _favoriteStatus[dua['id']] ?? false;
                    return _buildDuaCard(dua, isFavorite, index);
                  },
                ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            widget.isRecentlyViewed ? FontAwesomeIcons.clockRotateLeft : FontAwesomeIcons.bookOpen,
            size: 60,
            color: Colors.grey[400],
          ),
          SizedBox(height: 24),
          Text(
            widget.isRecentlyViewed ? "No Recently Viewed Duas" : "No Duas Found",
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 8),
          Text(
            widget.isRecentlyViewed
                ? "Duas you view will appear here"
                : "There are no duas in this category",
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: Icon(Icons.arrow_back),
            label: Text('Go Back'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(URLs().colorScheme),
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDuaCard(Map<String, dynamic> dua, bool isFavorite, int index) {
    // Create a gradient color based on index
    List<Color> gradientColors = [
      Color(0xFFF5F7FA), // Light gray
      Colors.white,
    ];
    
    if (index % 3 == 0) {
      gradientColors = [
        Color(0xFFF1F9FF), // Very light blue
        Colors.white,
      ];
    } else if (index % 3 == 1) {
      gradientColors = [
        Color(0xFFFFF8F1), // Very light orange
        Colors.white,
      ];
    }
    
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: gradientColors,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            _duaService.addToRecentlyViewed(dua['id']);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => DuaDetailScreen(dua: dua),
              ),
            ).then((_) => _loadDuas());
          },
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Arabic text
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.05),
                        blurRadius: 5,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    dua['arabic'],
                    textAlign: TextAlign.right,
                    style: GoogleFonts.scheherazadeNew(
                      fontSize: 26,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                      height: 1.6,
                    ),
                  ),
                ),
                SizedBox(height: 16),
                
                // Transliteration
                Text(
                  dua['transliteration'],
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontStyle: FontStyle.italic,
                    color: Colors.grey[700],
                  ),
                ),
                SizedBox(height: 16),
                
                // Translation
                Text(
                  dua['translation'],
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[800],
                    height: 1.5,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 16),
                
                // Reference
                Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.book,
                      size: 14,
                      color: Color(URLs().colorScheme),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        dua['reference'],
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                
                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    _buildActionButton(
                      icon: Icons.content_copy,
                      color: Colors.blue,
                      onTap: () => _copyDua(dua),
                      tooltip: 'Copy dua',
                    ),
                    SizedBox(width: 8),
                    _buildActionButton(
                      icon: Icons.share,
                      color: Colors.green,
                      onTap: () => _shareDua(dua),
                      tooltip: 'Share dua',
                    ),
                    SizedBox(width: 8),
                    _buildActionButton(
                      icon: isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: Colors.red,
                      onTap: () => _toggleFavorite(dua['id']),
                      tooltip: isFavorite ? 'Remove from favorites' : 'Add to favorites',
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required String tooltip,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: IconButton(
        onPressed: onTap,
        icon: Icon(
          icon,
          color: color,
          size: 20,
        ),
        tooltip: tooltip,
        constraints: BoxConstraints(
          minWidth: 40,
          minHeight: 40,
        ),
        padding: EdgeInsets.zero,
      ),
    );
  }
} 