import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/dua/services/dua_service.dart';
import 'package:share_plus/share_plus.dart';
import 'package:percent_indicator/percent_indicator.dart';

class AzkarListScreen extends StatefulWidget {
  final String title;
  final String categoryId;
  
  const AzkarListScreen({
    Key? key,
    required this.title,
    required this.categoryId,
  }) : super(key: key);

  @override
  State<AzkarListScreen> createState() => _AzkarListScreenState();
}

class _AzkarListScreenState extends State<AzkarListScreen> with SingleTickerProviderStateMixin {
  final DuaService _duaService = DuaService();
  bool _isLoading = true;
  List<Map<String, dynamic>> _azkar = [];
  Map<String, int> _counters = {};
  late AnimationController _animationController;
  Map<String, Animation<double>> _animations = {};
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 500),
    );
    _loadAzkar();
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  Future<void> _loadAzkar() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final azkar = await _duaService.getAzkarByCategory(widget.categoryId);
      
      // Initialize counters for each zikr
      final Map<String, int> counters = {};
      final Map<String, Animation<double>> animations = {};
      
      for (var zikr in azkar) {
        counters[zikr['id']] = 0;
        animations[zikr['id']] = Tween<double>(begin: 0.0, end: 0.0).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeInOut,
          ),
        );
      }
      
      setState(() {
        _azkar = azkar;
        _counters = counters;
        _animations = animations;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading azkar: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  void _incrementCounter(String zikrId, int maxRepeat) {
    if (_counters[zikrId]! < maxRepeat) {
      // Create a new animation for the progress
      final double oldProgress = _counters[zikrId]! / maxRepeat;
      final double newProgress = (_counters[zikrId]! + 1) / maxRepeat;
      
      _animations[zikrId] = Tween<double>(begin: oldProgress, end: newProgress).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Curves.easeInOut,
        ),
      );
      
      // Reset the animation controller and start it
      _animationController.reset();
      _animationController.forward();
      
      // Update the counter
      setState(() {
        _counters[zikrId] = _counters[zikrId]! + 1;
      });
      
      // Provide haptic feedback
      HapticFeedback.lightImpact();
      
      // If completed, show a snackbar
      if (_counters[zikrId] == maxRepeat) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Completed! 🎉'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }
  
  void _resetCounter(String zikrId, int maxRepeat) {
    // Create a new animation for the progress
    final double oldProgress = _counters[zikrId]! / maxRepeat;
    
    _animations[zikrId] = Tween<double>(begin: oldProgress, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    
    // Reset the animation controller and start it
    _animationController.reset();
    _animationController.forward();
    
    setState(() {
      _counters[zikrId] = 0;
    });
    
    // Provide haptic feedback
    HapticFeedback.mediumImpact();
  }
  
  void _copyZikr(Map<String, dynamic> zikr) {
    final String textToCopy = 
        '${zikr['arabic']}\n\n'
        '${zikr['transliteration']}\n\n'
        '${zikr['translation']}\n\n'
        'Reference: ${zikr['reference']}\n'
        'Repeat: ${zikr['repeat']} times';
    
    Clipboard.setData(ClipboardData(text: textToCopy));
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Zikr copied to clipboard'),
        backgroundColor: Color(URLs().colorScheme),
        duration: Duration(seconds: 2),
      ),
    );
  }
  
  void _shareZikr(Map<String, dynamic> zikr) {
    final String textToShare = 
        '${zikr['arabic']}\n\n'
        '${zikr['transliteration']}\n\n'
        '${zikr['translation']}\n\n'
        'Reference: ${zikr['reference']}\n'
        'Repeat: ${zikr['repeat']} times\n\n'
        'Shared from Masjid Sunnah App';
    
    Share.share(textToShare);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          widget.title,
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Color(URLs().colorScheme),
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Color(URLs().colorScheme)),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: Color(URLs().colorScheme),
              ),
            )
          : _azkar.isEmpty
              ? _buildEmptyState()
              : ListView.builder(
                  padding: EdgeInsets.all(16),
                  itemCount: _azkar.length,
                  itemBuilder: (context, index) {
                    final zikr = _azkar[index];
                    return _buildZikrCard(zikr, index);
                  },
                ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FontAwesomeIcons.prayingHands,
            size: 60,
            color: Colors.grey[400],
          ),
          SizedBox(height: 24),
          Text(
            "No Azkar Found",
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 8),
          Text(
            "There are no azkar in this category",
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: Icon(Icons.arrow_back),
            label: Text('Go Back'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(URLs().colorScheme),
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildZikrCard(Map<String, dynamic> zikr, int index) {
    final int maxRepeat = zikr['repeat'];
    final int currentCount = _counters[zikr['id']] ?? 0;
    final double progress = maxRepeat > 0 ? currentCount / maxRepeat : 0;
    
    // Create a gradient color based on index
    List<Color> gradientColors = [
      Color(0xFFF5F7FA), // Light gray
      Colors.white,
    ];
    
    if (index % 3 == 0) {
      gradientColors = [
        Color(0xFFF1F9FF), // Very light blue
        Colors.white,
      ];
    } else if (index % 3 == 1) {
      gradientColors = [
        Color(0xFFFFF8F1), // Very light orange
        Colors.white,
      ];
    }
    
    // Determine the progress color
    Color progressColor;
    if (progress < 0.3) {
      progressColor = Color(0xFF5E72E4); // Indigo
    } else if (progress < 0.7) {
      progressColor = Color(0xFF2DCE89); // Green
    } else {
      progressColor = Color(0xFFFB6340); // Orange
    }
    
    if (progress == 1.0) {
      progressColor = Color(0xFF11CDEF); // Cyan
    }
    
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: gradientColors,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Arabic text
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.05),
                    blurRadius: 5,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                zikr['arabic'],
                textAlign: TextAlign.right,
                style: GoogleFonts.scheherazadeNew(
                  fontSize: 26,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                  height: 1.6,
                ),
              ),
            ),
            SizedBox(height: 16),
            
            // Transliteration
            Text(
              zikr['transliteration'],
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontStyle: FontStyle.italic,
                color: Colors.grey[700],
              ),
            ),
            SizedBox(height: 16),
            
            // Translation
            Text(
              zikr['translation'],
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[800],
                height: 1.5,
              ),
            ),
            SizedBox(height: 16),
            
            // Reference and repeat count
            Row(
              children: [
                Icon(
                  FontAwesomeIcons.book,
                  size: 14,
                  color: Color(URLs().colorScheme),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    zikr['reference'],
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: progressColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Repeat: ${zikr['repeat']} times',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: progressColor,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 24),
            
            // Progress indicator
            AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                final double animatedProgress = _animations[zikr['id']]?.value ?? progress;
                
                return Column(
                  children: [
                    LinearPercentIndicator(
                      lineHeight: 10.0,
                      percent: animatedProgress,
                      backgroundColor: Colors.grey[200],
                      progressColor: progressColor,
                      barRadius: Radius.circular(5),
                      animation: false,
                      padding: EdgeInsets.zero,
                    ),
                    SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '$currentCount / $maxRepeat',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: progressColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              },
            ),
            SizedBox(height: 24),
            
            // Counter buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Reset button
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => _resetCounter(zikr['id'], maxRepeat),
                      borderRadius: BorderRadius.circular(30),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        child: Row(
                          children: [
                            Icon(
                              Icons.refresh,
                              color: Colors.grey[800],
                              size: 18,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Reset',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey[800],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 16),
                
                // Count button
                Expanded(
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: currentCount < maxRepeat
                            ? [progressColor.withOpacity(0.8), progressColor]
                            : [Colors.grey.shade300, Colors.grey.shade400],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: currentCount < maxRepeat
                          ? [
                              BoxShadow(
                                color: progressColor.withOpacity(0.3),
                                blurRadius: 10,
                                offset: Offset(0, 4),
                              ),
                            ]
                          : [],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: currentCount < maxRepeat
                            ? () => _incrementCounter(zikr['id'], maxRepeat)
                            : null,
                        borderRadius: BorderRadius.circular(30),
                        child: Center(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                currentCount < maxRepeat ? Icons.add : Icons.check,
                                color: Colors.white,
                                size: 20,
                              ),
                              SizedBox(width: 8),
                              Text(
                                currentCount < maxRepeat ? 'Count' : 'Completed',
                                style: GoogleFonts.poppins(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                _buildActionButton(
                  icon: Icons.content_copy,
                  color: Colors.blue,
                  onTap: () => _copyZikr(zikr),
                  tooltip: 'Copy zikr',
                ),
                SizedBox(width: 8),
                _buildActionButton(
                  icon: Icons.share,
                  color: Colors.green,
                  onTap: () => _shareZikr(zikr),
                  tooltip: 'Share zikr',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required String tooltip,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: IconButton(
        onPressed: onTap,
        icon: Icon(
          icon,
          color: color,
          size: 20,
        ),
        tooltip: tooltip,
        constraints: BoxConstraints(
          minWidth: 40,
          minHeight: 40,
        ),
        padding: EdgeInsets.zero,
      ),
    );
  }
} 