import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import 'package:new_islamic_app_uk/dua/dua_detail.dart';
import 'package:new_islamic_app_uk/dua/services/dua_service.dart';
import 'package:share_plus/share_plus.dart';

class FavoriteDuasScreen extends StatefulWidget {
  const FavoriteDuasScreen({Key? key}) : super(key: key);

  @override
  State<FavoriteDuasScreen> createState() => _FavoriteDuasScreenState();
}

class _FavoriteDuasScreenState extends State<FavoriteDuasScreen> {
  final DuaService _duaService = DuaService();
  bool _isLoading = true;
  List<Map<String, dynamic>> _favoriteDuas = [];

  @override
  void initState() {
    super.initState();
    _loadFavoriteDuas();
  }

  Future<void> _loadFavoriteDuas() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final favoriteDuas = await _duaService.getFavoriteDuas();

      setState(() {
        _favoriteDuas = favoriteDuas;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading favorite duas: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleFavorite(String duaId) async {
    await _duaService.toggleFavoriteDua(duaId);
    _loadFavoriteDuas();
  }

  void _copyDua(Map<String, dynamic> dua) {
    final String textToCopy =
        '${dua['arabic']}\n\n'
        '${dua['transliteration']}\n\n'
        '${dua['translation']}\n\n'
        'Reference: ${dua['reference']}';

    Clipboard.setData(ClipboardData(text: textToCopy));

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Dua copied to clipboard'),
        backgroundColor: AppColors.primary,
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _shareDua(Map<String, dynamic> dua) {
    final String textToShare =
        '${dua['arabic']}\n\n'
        '${dua['transliteration']}\n\n'
        '${dua['translation']}\n\n'
        'Reference: ${dua['reference']}\n\n'
        'Shared from Masjid Sunnah App';

    Share.share(textToShare);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Favorite Duas',
          style: AppTextStyles.headlineMedium.copyWith(
            color: AppColors.primary,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.primary),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh, color: AppColors.primary),
            onPressed: _loadFavoriteDuas,
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: AppColors.primary,
              ),
            )
          : _favoriteDuas.isEmpty
              ? _buildEmptyState()
              : ListView.builder(
                  padding: EdgeInsets.all(16),
                  itemCount: _favoriteDuas.length,
                  itemBuilder: (context, index) {
                    final dua = _favoriteDuas[index];
                    return _buildDuaCard(dua);
                  },
                ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FontAwesomeIcons.heart,
            size: 60,
            color: Colors.grey[400],
          ),
          SizedBox(height: 24),
          Text(
            "No Favorite Duas",
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            "Duas you favorite will appear here",
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: Icon(Icons.arrow_back),
            label: Text('Browse Duas'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDuaCard(Map<String, dynamic> dua) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 2,
      child: InkWell(
        onTap: () {
          _duaService.addToRecentlyViewed(dua['id']);
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => DuaDetailScreen(dua: dua),
            ),
          ).then((_) => _loadFavoriteDuas());
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Arabic text
              Text(
                dua['arabic'],
                textAlign: TextAlign.right,
                style: AppTextStyles.displaySmall.copyWith(
                  fontFamily: 'Scheherazade New',
                  color: AppColors.textPrimary,
                  height: 1.6,
                ),
              ),
              SizedBox(height: 16),

              // Transliteration
              Text(
                dua['transliteration'],
                style: AppTextStyles.bodyMedium.copyWith(
                  fontStyle: FontStyle.italic,
                  color: AppColors.textSecondary,
                ),
              ),
              SizedBox(height: 16),

              // Translation
              Text(
                dua['translation'],
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textPrimary,
                  height: 1.5,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 16),

              // Reference
              Row(
                children: [
                  Icon(
                    FontAwesomeIcons.book,
                    size: 14,
                    color: AppColors.primary,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      dua['reference'],
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textTertiary,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    onPressed: () => _copyDua(dua),
                    icon: Icon(
                      Icons.content_copy,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                    tooltip: 'Copy dua',
                  ),
                  IconButton(
                    onPressed: () => _shareDua(dua),
                    icon: Icon(
                      Icons.share,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                    tooltip: 'Share dua',
                  ),
                  IconButton(
                    onPressed: () => _toggleFavorite(dua['id']),
                    icon: Icon(
                      Icons.favorite,
                      color: Colors.red,
                      size: 20,
                    ),
                    tooltip: 'Remove from favorites',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}