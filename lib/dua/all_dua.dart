import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/models/dua.dart';

import 'package:shimmer/shimmer.dart';

class allDuaPage extends StatefulWidget {
  var id;
  var name;
  var image;

  allDuaPage({Key? key, this.id, this.name, this.image}) : super(key: key);

  @override
  State<allDuaPage> createState() => _allDuaPageState();
}

class _allDuaPageState extends State<allDuaPage> {
  @override
  Widget build(BuildContext context) {
    var _crossAxisSpacing = 8;
    var _screenWidth = MediaQuery.of(context).size.width;
    var _crossAxisCount = 1;
    var _width = (_screenWidth - ((_crossAxisCount - 1) * _crossAxisSpacing)) /
        _crossAxisCount;
    var cellHeight = 190;
    var _aspectRatio = _width / cellHeight;


      var _crossAxisSpacingList = 8;
    var _screenWidthList = MediaQuery.of(context).size.width;
    var _crossAxisCountList = 1;
    var _widthList = (_screenWidthList -
            ((_crossAxisCountList - 1) * _crossAxisSpacingList)) /
        _crossAxisCountList;
    var cellHeightList = 90;
    var _aspectRatioList = _widthList / cellHeightList;


    // TODO: implement build
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        title: Text(""),
        leading: InkWell(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                margin: EdgeInsets.only(left:10, top: 5, bottom: 5),
                padding: EdgeInsets.only(left:8),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12)
                ),
                child: Icon(Icons.arrow_back_ios, color: Colors.white,),
              ),
            ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        decoration: BoxDecoration(
          image: DecorationImage(image: AssetImage("assets/bg.jpeg"), fit: BoxFit.cover)
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Container(
              //   width: MediaQuery.of(context).size.width,
              //   // color: Color(0xFF9538FF),
              //   child: Padding(
              //     padding: const EdgeInsets.all(28.0),
              //     child: Row(
              //       mainAxisAlignment: MainAxisAlignment.start,
              //       crossAxisAlignment: CrossAxisAlignment.start,
              //       children: [
              //         Container(
              //           child: Container(
              //             // height: 60,
              //             padding: EdgeInsets.only(right: 10),
              //             width: 70,
        
              //             child:
              //                 Container(child: Image.asset("assets/masjid.png")),
              //           ),
              //         ),
              //         Container(
              //           width: 210,
              //           padding: const EdgeInsets.all(8.0),
              //           child: Column(
              //             crossAxisAlignment: CrossAxisAlignment.start,
              //             children: [
              //               Text("${widget.name}",
              //                   style: GoogleFonts.passionOne(
              //                     fontSize: 22,
              //                     // color: Colors.white,
              //                     fontWeight: FontWeight.bold,
              //                   ),
              //                   textAlign: TextAlign.left),
              //             ],
              //           ),
              //         )
              //       ],
              //     ),
              //   ),
              // ),
        
              Container(
                width: MediaQuery.of(context).size.width,
                height: 240,
                padding: EdgeInsets.all(20),
                // margin: EdgeInsets.symmetric(horizontal: 5.0),
                decoration: BoxDecoration(
                  image: DecorationImage(
                      image: AssetImage("assets/dua_header.png"),
                      fit: BoxFit.cover),
                ),
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 72.0),
                  child: Center(
                    child: Text(
                      '${widget.name}',
                      style: GoogleFonts.openSans(
                        fontSize: 26,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
              
              Padding(
                padding: EdgeInsets.all(6.0),
                child: duas.length >= 1 ? ListView.builder(
                    physics: PageScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: duas.length,
                    itemBuilder: (context, int index) {
                      return Card(
                        elevation: 0,
                        child: Padding(
                          padding: const EdgeInsets.all(10.0),
                          child: ExpansionTile(
                            title: Text(
                              duas[index].title ?? '',
                              style: GoogleFonts.roboto(fontSize: 18),
                            ),
                            children: <Widget>[
                              Padding(
                                padding: const EdgeInsets.all(12.0),
                                child: HtmlWidget(
                                  duas[index].content ?? '',
                                  customStylesBuilder: (e) {
                                    if (e.localName == 'h2') {
                                      return {'text-align': 'right'};
                                    }

                                    if (e.localName == 'p') {
                                      return {
                                        'font-size': '20px',
                                        // 'background': 'rgba(0,255,0,0.1)',
                                        // 'border-radius': '10px',
                                        // 'padding': '15px',
                                        // 'margin-top': '7px'
                                      
                                      };
                                    }
        
                                    return null;
                                  },
                                ),
                              ),
                            ],
                            leading: Container(
                              child: Image.asset(
                                "assets/logo.png",
                                width: 35,
                              ),
                              alignment: Alignment.center,
                              width: 40.0,
                              height: 40.0,
                              decoration: new BoxDecoration(
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        )
                      );
                      
        
                      // return Card(
                      //   elevation: 3,
                      //   child: Padding(
                      //     padding: const EdgeInsets.symmetric(vertical: 10.0),
                      //     child: ListTile(
                      //       leading: Padding(
                      //         padding: const EdgeInsets.all(0),
                      //         child: Container(
                      //           // height: 100,
                      //           width: 50,
                      //           decoration: BoxDecoration(
                      //             borderRadius: BorderRadius.circular(7),
                      //             image: DecorationImage(
                      //               image: ExactAssetImage('assets/message.png'),
                      //               fit: BoxFit.cover,
                      //             ),
                      //           ),
                      //         ),
                      //       ),
                      //       title: Container(
                      //         // height: 80,
                      //         // width: 170,
                      //         child: Column(
                      //           crossAxisAlignment: CrossAxisAlignment.start,
                      //           children: [
                      //             Text("${duas[index].title}",
                      //                 style: GoogleFonts.raleway(
                      //                     fontSize: 16, fontWeight: FontWeight.w700)),
                      //             Padding(
                      //               padding: const EdgeInsets.only(top: 5, right: 0),
                      //               child: Text("${duas[index].content}",
                      //                   style: GoogleFonts.raleway(fontSize: 14)),
                      //             ),
                      //             Padding(
                      //                 padding: const EdgeInsets.only(top: 5, right: 0),
                      //                 child: Align(
                      //                   alignment: Alignment.bottomRight,
                      //                   child: Text(
                      //                     "12 September 2021",
                      //                     style: GoogleFonts.raleway(
                      //                         fontSize: 14, color: Colors.grey),
                      //                   ),
                      //                 )),
                      //           ],
                      //         ),
                      //       ),
                      //     ),
                      //   ),
                      // );
                    })
                    : GridView.count(
                    physics: NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    crossAxisSpacing: 10.0,
                    crossAxisCount: 1,
                    childAspectRatio: _aspectRatioList,
                    children: List.generate(8, (index) {
                      return ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 6, horizontal: 8),
                          child: Container(
                            child: Shimmer.fromColors(
                                baseColor: Colors.grey.withOpacity(0.3),
                                highlightColor: Colors.white,
                                child: Container(
                                  height: 100,
                                  width: double.infinity,
                                  color: Colors.grey.withOpacity(0.3),
                                )),
                          ),
                        ),
                      );
                    }),
                  ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    GetAllDua();
  }

  List<AllduaModel> duas = [];

  GetAllDua() async {
    try {
      var dio = Dio();
      Response response = await dio.get(URLs().WEBURL +
          "/wp-json/wp/v2/posts?categories=${widget.id}&order=asc&per_page=100");
      if (response.statusCode == 200) {
        var parsedData =
            response.data.map((res) => AllduaModel.fromJson(res)).toList();
        parsedData.forEach(
          (f) async {
            duas.add(f);
          },
        );
        setState(() {
           duas;
        });
      }
    } catch (e) {
      print(e);
    }
  }
}
