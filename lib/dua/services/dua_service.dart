import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart' show rootBundle;

class DuaService {
  static final DuaService _instance = DuaService._internal();
  
  factory DuaService() {
    return _instance;
  }
  
  DuaService._internal();
  
  // Keys for SharedPreferences
  final String _duaCategoriesKey = 'dua_categories';
  final String _azkarCategoriesKey = 'azkar_categories';
  final String _duasKey = 'duas';
  final String _azkarKey = 'azkar';
  final String _favoriteDuasKey = 'favorite_duas';
  final String _recentlyViewedDuasKey = 'recently_viewed_duas';
  
  // Get dua categories
  Future<List<Map<String, dynamic>>> getDuaCategories() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? categoriesJson = prefs.getString(_duaCategoriesKey);
      
      if (categoriesJson != null) {
        final List<dynamic> decodedList = json.decode(categoriesJson);
        return List<Map<String, dynamic>>.from(decodedList);
      } else {
        // Load from asset if not in SharedPreferences
        final String jsonData = await rootBundle.loadString('assets/data/dua_categories.json');
        final List<dynamic> decodedList = json.decode(jsonData);
        final categories = List<Map<String, dynamic>>.from(decodedList);
        
        // Save to SharedPreferences for offline use
        await prefs.setString(_duaCategoriesKey, jsonData);
        
        return categories;
      }
    } catch (e) {
      debugPrint('Error getting dua categories: $e');
      
      // Return sample data if everything fails
      return [
        {'id': 'morning_evening', 'name': 'Morning & Evening', 'count': 12},
        {'id': 'prayer', 'name': 'Prayer', 'count': 15},
        {'id': 'quran', 'name': 'Quran', 'count': 8},
        {'id': 'travel', 'name': 'Travel', 'count': 10},
        {'id': 'food_drink', 'name': 'Food & Drink', 'count': 7},
        {'id': 'home', 'name': 'Home', 'count': 6},
        {'id': 'protection', 'name': 'Protection', 'count': 14},
        {'id': 'forgiveness', 'name': 'Forgiveness', 'count': 9},
        {'id': 'hardship', 'name': 'Hardship', 'count': 11},
        {'id': 'family', 'name': 'Family', 'count': 8},
      ];
    }
  }
  
  // Get azkar categories
  Future<List<Map<String, dynamic>>> getAzkarCategories() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? categoriesJson = prefs.getString(_azkarCategoriesKey);
      
      if (categoriesJson != null) {
        final List<dynamic> decodedList = json.decode(categoriesJson);
        return List<Map<String, dynamic>>.from(decodedList);
      } else {
        // Load from asset if not in SharedPreferences
        final String jsonData = await rootBundle.loadString('assets/data/azkar_categories.json');
        final List<dynamic> decodedList = json.decode(jsonData);
        final categories = List<Map<String, dynamic>>.from(decodedList);
        
        // Save to SharedPreferences for offline use
        await prefs.setString(_azkarCategoriesKey, jsonData);
        
        return categories;
      }
    } catch (e) {
      debugPrint('Error getting azkar categories: $e');
      
      // Return sample data if everything fails
      return [
        {'id': 'morning_azkar', 'name': 'Morning Azkar', 'count': 15},
        {'id': 'evening_azkar', 'name': 'Evening Azkar', 'count': 15},
        {'id': 'prayer_azkar', 'name': 'Prayer Azkar', 'count': 12},
        {'id': 'sleep_azkar', 'name': 'Sleep Azkar', 'count': 8},
        {'id': 'mosque_azkar', 'name': 'Mosque Azkar', 'count': 6},
        {'id': 'wudu_azkar', 'name': 'Wudu Azkar', 'count': 5},
        {'id': 'food_azkar', 'name': 'Food Azkar', 'count': 7},
        {'id': 'travel_azkar', 'name': 'Travel Azkar', 'count': 9},
      ];
    }
  }
  
  // Get duas by category
  Future<List<Map<String, dynamic>>> getDuasByCategory(String categoryId) async {
    try {
      if (categoryId == 'recently_viewed') {
        return await getRecentlyViewedDuas();
      }
      
      final prefs = await SharedPreferences.getInstance();
      final String? duasJson = prefs.getString(_duasKey);
      
      if (duasJson != null) {
        final Map<String, dynamic> duasMap = json.decode(duasJson);
        if (duasMap.containsKey(categoryId)) {
          return List<Map<String, dynamic>>.from(duasMap[categoryId]);
        }
      }
      
      // Load from asset if not in SharedPreferences
      final String jsonData = await rootBundle.loadString('assets/data/duas.json');
      final Map<String, dynamic> duasMap = json.decode(jsonData);
      
      // Save to SharedPreferences for offline use
      await prefs.setString(_duasKey, jsonData);
      
      if (duasMap.containsKey(categoryId)) {
        return List<Map<String, dynamic>>.from(duasMap[categoryId]);
      }
      
      return [];
    } catch (e) {
      debugPrint('Error getting duas by category: $e');
      return [];
    }
  }
  
  // Get azkar by category
  Future<List<Map<String, dynamic>>> getAzkarByCategory(String categoryId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? azkarJson = prefs.getString(_azkarKey);
      
      if (azkarJson != null) {
        final Map<String, dynamic> azkarMap = json.decode(azkarJson);
        if (azkarMap.containsKey(categoryId)) {
          return List<Map<String, dynamic>>.from(azkarMap[categoryId]);
        }
      }
      
      // Load from asset if not in SharedPreferences
      final String jsonData = await rootBundle.loadString('assets/data/azkar.json');
      final Map<String, dynamic> azkarMap = json.decode(jsonData);
      
      // Save to SharedPreferences for offline use
      await prefs.setString(_azkarKey, jsonData);
      
      if (azkarMap.containsKey(categoryId)) {
        return List<Map<String, dynamic>>.from(azkarMap[categoryId]);
      }
      
      return [];
    } catch (e) {
      debugPrint('Error getting azkar by category: $e');
      return [];
    }
  }
  
  // Get dua by ID
  Future<Map<String, dynamic>?> getDuaById(String duaId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? duasJson = prefs.getString(_duasKey);
      
      if (duasJson != null) {
        final Map<String, dynamic> duasMap = json.decode(duasJson);
        
        // Search through all categories
        for (final categoryDuas in duasMap.values) {
          for (final dua in categoryDuas) {
            if (dua['id'] == duaId) {
              return Map<String, dynamic>.from(dua);
            }
          }
        }
      }
      
      // If not found in SharedPreferences, load from asset
      final String jsonData = await rootBundle.loadString('assets/data/duas.json');
      final Map<String, dynamic> duasMap = json.decode(jsonData);
      
      // Search through all categories
      for (final categoryDuas in duasMap.values) {
        for (final dua in categoryDuas) {
          if (dua['id'] == duaId) {
            return Map<String, dynamic>.from(dua);
          }
        }
      }
      
      return null;
    } catch (e) {
      debugPrint('Error getting dua by ID: $e');
      return null;
    }
  }
  
  // Get azkar by ID
  Future<Map<String, dynamic>?> getAzkarById(String azkarId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? azkarJson = prefs.getString(_azkarKey);
      
      if (azkarJson != null) {
        final Map<String, dynamic> azkarMap = json.decode(azkarJson);
        
        // Search through all categories
        for (final categoryAzkar in azkarMap.values) {
          for (final azkar in categoryAzkar) {
            if (azkar['id'] == azkarId) {
              return Map<String, dynamic>.from(azkar);
            }
          }
        }
      }
      
      // If not found in SharedPreferences, load from asset
      final String jsonData = await rootBundle.loadString('assets/data/azkar.json');
      final Map<String, dynamic> azkarMap = json.decode(jsonData);
      
      // Search through all categories
      for (final categoryAzkar in azkarMap.values) {
        for (final azkar in categoryAzkar) {
          if (azkar['id'] == azkarId) {
            return Map<String, dynamic>.from(azkar);
          }
        }
      }
      
      return null;
    } catch (e) {
      debugPrint('Error getting azkar by ID: $e');
      return null;
    }
  }
  
  // Get favorite duas
  Future<List<Map<String, dynamic>>> getFavoriteDuas() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String> favoriteIds = prefs.getStringList(_favoriteDuasKey) ?? [];
      
      final List<Map<String, dynamic>> favorites = [];
      
      for (final id in favoriteIds) {
        final dua = await getDuaById(id);
        if (dua != null) {
          favorites.add(dua);
        }
      }
      
      return favorites;
    } catch (e) {
      debugPrint('Error getting favorite duas: $e');
      return [];
    }
  }
  
  // Toggle favorite status of a dua
  Future<bool> toggleFavoriteDua(String duaId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String> favoriteIds = prefs.getStringList(_favoriteDuasKey) ?? [];
      
      if (favoriteIds.contains(duaId)) {
        favoriteIds.remove(duaId);
      } else {
        favoriteIds.add(duaId);
      }
      
      await prefs.setStringList(_favoriteDuasKey, favoriteIds);
      
      return favoriteIds.contains(duaId);
    } catch (e) {
      debugPrint('Error toggling favorite status: $e');
      return false;
    }
  }
  
  // Check if a dua is favorite
  Future<bool> isDuaFavorite(String duaId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String> favoriteIds = prefs.getStringList(_favoriteDuasKey) ?? [];
      
      return favoriteIds.contains(duaId);
    } catch (e) {
      debugPrint('Error checking if dua is favorite: $e');
      return false;
    }
  }
  
  // Add dua to recently viewed
  Future<void> addToRecentlyViewed(String duaId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String> recentIds = prefs.getStringList(_recentlyViewedDuasKey) ?? [];
      
      // Remove if already exists
      recentIds.remove(duaId);
      
      // Add to beginning of list
      recentIds.insert(0, duaId);
      
      // Keep only the most recent 20 duas
      final limitedRecents = recentIds.take(20).toList();
      
      await prefs.setStringList(_recentlyViewedDuasKey, limitedRecents);
    } catch (e) {
      debugPrint('Error adding to recently viewed: $e');
    }
  }
  
  // Get recently viewed duas
  Future<List<Map<String, dynamic>>> getRecentlyViewedDuas() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String> recentIds = prefs.getStringList(_recentlyViewedDuasKey) ?? [];
      
      final List<Map<String, dynamic>> recents = [];
      
      for (final id in recentIds) {
        final dua = await getDuaById(id);
        if (dua != null) {
          recents.add(dua);
        }
      }
      
      return recents;
    } catch (e) {
      debugPrint('Error getting recently viewed duas: $e');
      return [];
    }
  }
  
  // Search duas
  Future<List<Map<String, dynamic>>> searchDuas(String query) async {
    try {
      if (query.isEmpty) {
        return [];
      }
      
      final prefs = await SharedPreferences.getInstance();
      final String? duasJson = prefs.getString(_duasKey);
      
      final List<Map<String, dynamic>> results = [];
      final String lowercaseQuery = query.toLowerCase();
      
      if (duasJson != null) {
        final Map<String, dynamic> duasMap = json.decode(duasJson);
        
        // Search through all categories
        for (final categoryDuas in duasMap.values) {
          for (final dua in categoryDuas) {
            if (dua['arabic'].toLowerCase().contains(lowercaseQuery) ||
                dua['translation'].toLowerCase().contains(lowercaseQuery) ||
                dua['transliteration'].toLowerCase().contains(lowercaseQuery) ||
                dua['reference'].toLowerCase().contains(lowercaseQuery)) {
              results.add(Map<String, dynamic>.from(dua));
            }
          }
        }
      } else {
        // If not in SharedPreferences, load from asset
        final String jsonData = await rootBundle.loadString('assets/data/duas.json');
        final Map<String, dynamic> duasMap = json.decode(jsonData);
        
        // Search through all categories
        for (final categoryDuas in duasMap.values) {
          for (final dua in categoryDuas) {
            if (dua['arabic'].toLowerCase().contains(lowercaseQuery) ||
                dua['translation'].toLowerCase().contains(lowercaseQuery) ||
                dua['transliteration'].toLowerCase().contains(lowercaseQuery) ||
                dua['reference'].toLowerCase().contains(lowercaseQuery)) {
              results.add(Map<String, dynamic>.from(dua));
            }
          }
        }
      }
      
      return results;
    } catch (e) {
      debugPrint('Error searching duas: $e');
      return [];
    }
  }
  
  // Search azkar
  Future<List<Map<String, dynamic>>> searchAzkar(String query) async {
    try {
      if (query.isEmpty) {
        return [];
      }
      
      final prefs = await SharedPreferences.getInstance();
      final String? azkarJson = prefs.getString(_azkarKey);
      
      final List<Map<String, dynamic>> results = [];
      final String lowercaseQuery = query.toLowerCase();
      
      if (azkarJson != null) {
        final Map<String, dynamic> azkarMap = json.decode(azkarJson);
        
        // Search through all categories
        for (final categoryAzkar in azkarMap.values) {
          for (final azkar in categoryAzkar) {
            if (azkar['arabic'].toLowerCase().contains(lowercaseQuery) ||
                azkar['translation'].toLowerCase().contains(lowercaseQuery) ||
                azkar['transliteration'].toLowerCase().contains(lowercaseQuery) ||
                azkar['reference'].toLowerCase().contains(lowercaseQuery)) {
              results.add(Map<String, dynamic>.from(azkar));
            }
          }
        }
      } else {
        // If not in SharedPreferences, load from asset
        final String jsonData = await rootBundle.loadString('assets/data/azkar.json');
        final Map<String, dynamic> azkarMap = json.decode(jsonData);
        
        // Search through all categories
        for (final categoryAzkar in azkarMap.values) {
          for (final azkar in categoryAzkar) {
            if (azkar['arabic'].toLowerCase().contains(lowercaseQuery) ||
                azkar['translation'].toLowerCase().contains(lowercaseQuery) ||
                azkar['transliteration'].toLowerCase().contains(lowercaseQuery) ||
                azkar['reference'].toLowerCase().contains(lowercaseQuery)) {
              results.add(Map<String, dynamic>.from(azkar));
            }
          }
        }
      }
      
      return results;
    } catch (e) {
      debugPrint('Error searching azkar: $e');
      return [];
    }
  }
} 