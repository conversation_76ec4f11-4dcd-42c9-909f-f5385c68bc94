import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import 'package:new_islamic_app_uk/dua/dua_detail.dart';
import 'package:new_islamic_app_uk/dua/services/dua_service.dart';
import 'package:share_plus/share_plus.dart';

class DuaSearchScreen extends StatefulWidget {
  const DuaSearchScreen({Key? key}) : super(key: key);

  @override
  State<DuaSearchScreen> createState() => _DuaSearchScreenState();
}

class _DuaSearchScreenState extends State<DuaSearchScreen> with SingleTickerProviderStateMixin {
  final DuaService _duaService = DuaService();
  final TextEditingController _searchController = TextEditingController();
  late TabController _tabController;

  bool _isLoading = false;
  String _searchQuery = '';
  List<Map<String, dynamic>> _duaResults = [];
  List<Map<String, dynamic>> _azkarResults = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.trim();
    if (query != _searchQuery) {
      setState(() {
        _searchQuery = query;
      });

      if (query.length >= 2) {
        _searchDuasAndAzkar(query);
      } else {
        setState(() {
          _duaResults = [];
          _azkarResults = [];
        });
      }
    }
  }

  Future<void> _searchDuasAndAzkar(String query) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final duaResults = await _duaService.searchDuas(query);
      final azkarResults = await _duaService.searchAzkar(query);

      setState(() {
        _duaResults = duaResults;
        _azkarResults = azkarResults;
        _isLoading = false;
      });
    } catch (e) {
      print('Error searching: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleFavorite(String duaId) async {
    await _duaService.toggleFavoriteDua(duaId);
    setState(() {});
  }

  Future<bool> _isDuaFavorite(String duaId) async {
    return await _duaService.isDuaFavorite(duaId);
  }

  void _copyDua(Map<String, dynamic> dua) {
    final String textToCopy =
        '${dua['arabic']}\n\n'
        '${dua['transliteration']}\n\n'
        '${dua['translation']}\n\n'
        'Reference: ${dua['reference']}';

    Clipboard.setData(ClipboardData(text: textToCopy));

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Dua copied to clipboard'),
        backgroundColor: Color(URLs().colorScheme),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _shareDua(Map<String, dynamic> dua) {
    final String textToShare =
        '${dua['arabic']}\n\n'
        '${dua['transliteration']}\n\n'
        '${dua['translation']}\n\n'
        'Reference: ${dua['reference']}\n\n'
        'Shared from Masjid Sunnah App';

    Share.share(textToShare);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'Search duas and azkar...',
            hintStyle: GoogleFonts.poppins(
              fontSize: 16,
              color: Colors.grey[400],
            ),
            border: InputBorder.none,
          ),
          style: GoogleFonts.poppins(
            fontSize: 16,
            color: Colors.grey[800],
          ),
          autofocus: true,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Color(URLs().colorScheme)),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          if (_searchController.text.isNotEmpty)
            IconButton(
              icon: Icon(Icons.clear, color: Color(URLs().colorScheme)),
              onPressed: () {
                _searchController.clear();
              },
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Color(URLs().colorScheme),
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: Color(URLs().colorScheme),
          labelStyle: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          tabs: [
            Tab(
              text: 'Duas (${_duaResults.length})',
            ),
            Tab(
              text: 'Azkar (${_azkarResults.length})',
            ),
          ],
        ),
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: Color(URLs().colorScheme),
              ),
            )
          : _searchQuery.length < 2
              ? _buildInitialState()
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _duaResults.isEmpty
                        ? _buildEmptyState('No duas found')
                        : _buildDuaResults(),
                    _azkarResults.isEmpty
                        ? _buildEmptyState('No azkar found')
                        : _buildAzkarResults(),
                  ],
                ),
    );
  }

  Widget _buildInitialState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FontAwesomeIcons.magnifyingGlass,
            size: 60,
            color: Colors.grey[400],
          ),
          SizedBox(height: 24),
          Text(
            "Search Duas & Azkar",
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 8),
          Text(
            "Type at least 2 characters to search",
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FontAwesomeIcons.fileCircleXmark,
            size: 60,
            color: Colors.grey[400],
          ),
          SizedBox(height: 24),
          Text(
            message,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 8),
          Text(
            "Try a different search term",
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDuaResults() {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: _duaResults.length,
      itemBuilder: (context, index) {
        final dua = _duaResults[index];
        return _buildDuaCard(dua);
      },
    );
  }

  Widget _buildAzkarResults() {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: _azkarResults.length,
      itemBuilder: (context, index) {
        final zikr = _azkarResults[index];
        return _buildZikrCard(zikr);
      },
    );
  }

  Widget _buildDuaCard(Map<String, dynamic> dua) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 2,
      child: InkWell(
        onTap: () {
          _duaService.addToRecentlyViewed(dua['id']);
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => DuaDetailScreen(dua: dua),
            ),
          ).then((_) => setState(() {}));
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Arabic text
              Text(
                dua['arabic'],
                textAlign: TextAlign.right,
                style: GoogleFonts.scheherazadeNew(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                  height: 1.6,
                ),
              ),
              SizedBox(height: 16),

              // Transliteration
              Text(
                dua['transliteration'],
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: Colors.grey[700],
                ),
              ),
              SizedBox(height: 16),

              // Translation
              Text(
                dua['translation'],
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[800],
                  height: 1.5,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 16),

              // Reference
              Row(
                children: [
                  Icon(
                    FontAwesomeIcons.book,
                    size: 14,
                    color: Color(URLs().colorScheme),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      dua['reference'],
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    onPressed: () => _copyDua(dua),
                    icon: Icon(
                      Icons.content_copy,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                    tooltip: 'Copy dua',
                  ),
                  IconButton(
                    onPressed: () => _shareDua(dua),
                    icon: Icon(
                      Icons.share,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                    tooltip: 'Share dua',
                  ),
                  FutureBuilder<bool>(
                    future: _isDuaFavorite(dua['id']),
                    builder: (context, snapshot) {
                      final isFavorite = snapshot.data ?? false;
                      return IconButton(
                        onPressed: () => _toggleFavorite(dua['id']),
                        icon: Icon(
                          isFavorite ? Icons.favorite : Icons.favorite_border,
                          color: isFavorite ? Colors.red : Colors.grey[600],
                          size: 20,
                        ),
                        tooltip: isFavorite ? 'Remove from favorites' : 'Add to favorites',
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildZikrCard(Map<String, dynamic> zikr) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Arabic text
            Text(
              zikr['arabic'],
              textAlign: TextAlign.right,
              style: GoogleFonts.scheherazadeNew(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
                height: 1.6,
              ),
            ),
            SizedBox(height: 16),

            // Transliteration
            Text(
              zikr['transliteration'],
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontStyle: FontStyle.italic,
                color: Colors.grey[700],
              ),
            ),
            SizedBox(height: 16),

            // Translation
            Text(
              zikr['translation'],
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[800],
                height: 1.5,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 16),

            // Reference and repeat count
            Row(
              children: [
                Icon(
                  FontAwesomeIcons.book,
                  size: 14,
                  color: Color(URLs().colorScheme),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    zikr['reference'],
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: Color(URLs().colorScheme).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Repeat: ${zikr['repeat']} times',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Color(URLs().colorScheme),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}