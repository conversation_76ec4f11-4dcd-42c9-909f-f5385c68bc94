import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class donatePage extends StatefulWidget {
  @override
  State<donatePage> createState() => _donatePageState();
}

class _donatePageState extends State<donatePage> {
  @override
  void initState() {
    super.initState();
    _launchURL("https://pub.dev/packages/url_launcher");
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Text("Donate Page");
  }
}

_launchURL(_url) async =>
    await canLaunch(_url) ? await launch(_url) : throw 'Could not launch $_url';
