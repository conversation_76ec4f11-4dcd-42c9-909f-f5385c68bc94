import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/models/event.dart';

class EventShowPage extends StatelessWidget {
  final EventModel event;

  const EventShowPage({Key? key, required this.event}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Event Details',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Color(URLs().colorScheme),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Event Image
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: NetworkImage(
                    event.img.isNotEmpty
                        ? event.img
                        : _extractImageUrl(event.content),
                  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            
            // Event Details
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    event.title,
                    style: GoogleFonts.poppins(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Date & Time
                  _buildInfoRow(
                    Icons.calendar_today,
                    'Date',
                    _getFormattedDate(),
                  ),
                  const SizedBox(height: 12),
                  
                  // Time
                  _buildInfoRow(
                    Icons.access_time,
                    'Time',
                    _getFormattedTime(),
                  ),
                  const SizedBox(height: 12),
                  
                  // Location
                  _buildInfoRow(
                    Icons.location_on,
                    'Location',
                    _extractLocation(event.content),
                  ),
                  
                  // Divider
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Divider(color: Colors.grey[300]),
                  ),
                  
                  // Description
                  Text(
                    'Description',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  // HTML Content
                  HtmlWidget(
                    event.content,
                    textStyle: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.grey[700],
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Color(URLs().colorScheme).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Color(URLs().colorScheme),
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            Text(
              value,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[800],
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getFormattedDate() {
    if (event.event_date.isNotEmpty) {
      try {
        final date = DateTime.parse(event.event_date);
        return DateFormat('EEEE, MMMM d, yyyy').format(date);
      } catch (e) {
        // If parsing fails, return the raw date
        return event.event_date;
      }
    } else if (event.event_day.isNotEmpty) {
      return event.event_day;
    } else {
      try {
        final date = DateTime.parse(event.date);
        return DateFormat('EEEE, MMMM d, yyyy').format(date);
      } catch (e) {
        return 'Date not specified';
      }
    }
  }

  String _getFormattedTime() {
    if (event.start_time.isNotEmpty) {
      if (event.end_time.isNotEmpty) {
        try {
          final startTime = DateFormat.jm().format(
            DateTime.parse("1969-07-20 " + event.start_time)
          );
          final endTime = DateFormat.jm().format(
            DateTime.parse("1969-07-20 " + event.end_time)
          );
          return '$startTime to $endTime';
        } catch (e) {
          return '${event.start_time} to ${event.end_time}';
        }
      } else {
        try {
          return DateFormat.jm().format(
            DateTime.parse("1969-07-20 " + event.start_time)
          );
        } catch (e) {
          return event.start_time;
        }
      }
    } else {
      return 'Time not specified';
    }
  }

  String _extractLocation(String content) {
    try {
      if (content.contains('Location:')) {
        final locationStart = content.indexOf('Location:') + 9;
        final locationEnd = content.indexOf('<', locationStart);
        if (locationEnd > locationStart) {
          return content.substring(locationStart, locationEnd).trim();
        }
      }
    } catch (e) {
      print('Error extracting location: $e');
    }
    return 'Masjid Sunnah';
  }

  String _extractImageUrl(String content) {
    try {
      if (content.contains('src="')) {
        final srcIndex = content.indexOf('src="');
        if (srcIndex != -1) {
          final startIndex = srcIndex + 5; // Length of 'src="'
          final endIndex = content.indexOf('"', startIndex);
          if (endIndex != -1) {
            final extractedUrl = content.substring(startIndex, endIndex);
            if (extractedUrl.startsWith('http')) {
              return extractedUrl;
            }
          }
        }
      }
    } catch (e) {
      print('Error extracting image: $e');
    }
    return 'https://images.unsplash.com/photo-1564121211835-e88c852648ab';
  }
}
