import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';

import 'URLs.dart';
import 'models/dua.dart';

class messagesPage extends StatefulWidget {
  @override
  State<messagesPage> createState() => _messagesPageState();
}

class _messagesPageState extends State<messagesPage> {
  @override
  Widget build(BuildContext context) {
    var _crossAxisSpacing = 8;
    var _screenWidth = MediaQuery.of(context).size.width;
    var _crossAxisCount = 1;
    var _width = (_screenWidth - ((_crossAxisCount - 1) * _crossAxisSpacing)) /
        _crossAxisCount;
    var cellHeight = 235;
    var _aspectRatio = _width / cellHeight;

    // TODO: implement build
    return  SingleChildScrollView(
      child: connectivityResult == ConnectivityResult.none ?
          Container(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 170.0),
                    child: Container(
                      width: 100,
                      child: Image.asset("assets/internet.png")
                      ),
                  ),
                Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Text("No Internet Connection"),
                )
              ],),
            )
            :
       messages.length >= 1
          ? Padding(
              padding: const EdgeInsets.all(8.0),
              child: ListView.builder(
                  physics: NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: messages.length,
                  // gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  //   crossAxisCount: 1,
                  //   crossAxisSpacing: 0,
                  //   mainAxisSpacing: 0,
                  //   childAspectRatio: _aspectRatio,
                  // ),
                  itemBuilder: (context, int index) {
                    return ClipRRect(
                      child: Card(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10.0),
                        ),
                        elevation: 0,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 10.0),
                          child: ListTile(
                            leading: Padding(
                              padding: const EdgeInsets.all(0),
                              child: Container(
                                // height: 100,
                                width: 50,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(7),
                                  image: DecorationImage(
                                    image:
                                        ExactAssetImage('assets/message.png'),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                            ),
                            title: Container(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text("${messages[index].title}",
                                      style: GoogleFonts.openSans(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w700)),
                                  Padding(
                                    padding:
                                        const EdgeInsets.only(top: 5, right: 0),
                                    child: HtmlWidget(messages[index].content ?? ''),
                                    // style: GoogleFonts.raleway(fontSize: 14),
                                    // ),
                                  ),
                                  Padding(
                                      padding: const EdgeInsets.only(
                                          top: 5, right: 0),
                                      child: Align(
                                        alignment: Alignment.bottomRight,
                                        child: Text(
                                          DateFormat.yMMMd().format(
                                              DateTime.parse(
                                                  "${messages[index].date}")),
                                          style: GoogleFonts.roboto(
                                              fontSize: 14, color: Colors.grey),
                                        ),
                                      )),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
            )
            : Container(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 170.0),
                    child: Container(
                      width: 100,
                      child: Image.asset("assets/message.png")
                      ),
                  ),
                Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Text("No Messages Found"),
                )
              ],),
            )
          // : GridView.count(
          //     physics: NeverScrollableScrollPhysics(),
          //     shrinkWrap: true,
          //     crossAxisSpacing: 10.0,
          //     crossAxisCount: 1,
          //     childAspectRatio: _aspectRatio,
          //     children: List.generate(8, (index) {
          //       return ClipRRect(
          //         borderRadius: BorderRadius.circular(8),
          //         child: Padding(
          //           padding:
          //               const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
          //           child: Container(
          //             child: Shimmer.fromColors(
          //                 baseColor: Colors.grey.withOpacity(0.3),
          //                 highlightColor: Colors.white,
          //                 child: Container(
          //                   height: 190,
          //                   width: double.infinity,
          //                   color: Colors.grey.withOpacity(0.3),
          //                 )),
          //           ),
          //         ),
          //       );
          //     }),
          //   )


    );
    
  }

  @override
  void initState() {
    super.initState();
    ConnectivityCheck();
    GetMessages();
  }

  List<AllduaModel> messages = [];
  GetMessages() async {
    try {
      var dio = Dio();
      Response response = await dio.get(URLs().WEBURL +
          "/wp-json/wp/v2/posts?categories=${URLs().message_category_id}&order=desc&per_page=100");
      if (response.statusCode == 200) {
        var parsedData =
            response.data.map((res) => AllduaModel.fromJson(res)).toList();
        parsedData.forEach(
          (f) async {
            messages.add(f);
          },
        );
        setState(() {
           messages;
        });
      }
    } catch (e) {
      print(e);
    }
  }


  var connectivityResult;
  ConnectivityCheck() async {
    connectivityResult = await (Connectivity().checkConnectivity());
if (connectivityResult == ConnectivityResult.mobile) {
  // I am connected to a mobile network.
} else if (connectivityResult == ConnectivityResult.wifi) {
  // I am connected to a wifi network.
}
  }
}
