import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:intl/intl.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/models/prayer_time.dart';
import 'package:new_islamic_app_uk/models/event.dart';
import 'package:new_islamic_app_uk/models/jumuah.dart';

class HomeController {
  final Dio _dio = Dio();
  
  // Prayer times data
  List<PrayerTimeModel> prayerTimes = [];
  String fajrTime = '';
  String zuhrTime = '';
  String asrTime = '';
  String maghribTime = '';
  String ishaTime = '';
  String hijriDate = '';
  String nextPrayer = '';
  String timeRemaining = '';
  
  // Events data
  List<EventModel> upcomingEvents = [];
  
  // Jumuah times data
  List<JumuahModel> jumuahTimes = [];
  String nextJumuahDay = 'Friday';
  String daysRemaining = '3 days';
  
  // Announcements data
  List<Map<String, dynamic>> announcements = [];
  
  // Donation campaign data
  String campaignTitle = 'Support Our Masjid';
  String campaignDescription = 'Help us maintain and improve our facilities to better serve the community.';
  double targetAmount = 10000;
  double raisedAmount = 6500;
  String donationUrl = 'https://example.com/donate';
  
  // Loading state
  bool isLoading = true;
  String errorMessage = '';
  
  // Initialize and fetch data
  Future<void> initialize() async {
    try {
      isLoading = true;
      
      // Fetch prayer times
      await fetchPrayerTimes();
      
      // Fetch events
      await fetchEvents();
      
      // Fetch jumuah times
      await fetchJumuahTimes();
      
      // Fetch announcements
      await fetchAnnouncements();
      
      // Calculate next prayer and time remaining
      calculateNextPrayer();
      
      isLoading = false;
    } catch (e) {
      isLoading = false;
      errorMessage = 'Failed to load data. Please check your internet connection.';
      print('Error initializing home data: $e');
    }
  }
  
  // Fetch prayer times from API
  Future<void> fetchPrayerTimes() async {
    try {
      final response = await _dio.get(URLs().WEBURL + "/wp-json/dpt/v1/prayertime?filter=today");
      
      if (response.statusCode == 200) {
        final parsedData = response.data.map<PrayerTimeModel>((res) => PrayerTimeModel.fromJson(res)).toList();
        
        if (parsedData.isNotEmpty) {
          prayerTimes = parsedData;
          
          final formattedDate = DateFormat('yyyy-MM-dd').format(DateTime.now());
          
          for (var prayer in prayerTimes) {
            fajrTime = DateFormat.Hm().format(DateTime.parse("$formattedDate ${prayer.fajrJamah}"));
            zuhrTime = DateFormat.Hm().format(DateTime.parse("$formattedDate ${prayer.zuhrJamah}"));
            asrTime = DateFormat.Hm().format(DateTime.parse("$formattedDate ${prayer.asrJamah}"));
            maghribTime = DateFormat.Hm().format(DateTime.parse("$formattedDate ${prayer.maghribJamah}"));
            ishaTime = DateFormat.Hm().format(DateTime.parse("$formattedDate ${prayer.ishaJamah}"));
            hijriDate = prayer.hijri_date;
          }
        }
      }
    } catch (e) {
      print('Error fetching prayer times: $e');
      // Set default values if API fails
      fajrTime = '5:30 AM';
      zuhrTime = '1:30 PM';
      asrTime = '4:45 PM';
      maghribTime = '7:15 PM';
      ishaTime = '9:00 PM';
      hijriDate = '15 Ramadan 1445';
    }
  }
  
  // Fetch events from API
  Future<void> fetchEvents() async {
    try {
      final response = await _dio.get(URLs().WEBURL + "/wp-json/wp/v2/events");
      
      if (response.statusCode == 200) {
        upcomingEvents = response.data.map<EventModel>((res) => EventModel.fromJson(res)).toList();
      }
    } catch (e) {
      print('Error fetching events: $e');
      // Set sample events if API fails
      upcomingEvents = [
        EventModel.fromJson({
          'id': 1,
          'title': {'rendered': 'Weekly Halaqa - 7:30 PM'},
          'content': {'rendered': '<p>Join us for our weekly Islamic study circle. Location: Main Prayer Hall</p><img src="https://images.unsplash.com/photo-1564121211835-e88c852648ab">'},
          'excerpt': {'rendered': 'Join us for our weekly Islamic study circle.'},
          'date': '2023-04-15T19:30:00',
        }),
        EventModel.fromJson({
          'id': 2,
          'title': {'rendered': 'Ramadan Iftar - 7:45 PM'},
          'content': {'rendered': '<p>Community iftar gathering for Ramadan. Location: Community Hall</p><img src="https://images.unsplash.com/photo-1532375810709-75b1da00537c">'},
          'excerpt': {'rendered': 'Community iftar gathering for Ramadan.'},
          'date': '2023-04-20T19:45:00',
        }),
      ];
    }
  }
  
  // Fetch jumuah times from API
  Future<void> fetchJumuahTimes() async {
    try {
      final response = await _dio.get(URLs().WEBURL + "/wp-json/wp/v2/jumuah");
      
      if (response.statusCode == 200) {
        jumuahTimes = response.data.map<JumuahModel>((res) => JumuahModel.fromJson(res)).toList();
      }
    } catch (e) {
      print('Error fetching jumuah times: $e');
      // Set sample jumuah times if API fails
      jumuahTimes = [
        JumuahModel.fromJson({
          'id': 1,
          'title': {'rendered': 'First Jumuah'},
          'content': {'rendered': '<p>Khutbah: 1:00 PM</p><p>Jamaat: 1:30 PM</p>'},
          'excerpt': {'rendered': 'First Jumuah prayer service'},
          'date': '2023-04-21T13:00:00',
        }),
        JumuahModel.fromJson({
          'id': 2,
          'title': {'rendered': 'Second Jumuah'},
          'content': {'rendered': '<p>Khutbah: 2:00 PM</p><p>Jamaat: 2:30 PM</p>'},
          'excerpt': {'rendered': 'Second Jumuah prayer service'},
          'date': '2023-04-21T14:00:00',
        }),
      ];
    }
    
    // Calculate days until next Jumuah
    calculateNextJumuah();
  }
  
  // Fetch announcements from API
  Future<void> fetchAnnouncements() async {
    try {
      final response = await _dio.get(URLs().WEBURL + "/wp-json/wp/v2/posts?categories=1");
      
      if (response.statusCode == 200) {
        announcements = response.data.map<Map<String, dynamic>>((res) => {
          'id': res['id'],
          'title': res['title']['rendered'],
          'content': res['excerpt']['rendered'].replaceAll(RegExp(r'<[^>]*>'), ''),
          'date': DateFormat('MMMM d, yyyy').format(DateTime.parse(res['date'])),
          'priority': 'normal',
        }).toList();
      }
    } catch (e) {
      print('Error fetching announcements: $e');
      // Set sample announcements if API fails
      announcements = [
        {
          'id': 1,
          'title': 'Ramadan Prayer Schedule',
          'content': 'Updated prayer schedule for the month of Ramadan.',
          'date': 'April 10, 2023',
          'priority': 'high',
        },
        {
          'id': 2,
          'title': 'Community Iftar',
          'content': 'Join us for community iftar every Saturday during Ramadan.',
          'date': 'April 8, 2023',
          'priority': 'normal',
        },
        {
          'id': 3,
          'title': 'Eid Prayer Announcement',
          'content': 'Eid prayer will be held at the community center.',
          'date': 'April 5, 2023',
          'priority': 'medium',
        },
      ];
    }
  }
  
  // Calculate next prayer and time remaining
  void calculateNextPrayer() {
    final now = DateTime.now();
    final formattedDate = DateFormat('yyyy-MM-dd').format(now);
    
    final fajrDateTime = _parseTime(formattedDate, fajrTime);
    final zuhrDateTime = _parseTime(formattedDate, zuhrTime);
    final asrDateTime = _parseTime(formattedDate, asrTime);
    final maghribDateTime = _parseTime(formattedDate, maghribTime);
    final ishaDateTime = _parseTime(formattedDate, ishaTime);
    
    // Tomorrow's Fajr
    final tomorrow = DateTime.now().add(Duration(days: 1));
    final tomorrowFormatted = DateFormat('yyyy-MM-dd').format(tomorrow);
    final tomorrowFajr = _parseTime(tomorrowFormatted, fajrTime);
    
    if (now.isBefore(fajrDateTime)) {
      nextPrayer = 'Fajr';
      timeRemaining = _formatDuration(fajrDateTime.difference(now));
    } else if (now.isBefore(zuhrDateTime)) {
      nextPrayer = 'Zuhr';
      timeRemaining = _formatDuration(zuhrDateTime.difference(now));
    } else if (now.isBefore(asrDateTime)) {
      nextPrayer = 'Asr';
      timeRemaining = _formatDuration(asrDateTime.difference(now));
    } else if (now.isBefore(maghribDateTime)) {
      nextPrayer = 'Maghrib';
      timeRemaining = _formatDuration(maghribDateTime.difference(now));
    } else if (now.isBefore(ishaDateTime)) {
      nextPrayer = 'Isha';
      timeRemaining = _formatDuration(ishaDateTime.difference(now));
    } else {
      nextPrayer = 'Fajr';
      timeRemaining = _formatDuration(tomorrowFajr.difference(now));
    }
  }
  
  // Calculate days until next Jumuah
  void calculateNextJumuah() {
    final now = DateTime.now();
    final currentWeekday = now.weekday;
    
    // Friday is weekday 5
    int daysUntilFriday = 5 - currentWeekday;
    if (daysUntilFriday <= 0) {
      daysUntilFriday += 7;
    }
    
    nextJumuahDay = 'Friday, ${DateFormat('MMMM d').format(now.add(Duration(days: daysUntilFriday)))}';
    daysRemaining = daysUntilFriday == 1 ? 'Tomorrow' : '$daysUntilFriday days';
  }
  
  // Helper method to parse time string
  DateTime _parseTime(String date, String timeStr) {
    try {
      // Handle AM/PM format
      if (timeStr.contains('AM') || timeStr.contains('PM')) {
        return DateFormat('yyyy-MM-dd hh:mm a').parse('$date $timeStr');
      }
      // Handle 24-hour format
      return DateFormat('yyyy-MM-dd HH:mm').parse('$date $timeStr');
    } catch (e) {
      print('Error parsing time: $e');
      return DateTime.now();
    }
  }
  
  // Helper method to format duration
  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes.remainder(60)}m';
    } else {
      return '${duration.inMinutes}m';
    }
  }
  
  // Get greeting based on time of day
  String getGreeting() {
    final hour = DateTime.now().hour;
    
    if (hour < 12) {
      return 'Good Morning';
    } else if (hour < 17) {
      return 'Good Afternoon';
    } else {
      return 'Good Evening';
    }
  }
} 