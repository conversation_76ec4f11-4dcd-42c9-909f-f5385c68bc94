import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';

import 'package:flutter_countdown_timer/current_remaining_time.dart';
import 'package:flutter_countdown_timer/flutter_countdown_timer.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';
import 'package:slide_countdown/slide_countdown.dart';

import 'URLs.dart';
import 'event_show_page.dart';
import 'models/event.dart';
import 'models/jumuah.dart';
import 'models/prayer_time.dart';
import 'package:new_islamic_app_uk/controllers/home_controller.dart';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/features/prayer_reminders/widgets/prayer_reminder_widget.dart';
import 'package:new_islamic_app_uk/features/daily_hadith/widgets/daily_hadith_card.dart';
import 'package:new_islamic_app_uk/features/arabic_word/widgets/arabic_word_card.dart';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/common/loading_state.dart';
import 'package:new_islamic_app_uk/widgets/common/error_state.dart';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/theme/app_theme.dart';
import 'package:new_islamic_app_uk/widgets/home/<USER>';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final HomeController _controller = HomeController();
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    await _controller.initialize();

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final urls = URLs();

    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      body: RefreshIndicator(
        onRefresh: _loadData,
        color: AppTheme.primaryGreen,
        child: _buildPageContent(),
      ),
    );
  }

  Widget _buildPageContent() {
    if (_isLoading) {
      return LoadingState(message: 'Loading mosque information...');
    }

    if (_controller.errorMessage.isNotEmpty) {
      return ErrorState(
        message: _controller.errorMessage,
        onRetry: _loadData,
      );
    }

    return ModernHomeContent(
      controller: _controller,
      scrollController: _scrollController,
    );
  }
}
