class URLs {
  var WEBURL = "https://fountainsofknowledge.com";
  // "https://scala.smartstart.institute";
  var dua_category_id = 11;
  // 114;
  var message_category_id = 10;
  // 132;
  var quran_category_id = 0;
  // 130;
  var event_category_id = 9;
  // 131;
  var jumuah_time_category_id = 12;
  // 133;
  var spiritual_journal_category_id = 13;

  // Theme colors - Updated with pastel colors
  int TabbarColor = 0xFF4A5FC1; // Soft pastel blue
  int colorScheme = 0xFF455CC3; // Soft pastel blue
  int colorGold = 0xFFFFC973; // Soft gold
  
  // Additional theme variables for consistent styling
  int primaryColor = 0xFF455CC3; // Soft pastel blue
  int secondaryColor = 0xFFFFC973; // Soft gold
  int accentColor = 0xFF5AD6C4; // Mint/teal
  int lightBlue = 0xFF7AA2FF; // Lighter pastel blue
  int errorColor = 0xFFFF8A80; // Soft coral/red
  int successColor = 0xFF98DCA8; // Soft green
  int warningColor = 0xFFFFC973; // Soft amber
  int infoColor = 0xFF81D4FA; // Soft light blue
  int lightGray = 0xFFF8F9FA;
  int mediumGray = 0xFFEEEFF3;
  int darkGray = 0xFF9AA0B8;
  
  // Text colors
  int textPrimary = 0xFF2C3E50;
  int textSecondary = 0xFF6C7A89;
  int textLight = 0xFFFFFFFF;
  
  // Spiritual journal specific colors - Updated with pastel palette
  int journalPrimaryColor = 0xFF8C9EFF;    // Soft indigo
  int journalSecondaryColor = 0xFFABB4FF;  // Lighter indigo
  int journalAccentColor = 0xFF84FFFF;     // Soft cyan
  int journalBackgroundColor = 0xFFF8F9FA; // Light Gray Background
  
  // Mood colors - Updated with pastel colors
  int moodExcellent = 0xFF98DCA8;  // Soft green
  int moodGood = 0xFF7AA2FF;       // Soft blue
  int moodNeutral = 0xFFFFC973;    // Soft amber
  int moodLow = 0xFFFFAB91;        // Soft orange
  int moodPoor = 0xFFFF8A80;       // Soft red/coral
  
  // Spacing and sizing
  double defaultPadding = 16.0;
  double smallPadding = 8.0;
  double largePadding = 24.0;
  double defaultBorderRadius = 12.0; // Increased for more rounded corners
  double cardElevation = 1.5; // Softer shadows
  
  // New gradient palettes for softer UI
  List<int> primaryGradient = [0xFF4A5FC1, 0xFF8C9EFF]; // Soft blue gradient
  List<int> secondaryGradient = [0xFF5AD6C4, 0xFF84FFFF]; // Soft cyan/mint gradient
  List<int> accentGradient = [0xFF98DCA8, 0xFFCCFFCC]; // Soft green gradient
  List<int> warmGradient = [0xFFFFAB91, 0xFFFFC973]; // Soft orange to amber
  List<int> coolGradient = [0xFF7AA2FF, 0xFF81D4FA]; // Soft blue gradient
  
  // Animation durations
  int shortAnimationDuration = 200; // milliseconds
  int mediumAnimationDuration = 400; // milliseconds
  int longAnimationDuration = 800; // milliseconds
  
  // Elevation levels for consistent shadows
  double lowElevation = 1.0;
  double mediumElevation = 3.0; // Reduced for softer shadows
  double highElevation = 6.0; // Reduced for softer shadows
  
  // Font sizes for better typography
  double smallFontSize = 12.0;
  double mediumFontSize = 16.0;
  double largeFontSize = 20.0;
  double headingFontSize = 24.0;
  
  // New design system additions
  double cardBorderRadius = 16.0;
  double buttonBorderRadius = 24.0;
  double iconSize = 24.0;
  double largeIconSize = 32.0;
  
  // New pastel accent colors
  int pastelPurple = 0xFFD0A8FF;
  int pastelPink = 0xFFF8BBD0;
  int pastelYellow = 0xFFFFF59D;
}
