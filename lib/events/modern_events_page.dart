import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../theme/app_theme.dart';
import '../widgets/common/modern_card.dart';
import '../widgets/common/modern_search_bar.dart';

class ModernEventsPage extends StatefulWidget {
  const ModernEventsPage({Key? key}) : super(key: key);

  @override
  State<ModernEventsPage> createState() => _ModernEventsPageState();
}

class _ModernEventsPageState extends State<ModernEventsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _selectedTabIndex = 0;
  String _selectedCategory = 'All';
  
  final List<String> _categories = ['All', 'Lectures', 'Workshops', 'Community'];
  
  final List<Map<String, dynamic>> _events = [
    {
      'title': 'Islamic Finance Workshop',
      'description': 'Learn about Islamic banking and finance principles',
      'date': 'March 15, 2024',
      'time': '7:00 PM',
      'location': 'Masjid <PERSON>',
      'category': 'Workshops',
      'icon': FontAwesomeIcons.coins,
      'color': Color(0xFF4CAF50),
      'isUpcoming': true,
    },
    {
      'title': 'Quran Recitation Competition',
      'description': 'Annual Quran recitation competition for all ages',
      'date': 'March 22, 2024',
      'time': '6:30 PM',
      'location': 'Masjid Sunnah Nelson',
      'category': 'Community',
      'icon': FontAwesomeIcons.bookQuran,
      'color': Color(0xFF2196F3),
      'isUpcoming': true,
    },
    {
      'title': 'Eid Celebration',
      'description': 'Community Eid celebration with food and activities',
      'date': 'April 10, 2024',
      'time': '10:00 AM',
      'location': 'Masjid Sunnah Nelson',
      'category': 'Community',
      'icon': FontAwesomeIcons.star,
      'color': Color(0xFFFF9800),
      'isUpcoming': true,
    },
    {
      'title': 'Youth Islamic Studies',
      'description': 'Weekly Islamic studies session for youth',
      'date': 'February 28, 2024',
      'time': '7:00 PM',
      'location': 'Masjid Sunnah Nelson',
      'category': 'Lectures',
      'icon': FontAwesomeIcons.graduationCap,
      'color': Color(0xFF9C27B0),
      'isUpcoming': false,
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _selectedTabIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildUpcomingTab(),
                _buildPastTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(
          Icons.arrow_back_ios,
          color: AppTheme.textPrimary,
        ),
      ),
      title: Text(
        'Events',
        style: AppTheme.textTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        IconButton(
          onPressed: () {
            // Calendar view
          },
          icon: const Icon(
            Icons.calendar_month_outlined,
            color: AppTheme.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppTheme.primaryGreen,
          borderRadius: BorderRadius.circular(12),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondary,
        labelStyle: GoogleFonts.poppins(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: GoogleFonts.poppins(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        tabs: const [
          Tab(
            icon: Icon(Icons.upcoming, size: 20),
            text: 'Upcoming',
          ),
          Tab(
            icon: Icon(Icons.history, size: 20),
            text: 'Past',
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingTab() {
    final upcomingEvents = _events.where((event) => event['isUpcoming']).toList();
    
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Categories filter
          _buildCategoriesFilter(),
          
          const SizedBox(height: 24),
          
          // Events list
          Text(
            'Upcoming Events',
            style: AppTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 16),
          
          if (upcomingEvents.isNotEmpty)
            ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: upcomingEvents.length,
              itemBuilder: (context, index) {
                final event = upcomingEvents[index];
                if (_selectedCategory == 'All' || event['category'] == _selectedCategory) {
                  return _buildEventCard(event);
                }
                return const SizedBox.shrink();
              },
            )
          else
            _buildEmptyState('No upcoming events'),
          
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  Widget _buildPastTab() {
    final pastEvents = _events.where((event) => !event['isUpcoming']).toList();
    
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Categories filter
          _buildCategoriesFilter(),
          
          const SizedBox(height: 24),
          
          // Events list
          Text(
            'Past Events',
            style: AppTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 16),
          
          if (pastEvents.isNotEmpty)
            ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: pastEvents.length,
              itemBuilder: (context, index) {
                final event = pastEvents[index];
                if (_selectedCategory == 'All' || event['category'] == _selectedCategory) {
                  return _buildEventCard(event, isPast: true);
                }
                return const SizedBox.shrink();
              },
            )
          else
            _buildEmptyState('No past events'),
          
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  Widget _buildCategoriesFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Categories',
          style: AppTheme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 40,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: _categories.length,
            separatorBuilder: (context, index) => const SizedBox(width: 8),
            itemBuilder: (context, index) {
              final category = _categories[index];
              final isSelected = category == _selectedCategory;
              
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedCategory = category;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected ? AppTheme.primaryGreen : AppTheme.surfaceColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    category,
                    style: AppTheme.textTheme.labelMedium?.copyWith(
                      color: isSelected ? Colors.white : AppTheme.textSecondary,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEventCard(Map<String, dynamic> event, {bool isPast = false}) {
    return ModernCard(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: event['color'].withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: FaIcon(
                  event['icon'],
                  color: event['color'],
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      event['title'],
                      style: AppTheme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isPast ? AppTheme.textSecondary : AppTheme.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      event['location'],
                      style: AppTheme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.textLight,
                      ),
                    ),
                  ],
                ),
              ),
              if (!isPast)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: event['color'].withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    event['category'],
                    style: AppTheme.textTheme.labelSmall?.copyWith(
                      color: event['color'],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Text(
            event['description'],
            style: AppTheme.textTheme.bodyMedium?.copyWith(
              color: isPast ? AppTheme.textLight : AppTheme.textSecondary,
            ),
          ),
          
          const SizedBox(height: 12),
          
          Row(
            children: [
              Icon(
                Icons.calendar_today_outlined,
                size: 16,
                color: AppTheme.textLight,
              ),
              const SizedBox(width: 4),
              Text(
                event['date'],
                style: AppTheme.textTheme.bodySmall?.copyWith(
                  color: AppTheme.textLight,
                ),
              ),
              const SizedBox(width: 16),
              Icon(
                Icons.access_time,
                size: 16,
                color: AppTheme.textLight,
              ),
              const SizedBox(width: 4),
              Text(
                event['time'],
                style: AppTheme.textTheme.bodySmall?.copyWith(
                  color: AppTheme.textLight,
                ),
              ),
            ],
          ),
        ],
      ),
      onTap: () => _navigateToEventDetails(event),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        children: [
          const SizedBox(height: 40),
          Icon(
            Icons.event_busy,
            size: 64,
            color: AppTheme.textLight,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: AppTheme.textTheme.titleMedium?.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToEventDetails(Map<String, dynamic> event) {
    // Navigate to event details screen
    print('Navigate to event: ${event['title']}');
  }
}
