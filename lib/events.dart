import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:new_islamic_app_uk/event_show_page.dart';
import 'package:shimmer/shimmer.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';

import 'URLs.dart';
import 'models/event.dart';

class allEventsPage extends StatefulWidget {
  @override
  State<allEventsPage> createState() => _allEventsPageState();
}

class _allEventsPageState extends State<allEventsPage> with SingleTickerProviderStateMixin {
  var inputFormat = DateFormat('hh:mm a');
  late TabController _tabController;
  bool _isLoading = true;
  String _searchQuery = '';
  TextEditingController _searchController = TextEditingController();
  List<EventModel> _filteredEvents = [];
  
  // Event categories
  final List<String> _categories = [
    'All Events',
    'Upcoming',
    'Past',
    'Lectures',
    'Workshops',
    'Community'
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);
    _tabController.addListener(_handleTabSelection);
    ConnectivityCheck();
    GetAllEvents();
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabSelection);
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _handleTabSelection() {
    if (_tabController.indexIsChanging) {
      _filterEventsByCategory(_categories[_tabController.index]);
    }
  }

  void _filterEventsByCategory(String category) {
    setState(() {
      if (category == 'All Events') {
        _filteredEvents = List.from(events);
      } else if (category == 'Upcoming') {
        _filteredEvents = events.where((event) {
          if (event.event_date.isNotEmpty) {
            try {
              final eventDate = DateTime.parse(event.event_date);
              return eventDate.isAfter(DateTime.now());
            } catch (e) {
              return false;
            }
          } else {
            return false;
          }
        }).toList();
      } else if (category == 'Past') {
        _filteredEvents = events.where((event) {
          if (event.event_date.isNotEmpty) {
            try {
              final eventDate = DateTime.parse(event.event_date);
              return eventDate.isBefore(DateTime.now());
            } catch (e) {
              return false;
            }
          } else {
            return false;
          }
        }).toList();
      } else {
        // Filter by category name (assuming category is in title)
        _filteredEvents = events.where((event) => 
          event.title.toLowerCase().contains(category.toLowerCase())).toList();
      }
      
      // Apply search filter if there's a search query
      if (_searchQuery.isNotEmpty) {
        _filteredEvents = _filteredEvents.where((event) => 
          event.title.toLowerCase().contains(_searchQuery.toLowerCase())).toList();
      }
    });
  }

  void _searchEvents(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filterEventsByCategory(_categories[_tabController.index]);
      } else {
        _filteredEvents = events.where((event) => 
          event.title.toLowerCase().contains(query.toLowerCase())).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Events',
          style: GoogleFonts.poppins(
            fontSize: 22,
            fontWeight: FontWeight.w600,
            color: Color(URLs().colorScheme),
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Color(URLs().colorScheme)),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh, color: Color(URLs().colorScheme)),
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              GetAllEvents();
            },
          ),
        ],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(110),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(
                      color: Colors.grey[300]!,
                      width: 1,
                    ),
                  ),
                  child: TextField(
                    controller: _searchController,
                    onChanged: _searchEvents,
                    decoration: InputDecoration(
                      hintText: 'Search events...',
                      hintStyle: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey[500],
                      ),
                      prefixIcon: Icon(
                        Icons.search,
                        color: Color(URLs().colorScheme),
                      ),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: Icon(Icons.clear, color: Colors.grey[500]),
                              onPressed: () {
                                _searchController.clear();
                                _searchEvents('');
                              },
                            )
                          : null,
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ),
              TabBar(
                controller: _tabController,
                isScrollable: true,
                labelColor: Colors.white,
                unselectedLabelColor: Color(URLs().colorScheme),
                labelStyle: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                unselectedLabelStyle: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                indicator: BoxDecoration(
                  borderRadius: BorderRadius.circular(30),
                  color: Color(URLs().colorScheme),
                ),
                tabs: _categories.map((category) => 
                  Tab(
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: Text(category),
                    ),
                  )
                ).toList(),
              ),
            ],
          ),
        ),
      ),
      body: connectivityResult == ConnectivityResult.none
          ? _buildNoInternetView()
          : _isLoading
              ? _buildLoadingView()
              : _buildEventsList(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Show calendar view or filter by date
          _showDateFilterDialog();
        },
        backgroundColor: Color(URLs().colorScheme),
        child: Icon(Icons.calendar_month, color: Colors.white),
      ),
    );
  }

  Widget _buildNoInternetView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.wifi_off,
              size: 60,
              color: Colors.grey[500],
            ),
          ),
          SizedBox(height: 24),
          Text(
            "No Internet Connection",
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 8),
          Text(
            "Please check your connection and try again",
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              ConnectivityCheck();
              GetAllEvents();
            },
            icon: Icon(Icons.refresh),
            label: Text("Try Again"),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(URLs().colorScheme),
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingView() {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Card(
          margin: EdgeInsets.only(bottom: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 2,
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              height: 120,
              padding: EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    width: 90,
                    height: 90,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: double.infinity,
                          height: 20,
                          color: Colors.white,
                        ),
                        SizedBox(height: 8),
                        Container(
                          width: 150,
                          height: 15,
                          color: Colors.white,
                        ),
                        SizedBox(height: 8),
                        Container(
                          width: 100,
                          height: 15,
                          color: Colors.white,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEventsList() {
    if (_filteredEvents.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FontAwesomeIcons.calendarXmark,
              size: 60,
              color: Colors.grey[400],
            ),
            SizedBox(height: 24),
            Text(
              "No Events Found",
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
            SizedBox(height: 8),
            Text(
              _searchQuery.isNotEmpty
                  ? "Try a different search term"
                  : "Check back later for upcoming events",
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: _filteredEvents.length,
      itemBuilder: (context, index) {
        final event = _filteredEvents[index];
        return _buildEventCard(event);
      },
    );
  }

  Widget _buildEventCard(EventModel event) {
    bool isUpcoming = false;
    if (event.event_date.isNotEmpty) {
      try {
        final eventDate = DateTime.parse(event.event_date);
        isUpcoming = eventDate.isAfter(DateTime.now());
      } catch (e) {
        // Handle parsing error
      }
    }

    return Card(
      margin: EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 2,
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => EventShowPage(event: event),
            ),
          );
        },
        borderRadius: BorderRadius.circular(16),
        child: Column(
          children: [
            // Event image
            ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              child: event.img != ''
                  ? CachedNetworkImage(
                      imageUrl: event.img,
                      height: 180,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        height: 180,
                        color: Colors.grey[300],
                        child: Center(
                          child: CircularProgressIndicator(
                            color: Color(URLs().colorScheme),
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        height: 180,
                        color: Color(URLs().colorScheme).withOpacity(0.1),
                        child: Center(
                          child: Image.asset(
                            'assets/event-cover.png',
                            height: 80,
                          ),
                        ),
                      ),
                    )
                  : Container(
                      height: 180,
                      color: Color(URLs().colorScheme).withOpacity(0.1),
                      child: Center(
                        child: Image.asset(
                          'assets/event-cover.png',
                          height: 80,
                        ),
                      ),
                    ),
            ),
            
            // Event details
            Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status badge
                  if (isUpcoming)
                    Container(
                      margin: EdgeInsets.only(bottom: 12),
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.green.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        'Upcoming',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Colors.green[700],
                        ),
                      ),
                    ),
                  
                  // Title
                  Text(
                    event.title,
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                  SizedBox(height: 12),
                  
                  // Date and time
                  Row(
                    children: [
                      Icon(
                        FontAwesomeIcons.calendar,
                        size: 16,
                        color: Color(URLs().colorScheme),
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          event.event_date != ''
                              ? _formatDate(event.event_date)
                              : "Day: ${event.event_day}",
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.grey[700],
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  
                  // Time
                  Row(
                    children: [
                      Icon(
                        FontAwesomeIcons.clock,
                        size: 16,
                        color: Color(URLs().colorScheme),
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          event.end_time != ''
                              ? "Time: ${_formatTime(event.start_time)} to ${_formatTime(event.end_time)}"
                              : "Start Time: ${_formatTime(event.start_time)}",
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.grey[700],
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  // View details button
                  SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton.icon(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => EventShowPage(event: event),
                            ),
                          );
                        },
                        icon: Icon(
                          Icons.arrow_forward,
                          size: 18,
                        ),
                        label: Text('View Details'),
                        style: TextButton.styleFrom(
                          foregroundColor: Color(URLs().colorScheme),
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDateFilterDialog() {
    showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: ColorScheme.light(
              primary: Color(URLs().colorScheme),
            ),
            dialogBackgroundColor: Colors.white,
          ),
          child: child!,
        );
      },
    ).then((selectedDate) {
      if (selectedDate != null) {
        setState(() {
          _filteredEvents = events.where((event) {
            if (event.event_date.isNotEmpty) {
              try {
                final eventDate = DateTime.parse(event.event_date);
                return eventDate.year == selectedDate.year &&
                    eventDate.month == selectedDate.month &&
                    eventDate.day == selectedDate.day;
              } catch (e) {
                return false;
              }
            } else {
              return false;
            }
          }).toList();
        });
        
        // Switch to "All Events" tab but keep the date filter
        _tabController.animateTo(0);
      }
    });
  }

  // Helper method to format time
  String _formatTime(String timeStr) {
    if (timeStr.isEmpty) return '';
    
    try {
      return DateFormat.jm('en_US').format(DateTime.parse("1969-07-20 " + timeStr));
    } catch (e) {
      return timeStr;
    }
  }
  
  // Helper method to format date
  String _formatDate(String dateStr) {
    if (dateStr.isEmpty) return '';
    
    try {
      final date = DateTime.parse(dateStr);
      return DateFormat.yMMMMd().format(date);
    } catch (e) {
      return dateStr;
    }
  }

  List<EventModel> events = [];
  var connectivityResult;

  Future<void> GetAllEvents() async {
    try {
      var dio = Dio();
      Response response = await dio.get(URLs().WEBURL +
          "/wp-json/wp/v2/posts?categories=${URLs().event_category_id}&order=desc&per_page=100");
      if (response.statusCode == 200) {
        events.clear();
        var parsedData =
            response.data.map((res) => EventModel.fromJson(res)).toList();
        parsedData.forEach(
          (f) async {
            events.add(f);
          },
        );
        
        // Initialize filtered events with all events
        _filteredEvents = List.from(events);
        
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      print(e);
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> ConnectivityCheck() async {
    connectivityResult = await (Connectivity().checkConnectivity());
  }
}
