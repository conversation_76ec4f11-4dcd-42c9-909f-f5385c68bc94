import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:smooth_compass_plus/utils/src/compass_ui.dart';

class QiblahDirectionPage extends StatelessWidget {

   var _compassSvg = SvgPicture.asset('assets/compass.svg');
  var _needleSvg = SvgPicture.asset(
    'assets/needle.svg',
    fit: BoxFit.contain,
    height: 300,
    alignment: Alignment.center,
  );


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: InkWell(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                margin: EdgeInsets.only(left:10, top: 5, bottom: 5),
                padding: EdgeInsets.only(left:8),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12)
                ),
                child: Icon(Icons.arrow_back_ios, color: Colors.white,),
              ),
            ),
      ),
      body: Center(
        child: SmoothCompassWidget(
          rotationSpeed: 200,
          height: 400,
          isQiblahCompass: true,
          width: 400,
          compassBuilder: (context, AsyncSnapshot<CompassModel>? compassData,
              Widget compassAsset) {
            return AnimatedRotation(
              turns: compassData?.data?.turns ?? 0 / 360,
              duration: const Duration(milliseconds: 400),
              child: SizedBox(
                height: 300,
                width: 300,
                child: Stack(
                  children: [
                    //put your compass here
                    Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      child: _compassSvg
                    ),

                    //put your qiblah needle here
                    Positioned(
                      top: 20,
                      left: 0,
                      right: 0,
                      bottom: 20,
                      child: AnimatedRotation(
                        turns: (compassData?.data?.qiblahOffset ?? 0) / 360,
                        duration: const Duration(milliseconds: 400),
                        child: _needleSvg
                      ),
                    )
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}





// import 'package:flutter/material.dart';
// import 'package:flutter_qiblah/flutter_qiblah.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:smooth_compass_plus/utils/src/compass_ui.dart';

// class QiblahDirectionPage extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     // TODO: implement build
//     return Scaffold(
//       appBar: AppBar(
//         elevation: 0,
//         title: Text(
//           "Qiblah Direction",
//           style: GoogleFonts.quicksand(
//             color: Colors.black,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//         iconTheme: IconThemeData(color: Colors.black),
//         backgroundColor: Colors.transparent,
//       ),
//       body: Padding(
//         padding: const EdgeInsets.all(20.0),
//         child: QiblahCompassWidget(),
//       ),
//     );
//   }
// }

// class QiblahCompassWidget extends StatefulWidget {
//   @override
//   State<QiblahCompassWidget> createState() => _QiblahCompassWidgetState();
// }

// class _QiblahCompassWidgetState extends State<QiblahCompassWidget> {
//   var _compassSvg = SvgPicture.asset('assets/compass.svg');
//   var _needleSvg = SvgPicture.asset(
//     'assets/needle.svg',
//     fit: BoxFit.contain,
//     height: 300,
//     alignment: Alignment.center,
//   );

//   @override
//   Widget build(BuildContext context) {

//     return StreamBuilder(
//       stream: FlutterQiblah.qiblahStream,
//       builder: (_, AsyncSnapshot<QiblahDirection> snapshot) {
//         if (snapshot.connectionState == ConnectionState.waiting)
//           return Center(child: CircularProgressIndicator());

//         final qiblahDirection = snapshot.data;

//         return Stack(
//           alignment: Alignment.center,
//           children: <Widget>[
//             Transform.rotate(
//               angle: ((qiblahDirection?.direction ?? 0) *
//                   (3.1415926535897932 / 180) *
//                   -1),
//               child: _compassSvg,
//             ),
//             Transform.rotate(
//               angle: ((qiblahDirection?.qiblah ?? 0) *
//                   (3.1415926535897932 / 180) *
//                   -1),
//               alignment: Alignment.center,
//               child: _needleSvg,
//             ),
//             Positioned(
//               bottom: 8,
//               child: Text("${qiblahDirection?.offset.toStringAsFixed(3)}°"),
//             )
//           ],
//         );
//       },
//     );
//   }
// }
