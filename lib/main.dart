import 'dart:io';
import 'dart:isolate';
import 'dart:math';
import 'package:animated_notch_bottom_bar/animated_notch_bottom_bar/animated_notch_bottom_bar.dart';
import 'package:flutter_svg/svg.dart';
import 'package:new_islamic_app_uk/theme/app_theme.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:android_alarm_manager_plus/android_alarm_manager_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_ringtone_player/flutter_ringtone_player.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:home_widget/home_widget.dart';
import 'package:intl/intl.dart';
import 'package:new_islamic_app_uk/about.dart';
import 'package:new_islamic_app_uk/dua/dua_category.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/screens/prayer_reminder_settings.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/screens/prayer_reminder_dashboard.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/screens/prayer_notification_history.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/services/prayer_notification_service.dart' as prayer_reminders;
import 'package:new_islamic_app_uk/features/prayer_reminders/controllers/notification_controller.dart';
import 'package:new_islamic_app_uk/qiblah_direction.dart';
import 'package:new_islamic_app_uk/features/quran/quran_module.dart';
import 'package:new_islamic_app_uk/zakat_calculator.dart';

import 'package:share_plus/share_plus.dart';
import 'package:splash_view/source/presentation/pages/splash_view.dart';
import 'package:splash_view/source/presentation/widgets/done.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:workmanager/workmanager.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';
import 'package:new_islamic_app_uk/features/quran/controllers/quran_settings_controller.dart';
import 'package:new_islamic_app_uk/features/quran/services/quran_service.dart';

import 'URLs.dart';
import 'donate.dart';
import 'events.dart';
import 'home_page.dart';
import 'messages.dart';
import 'models/prayer_time.dart';
import 'package:new_islamic_app_uk/features/daily_hadith/screens/hadith_collection_screen.dart';
import 'package:new_islamic_app_uk/features/arabic_word/screens/word_collection_screen.dart';
import 'package:new_islamic_app_uk/features/prayer_times/controllers/notification_controller.dart';
import 'package:new_islamic_app_uk/features/prayer_times/services/prayer_notification_service.dart' as prayer_times;
import 'package:new_islamic_app_uk/features/prayer_times/services/prayer_api_service.dart' as prayer_times_api;
import 'package:new_islamic_app_uk/features/spiritual_journal/controllers/journal_controller.dart';
import 'package:new_islamic_app_uk/features/spiritual_journal/screens/journal_list_screen.dart';
import 'package:new_islamic_app_uk/features/spiritual_journal/widgets/journal_home_card.dart';
import 'package:new_islamic_app_uk/features/dua_journal/dua_journal_main.dart';
import 'package:new_islamic_app_uk/features/hadith/hadith_module.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_home_screen.dart';
import 'package:new_islamic_app_uk/widgets/common/modern_bottom_nav.dart';

List<PrayerTimeModel> prayer = [];
var fajrTime;
var zuhrTime;
var AsrTime;
var MaghribTime;
var IshaTime;
var Date;
var fajrTimeAlarm;
var zuhrTimeAlarm;
var AsrTimeAlarm;
var MaghribTimeAlarm;
var IshaTimeAlarm;

var now = DateTime.now();

var formatter = new DateFormat('yyyy-MM-dd');
String formattedDate = formatter.format(now);

// var string = DateTime.now();
// var NewDateTime2 = DateFormat.Hm()
//               .format(DateTime.parse(string.toString()));
// var NewDateTime = DateTime.parse(string.toString());

 GetPrayerTime() async {
  try {
    var dio = Dio();
    var response = await dio
        .get(URLs().WEBURL + "/wp-json/dpt/v1/prayertime?filter=today");
    if (response.statusCode == 200) {
      var parsedData =
          response.data.map((res) => PrayerTimeModel.fromJson(res)).toList();

      parsedData.forEach(
        (f) async {
          // Parse and format prayer times
          fajrTime = DateFormat.Hm()
              .format(DateTime.parse("$formattedDate " + f.fajrJamah));

          zuhrTime = DateFormat.Hm()
              .format(DateTime.parse("$formattedDate " + f.zuhrJamah));
          AsrTime = DateFormat.Hm()
              .format(DateTime.parse("$formattedDate " + f.asrJamah));
          MaghribTime = DateFormat.Hm()
              .format(DateTime.parse("$formattedDate " + f.maghribJamah));
          IshaTime = DateFormat.Hm()
              .format(DateTime.parse("$formattedDate " + f.ishaJamah));
          Date = f.hijri_date;

          // Log prayer times for verification
          debugPrint("Prayer Times Loaded from backend API:");
          debugPrint("Fajr: $fajrTime");
          debugPrint("Dhuhr: $zuhrTime");
          debugPrint("Asr: $AsrTime");
          debugPrint("Maghrib: $MaghribTime");
          debugPrint("Isha: $IshaTime");
          debugPrint("Date: $Date");

          // After loading fresh prayer times, refresh notifications
          await refreshPrayerNotifications();
        },
      );

      return true;
    }
    return false;
  } catch (e) {
    debugPrint("Error fetching prayer times: $e");
    return false;
  }
}

Future<void> _sendData() async {
  try {
     Future.wait([
      HomeWidget.saveWidgetData<String>('_fajr_time', fajrTime.toString()),
      HomeWidget.saveWidgetData<String>('_dhuhr_time', zuhrTime.toString()),
      HomeWidget.saveWidgetData<String>('_asr_time', AsrTime.toString()),
      HomeWidget.saveWidgetData<String>(
          '_maghrib_time', MaghribTime.toString()),
      HomeWidget.saveWidgetData<String>('_isha_time', IshaTime.toString()),
      HomeWidget.saveWidgetData<String>('_date', Date.toString()),
    ]);
  } on PlatformException catch (exception) {
    debugPrint('Error Sending Data. $exception');
  }
}

// Function to refresh prayer notifications
Future<void> refreshPrayerNotifications() async {
  try {
    // Clear API cache to ensure we get fresh data
    final apiService = prayer_times_api.PrayerApiService();
    apiService.clearCache();

    // Reschedule notifications with fresh prayer time data
    await prayer_times.PrayerNotificationService().scheduleAllNotifications();

    debugPrint('Prayer notifications refreshed with latest prayer times');
  } catch (e) {
    debugPrint('Error refreshing prayer notifications: $e');
  }
}

Future<void> _updateWidget() async {
  try {
     HomeWidget.updateWidget(
        name: 'AppWidgetProvider',
        androidName: 'AppWidgetProvider',
        iOSName: 'HomeWidgetExample');
  } on PlatformException catch (exception) {
    debugPrint('Error Updating Widget. $exception');
  }
}

Future<void> _loadData() async {
  print("_loadData Clicked");
  // final int helloAlarmID = 0;
  // await AndroidAlarmManager.periodic(
  //     const Duration(seconds: 2), helloAlarmID, printHello,
  //     allowWhileIdle: true,
  //     rescheduleOnReboot: true,
  //     wakeup: true,
  //     exact: true);

  // try {
  //   return Future.wait([
  //     HomeWidget.getWidgetData<String>('_fajr_time', defaultValue: '0')
  //         .then((value) => _titleController.text = value),
  //     HomeWidget.getWidgetData<String>('message',
  //             defaultValue: 'Default Message')
  //         .then((value) => _messageController.text = value),
  //   ]);
  // } on PlatformException catch (exception) {
  //   debugPrint('Error Getting Data. $exception');
  // }
}

Future<void> _sendAndUpdate() async {
  await GetPrayerTime();
  await _sendData();
  await _updateWidget();
}

// void _startBackgroundUpdate() {
//   Workmanager().registerPeriodicTask('1', 'widgetBackgroundUpdate',
//       frequency: Duration(minutes: 15));
// }

void _stopBackgroundUpdate() {
  Workmanager().cancelByUniqueName('2');
}

setupWidgetData() async {
  await GetPrayerTime();
  // await _loadData();
  await _updateWidget();

  if (DateTime.now().isBefore(DateTime.parse("$formattedDate " + AsrTime))) {
    print("Date is");
    print("Playing Ringtone");
  } else {
    print("Not Playing Ringtone");
        print("Date is");

  }

  // if (DateTime.now().isBefore(DateTime.parse("$formattedDate " + AsrTime))) {
  //   print("Playing Ringtone");
  // } else {
  //   print("Not Playing Ringtone");
  // }
}

// var formatter = new DateFormat('yyyy-MM-dd');
// String formattedDate = formatter.format(now);

// void widgetBackgroundUpdate() {
//   print("Hurrayyy Task is in Background");
// }

// void setupWidgets() {
//   setupWidgetData();
// }

const fetchBackground = "fetchBackground";
const setupWidgets = "setupWidgets";

void callbackDispatcher() {
  // Workmanager().executeTask((taskName, inputData) {
  //   // setupWidgetData();
  //   // print("Asrr TIme $AsrTime");

  //   return Future.wait<bool>([
  //     setupWidgetData()

  //     // HomeWidget.saveWidgetData(
  //     //   'title',
  //     //   'Updated from Background',
  //     // ),
  //     // HomeWidget.saveWidgetData(
  //     //   'message',
  //     //   '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}',
  //     // ),
  //     // HomeWidget.updateWidget(
  //     //   name: 'AppWidgetProvider',
  //     //   androidName: 'AppWidgetProvider',
  //     //   iOSName: 'PrayerTime',
  //     // ),
  //   ]).then((value) {
  //     return !value.contains(false);
  //   });
  // });

  Workmanager().executeTask((task, inputData) async {
    switch (task) {
      // case fetchBackground:
      //   print("Hurrayyyyyyyyyyy Background Task");
      //   break;

      case setupWidgets:
        await GetPrayerTime();
        // await _loadData();
        await _sendData();
        await _updateWidget();

        // Also refresh prayer notifications to ensure they are accurate
        await refreshPrayerNotifications();

        if (DateTime.now()
            .isBefore(DateTime.parse("$formattedDate " + AsrTime))) {
          print("Playing Ringtone");
          print("Date is ${formattedDate.toString()}");
        } else {
          print("Not Playing Ringtone");
          print("Date is ${formattedDate.toString()}");
        }
        break;
    }
    return Future.value(true);
  });
}

/// Called when Doing Background Work initiated from Widget

// void printHello() {
//   DateTime now = DateTime.now();

//   print("Now is $now");
//   print("AsrTime is ${AsrTime}");

//   if (now.isBefore(DateTime.parse("$formattedDate " + AsrTime))) {
//     FlutterRingtonePlayer.playNotification();

//     print("Playing Ringtone");
//   } else {
//     print("Not Playing Ringtone");
//   }

//   // FlutterRingtonePlayer.playAlarm();

//   // final DateTime now = DateTime.now();
//   // final int isolateId = Isolate.current.hashCode;
//   // print("[$now] Hello, world! isolate=${isolateId} function='$printHello'");
// }

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize GetX controllers
  Get.put(QuranSettingsController(), permanent: true);
  Get.put(QuranService(), permanent: true);

  // Initialize prayer notification service
  await prayer_times.PrayerNotificationService().initialize();
  await prayer_reminders.PrayerNotificationService().initialize();

  // Initialize notification listeners
  await AwesomeNotifications().setListeners(
    onActionReceivedMethod: NotificationController.onActionReceivedMethod,
    onNotificationCreatedMethod: NotificationController.onNotificationCreatedMethod,
    onNotificationDisplayedMethod: NotificationController.onNotificationDisplayedMethod,
    onDismissActionReceivedMethod: NotificationController.onDismissActionReceivedMethod,
  );

  // Initialize workmanager for background tasks
  Workmanager().initialize(callbackDispatcher,
      isInDebugMode:
          true // If enabled it will post a notification whenever the task is running. Handy for debugging tasks
      );

  // Register periodic task to refresh prayer times and notifications
  Workmanager().registerPeriodicTask('2', setupWidgets,
      frequency: Duration(minutes: 15),
      constraints: Constraints(networkType: NetworkType.connected));

  // Schedule immediate refresh of prayer notifications
  refreshPrayerNotifications();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => JournalController()),
        // Add other providers if needed
      ],
      child: GetMaterialApp(
        debugShowCheckedModeBanner: false,
        navigatorKey: NotificationController.navigatorKey,
        theme: AppTheme.lightTheme,
        routes: {
          '/prayerReminders': (context) => PrayerReminderSettingsScreen(),
          '/prayerDashboard': (context) => PrayerReminderDashboard(),
          '/notificationHistory': (context) => PrayerNotificationHistoryScreen(),
          '/dailyHadith': (context) => HadithCollectionScreen(),
          '/arabicWord': (context) => WordCollectionScreen(),
          // Add Quran routes
          '/quran': (context) => const QuranModule(),
          '/quran/continue': (context) => const QuranModule(continueReading: true),
          // You can also use QuranRoutes.getRoutes() to get all routes
        },
        home: SplashView(
          done: Done(MyApp()),
          logo: Image.asset("assets/splash.png"),
          backgroundColor: Colors.white,
        ),
      ),
    ),
  );

  // Workmanager().registerPeriodicTask('1', fetchBackground,
  //     initialDelay: Duration(minutes: 15),
  //     frequency: Duration(minutes: 15),
  //     constraints: Constraints(networkType: NetworkType.connected));
  // AndroidAlarmManager.initialize();
}

/// Notification controller to handle notification events
class NotificationController {
  /// Global navigator key for navigation from notifications
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Use this method to detect when a new notification or a schedule is created
  @pragma('vm:entry-point')
  static Future<void> onNotificationCreatedMethod(ReceivedNotification receivedNotification) async {
    debugPrint('Notification created: ${receivedNotification.id}');
  }

  /// Use this method to detect every time that a new notification is displayed
  @pragma('vm:entry-point')
  static Future<void> onNotificationDisplayedMethod(ReceivedNotification receivedNotification) async {
    debugPrint('Notification displayed: ${receivedNotification.id}');
  }

  /// Use this method to detect if the user dismissed a notification
  @pragma('vm:entry-point')
  static Future<void> onDismissActionReceivedMethod(ReceivedAction receivedAction) async {
    debugPrint('Notification dismissed: ${receivedAction.id}');
  }

  /// Use this method to detect when the user taps on a notification or action button
  @pragma('vm:entry-point')
  static Future<void> onActionReceivedMethod(ReceivedAction receivedAction) async {
    debugPrint('Notification action received: ${receivedAction.id} - ${receivedAction.buttonKeyPressed}');

    // Handle the notification action
    await prayer_times.PrayerNotificationService().handleNotificationAction(receivedAction);

    // Navigate to a specific screen when notification is tapped
    if (receivedAction.actionType == ActionType.Default) {
      navigatorKey.currentState?.pushNamed('/prayerReminders');
    }
  }

  /// Create a test notification to verify everything is working
  static Future<void> createTestNotification() async {
    await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: 999,
        channelKey: 'prayer_notifications',
        title: 'Test Notification',
        body: 'This is a test notification to verify that notifications are working correctly.',
        notificationLayout: NotificationLayout.Default,
        category: NotificationCategory.Message,
      ),
      actionButtons: [
        NotificationActionButton(
          key: 'DISMISS',
          label: 'Dismiss',
          autoDismissible: true,
        ),
      ],
    );
  }

  /// Open notification settings
  static Future<void> openNotificationSettings() async {
    await AwesomeNotifications().showNotificationConfigPage();
  }
}

//  HomeWidget.setAppGroupId('com.imranashraf.masjidsunnah');
//      HomeWidget.registerBackgroundCallback(backgroundCallback);

// HomeWidget.registerBackgroundCallback(backgroundCallback);
// OneSignal.shared.setAppId("************************************");
// OneSignal.shared.promptUserForPushNotificationPermission().then((accepted) {
//     print("Accepted permission: $accepted");
// });

class QiblahDirectionRoute extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ListTile(
      // leading: Icon(
      //   Icons.location_on_outlined,
      //   color: Colors.blue,
      // ),
      leading: Image.asset('assets/qiblah.png', width: 32,),
      title: Text('Qiblah Direction',
          style: GoogleFonts.roboto(fontWeight: FontWeight.w600, fontSize: 16)),
          subtitle: Text(
                    "Find Qiblah Direction any where",
                    style: GoogleFonts.roboto(
                        fontWeight: FontWeight.w600, fontSize: 10),
                  ),
      onTap: () => {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (BuildContext context) =>  QiblahDirectionPage(),
          ),
        ),
      },
    );
  }
}

class ZakatCalculatorRoute extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: FaIcon(FontAwesomeIcons.handHoldingDollar, color: Colors.grey),
      title: Text('Zakat Calculator',
          style: GoogleFonts.roboto(fontWeight: FontWeight.w600, fontSize: 16)),
      subtitle: Text(
        "Calculate your Zakat & Sadaqah",
        style: GoogleFonts.roboto(
            fontWeight: FontWeight.w600, fontSize: 10),
      ),
      onTap: () => {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (BuildContext context) => ZakatCalculator(),
          ),
        ),
      },
    );
  }
}

class HomeWidget2 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {

    return ListTile(
      leading: Icon(
        Icons.home,
        color: Colors.grey,
      ),


      title: Text(
        'Home',
        style: GoogleFonts.roboto(fontWeight: FontWeight.w600, fontSize: 16),
      ),
      onTap: () => {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (BuildContext context) => new MyApp(),
          ),
        ),
      },
    );
  }
}

class AboutWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Icon(
        Icons.person,
        color: Colors.grey,
      ),
      title: Text(
        'About us',
        style: GoogleFonts.roboto(fontWeight: FontWeight.w600, fontSize: 16),
      ),
      onTap: () => {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (BuildContext context) =>  AboutMeApp(),
          ),
        ),
      },
    );
  }
}

class MyApp extends StatelessWidget {
   MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      extendBodyBehindAppBar: true,
      drawer: _buildDrawer(context),
      appBar: _buildAppBar(context) as PreferredSizeWidget,
      body: _buildBody(),
      bottomNavigationBar: _buildBottomNavigationBar(context),
    );
  }

  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 20,
              offset: Offset(0, 10),
            ),
          ],
        ),
        child: ListView(
          padding: EdgeInsets.only(bottom: 100),
          children: <Widget>[
            _buildDrawerHeader(),

            // Main Features
            _buildDrawerSection(
              context,
              [
                _buildDrawerItem(
                  context,
                  'Home',
                  FontAwesomeIcons.house,
                  () => Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(builder: (context) => HomePage()),
                  ),
                ),
                _buildDivider(),
                _buildDrawerItem(
                  context,
                  'Quran',
                  FontAwesomeIcons.bookQuran,
                  () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => QuranModule()),
                  ),
                ),
                _buildDivider(),
                _buildDrawerItem(
                  context,
                  'Duas',
                  FontAwesomeIcons.handsPraying,
                  () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => duaCategory()),
                  ),
                ),
              ],
            ),

            // Islamic Knowledge
            _buildDrawerSectionWithTitle(context, 'Islamic Knowledge', [
              _buildDrawerItem(
                context,
                'Daily Hadith',
                FontAwesomeIcons.bookOpen,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => HadithCollectionScreen()),
                ),
              ),
              _buildDivider(),
              _buildDrawerItem(
                context,
                'Arabic Word of the Day',
                FontAwesomeIcons.language,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => WordCollectionScreen()),
                ),
              ),
            ]),

            // Prayer Features
            _buildDrawerSectionWithTitle(context, 'Prayer Features', [
              // _buildDrawerItem(
              //   context,
              //   'Prayer Times',
              //   FontAwesomeIcons.clock,
              //   () => Navigator.push(
              //     context,
              //     MaterialPageRoute(builder: (context) => PrayerTimesScreen()),
              //   ),
              // ),
              // _buildDivider(),
              // _buildDrawerItem(
              //   context,
              //   'Prayer Dashboard',
              //   FontAwesomeIcons.gauge,
              //   () => Navigator.push(
              //     context,
              //     MaterialPageRoute(builder: (context) => PrayerReminderDashboard()),
              //   ),
              // ),
              _buildDivider(),
              _buildDrawerItem(
                context,
                'Prayer Reminders',
                FontAwesomeIcons.bell,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => PrayerReminderSettingsScreen()),
                ),
              ),
              _buildDivider(),
              _buildDrawerItem(
                context,
                'Notification History',
                FontAwesomeIcons.clockRotateLeft,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => PrayerNotificationHistoryScreen()),
                ),
              ),
            ]),

            // Spiritual Growth
            // Tools
            _buildDrawerSectionWithTitle(context, 'Tools', [
              if (Platform.isAndroid)
                _buildDrawerItem(
                  context,
                  'Qiblah Direction',
                  FontAwesomeIcons.compass,
                  () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => QiblahDirectionPage()),
                  ),
                ),
              if (Platform.isAndroid) _buildDivider(),
              _buildDrawerItem(
                context,
                'Zakat Calculator',
                FontAwesomeIcons.calculator,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => ZakatCalculator()),
                ),
              ),
            ]),

            // About & Support
            _buildDrawerSectionWithTitle(context, 'About & Support', [
              _buildDrawerItem(
                context,
                'About Us',
                FontAwesomeIcons.circleInfo,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => AboutMeApp()),
                ),
              ),
              _buildDivider(),
              if (Platform.isAndroid)
                _buildDrawerItem(
                  context,
                  'Rate App',
                  FontAwesomeIcons.star,
                  () => _launchURL(
                    Platform.isAndroid
                      ? "https://play.google.com/store/apps/details?id=com.masjidsunnah.masjidsunnah"
                      : "https://apps.apple.com/us/app/id1609222737"
                  ),
                ),
              if (Platform.isAndroid) _buildDivider(),
              _buildDrawerItem(
                context,
                'Contact Us',
                FontAwesomeIcons.envelope,
                () => _launchURL("mailto:<EMAIL>"),
              ),
            ]),

            // Add Spiritual Mood Journal to the drawer
            _buildDrawerSectionWithTitle(context, 'Spiritual Growth', [
              _buildDrawerItem(
                context,
                'Spiritual Mood Journal',
                FontAwesomeIcons.bookOpen,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => JournalListScreen()),
                ),
              ),
              _buildDivider(),
              _buildDrawerItem(
                context,
                'Dua Journal',
                FontAwesomeIcons.solidNoteSticky,
                () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => DuaJournalMain()),
                ),
              ),
              _buildDivider(),
              // Other spiritual growth items
            ]),

            // App Version
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'App Version 2.0.1',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerHeader() {
    return DrawerHeader(
      padding: EdgeInsets.all(0),
      margin: EdgeInsets.all(0),
      child: Container(
        height: 500,
        padding: EdgeInsets.only(bottom: 0, top: 0),
        margin: EdgeInsets.all(0),
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage("assets/pattern.jpg"),
            fit: BoxFit.cover,
          ),
        ),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 24, horizontal: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(URLs().colorScheme).withOpacity(0.9),
                Color(URLs().colorScheme),
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 70,
                height: 70,
                padding: EdgeInsets.all(0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(15),
                  child: Image(
                    image: AssetImage('assets/logo2.png'),
                  ),
                ),
              ),
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  "Masjid Sunnah Nelson",
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
              SizedBox(height: 8),
              Text(
                "Islamic Center & Community Hub",
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.white.withOpacity(0.9),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDrawerSection(BuildContext context, List<Widget> items) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(children: items),
    );
  }

  Widget _buildDrawerSectionWithTitle(BuildContext context, String title, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
              Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Color(URLs().colorScheme),
              letterSpacing: 0.5,
            ),
          ),
        ),
        _buildDrawerSection(context, items),
      ],
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      scrolledUnderElevation: 0,
      leading: Padding(
        padding: const EdgeInsets.only(left: 16, top: 8, bottom: 8),
        child: Builder(
          builder: (BuildContext context) {
            return InkWell(
              onTap: () {
                Scaffold.of(context).openDrawer();
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Color(URLs().colorScheme).withOpacity(0.15),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Color(URLs().colorScheme).withOpacity(0.2),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.03),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  FontAwesomeIcons.barsStaggered,
                  color: Color(URLs().colorScheme),
                  size: 18,
                ),
              ),
            );
          },
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
          child: InkWell(
            onTap: () {
              // Navigate to notification history
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PrayerNotificationHistoryScreen(),
                ),
              );
            },
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Color(URLs().colorScheme).withOpacity(0.15),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Color(URLs().colorScheme).withOpacity(0.2),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.03),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Stack(
                alignment: Alignment.topRight,
                children: [
                  Icon(
                    FontAwesomeIcons.bell,
                    color: Color(URLs().colorScheme),
                    size: 18,
                  ),
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 1,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
      title: null,
      centerTitle: false,
    );
  }

  Widget _buildBody() {
    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage("assets/bg.jpeg"),
          fit: BoxFit.cover,
          colorFilter: ColorFilter.mode(
            Colors.white.withOpacity(0.9),
            BlendMode.lighten,
          ),
        ),
      ),
      child: HomePage(),
    );
  }

  Widget _buildBottomNavigationBar(BuildContext context) {
    return ModernBottomNav(
      currentIndex: 0,
      onTap: (index) {
        switch (index) {
          case 0:
            // Already on home
            break;
          case 1:
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => allEventsPage()),
            );
            break;
          case 2:
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => duaCategory()),
            );
            break;
          case 3:
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => QuranModule()),
            );
            break;
          case 4:
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const HadithHomeScreen()),
            );
            break;
        }
      },
      items: [
        ModernBottomNavItem(
          icon: FontAwesomeIcons.house,
          label: 'Home',
        ),
        ModernBottomNavItem(
          icon: FontAwesomeIcons.calendarDays,
          label: 'Events',
        ),
        ModernBottomNavItem(
          icon: FontAwesomeIcons.handsPraying,
          label: 'Duas',
        ),
        ModernBottomNavItem(
          icon: FontAwesomeIcons.bookQuran,
          label: 'Quran',
        ),
        ModernBottomNavItem(
          icon: FontAwesomeIcons.bookOpen,
          label: 'Hadith',
        ),
      ],
    );
  }
}

_launchURL(_url) async =>
    await canLaunch(_url) ? await launch(_url) : throw 'Could not launch $_url';

class MyHomePage extends StatefulWidget {
  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _messageController = TextEditingController();

   _launchedFromWidget( uri) {
    if (uri != null) {
      showDialog(
          context: context,
          builder: (buildContext) => AlertDialog(
                title: Text('App started from HomeScreenWidget'),
                content: Text('Here is the URI: $uri'),
              ));
    }
  }

   _checkForWidgetLaunch() {
    HomeWidget.initiallyLaunchedFromHomeWidget().then(_launchedFromWidget);
  }

  @override
  void initState() {
    super.initState();
    HomeWidget.setAppGroupId('group.com.imranashraf.masjidsunnah');

    // Workmanager().registerPeriodicTask(
    //   "1",
    //   fetchBackground,
    //   frequency: Duration(minutes: 15),
    // );
    // // HomeWidget.registerBackgroundCallback(backgroundCallback);
    // setupWidgetData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _checkForWidgetLaunch();
    HomeWidget.widgetClicked.listen(_launchedFromWidget);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TabBarView(
      children: <Widget>[
        HomePage(),
        allEventsPage(),
        messagesPage(),
        duaCategory(),
        QuranModule()
      ],
    );
  }
}

// Add this helper method for drawer items
Widget _buildDrawerItem(BuildContext context, String title, IconData icon, VoidCallback onTap) {
  return Container(
    margin: EdgeInsets.symmetric(vertical: 4),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(12),
    ),
    child: ListTile(
      leading: Container(
        padding: EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Color(URLs().colorScheme).withOpacity(0.1),
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: Color(URLs().colorScheme).withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: Color(URLs().colorScheme),
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: GoogleFonts.poppins(
          fontSize: 15,
          fontWeight: FontWeight.w500,
          color: Colors.grey[800],
          letterSpacing: 0.3,
        ),
      ),
      trailing: Container(
        padding: EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.arrow_forward_ios,
          size: 12,
          color: Colors.grey[500],
        ),
      ),
      onTap: onTap,
      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      tileColor: Colors.transparent,
      hoverColor: Color(URLs().colorScheme).withOpacity(0.05),
      selectedTileColor: Color(URLs().colorScheme).withOpacity(0.1),
    ),
  );
}

Widget _buildDivider() {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 16),
    child: Divider(
      color: Colors.grey[200],
      height: 1,
      thickness: 1,
    ),
  );
}

// Add this class for PrayerTimesScreen if it doesn't exist
class PrayerTimesScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Prayer Times',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: Color(URLs().colorScheme),
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPrayerTimesHeader(),
            SizedBox(height: 24),
            _buildPrayerTimesList(),
          ],
        ),
      ),
    );
  }

  Widget _buildPrayerTimesHeader() {
    final now = DateTime.now();
    final formatter = DateFormat('EEEE, MMMM d, yyyy');
    final formattedDate = formatter.format(now);

    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Color(URLs().colorScheme),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Color(URLs().colorScheme).withOpacity(0.3),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            formattedDate,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Prayer Times for Nelson, UK',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.white.withOpacity(0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrayerTimesList() {
    return Column(
      children: [
        _buildPrayerTimeItem('Fajr', '5:30 AM', true),
        _buildPrayerTimeItem('Sunrise', '6:45 AM', false),
        _buildPrayerTimeItem('Dhuhr', '1:15 PM', false),
        _buildPrayerTimeItem('Asr', '4:30 PM', false),
        _buildPrayerTimeItem('Maghrib', '7:15 PM', false),
        _buildPrayerTimeItem('Isha', '8:45 PM', false),
      ],
    );
  }

  Widget _buildPrayerTimeItem(String name, String time, bool isNext) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isNext ? Color(URLs().colorScheme).withOpacity(0.1) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isNext ? Color(URLs().colorScheme).withOpacity(0.3) : Colors.grey[200]!,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isNext ? Color(URLs().colorScheme) : Colors.grey[100],
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getPrayerIcon(name),
              color: isNext ? Colors.white : Colors.grey[600],
              size: 24,
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isNext ? Color(URLs().colorScheme) : Colors.grey[800],
                  ),
                ),
                if (isNext)
                  Text(
                    'Next Prayer',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Color(URLs().colorScheme),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
          ),
          Text(
            time,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isNext ? Color(URLs().colorScheme) : Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getPrayerIcon(String prayer) {
    switch (prayer) {
      case 'Fajr':
        return FontAwesomeIcons.sun;
      case 'Sunrise':
        return FontAwesomeIcons.solidSun;
      case 'Dhuhr':
        return FontAwesomeIcons.solidCircle;
      case 'Asr':
        return FontAwesomeIcons.cloudSun;
      case 'Maghrib':
        return FontAwesomeIcons.solidMoon;
      case 'Isha':
        return FontAwesomeIcons.moon;
      default:
        return FontAwesomeIcons.mosque;
    }
  }
}
