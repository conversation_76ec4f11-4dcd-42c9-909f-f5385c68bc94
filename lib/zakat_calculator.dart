import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/models/zakat_model.dart';
import 'package:new_islamic_app_uk/widgets/zakat_calculator/calculator_tab.dart';
import 'package:new_islamic_app_uk/widgets/zakat_calculator/summary_tab.dart';
import 'package:new_islamic_app_uk/widgets/zakat_calculator/guide_tab.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:flutter_animate/flutter_animate.dart';

class ZakatCalculator extends StatefulWidget {
  const ZakatCalculator({Key? key}) : super(key: key);

  @override
  _ZakatCalculatorState createState() => _ZakatCalculatorState();
}

class _ZakatCalculatorState extends State<ZakatCalculator> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  final ZakatModel _zakatModel = ZakatModel();
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Add listener to rebuild when tab changes
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {});
      }
    });
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'Zakat Calculator',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Color(URLs().colorScheme),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 20),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // Custom tab bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            height: 60,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(30),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                color: Color(URLs().colorScheme),
                boxShadow: [
                  BoxShadow(
                    color: Color(URLs().colorScheme).withOpacity(0.3),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              labelColor: Colors.white,
              unselectedLabelColor: Colors.grey[600],
              labelStyle: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
              unselectedLabelStyle: GoogleFonts.poppins(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
              tabs: const [
                Tab(text: 'Calculate'),
                Tab(text: 'Summary'),
                Tab(text: 'Guide'),
              ],
            ),
          ),
          
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                CalculatorTab(
                  zakatModel: _zakatModel,
                  formKey: _formKey,
                  onCalculate: () {
                    if (_formKey.currentState!.validate()) {
                      _zakatModel.calculateNisabThresholds();
                      _tabController.animateTo(1); // Switch to summary tab
                    }
                  },
                ),
                SummaryTab(
                  zakatModel: _zakatModel,
                  onEditValues: () {
                    _tabController.animateTo(0); // Switch back to calculator tab
                  },
                ),
                GuideTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 