import 'package:flutter/material.dart';
import 'package:new_islamic_app_uk/models/zakat_model.dart';
import 'package:new_islamic_app_uk/widgets/zakat_calculator/calculator_tab.dart';
import 'package:new_islamic_app_uk/widgets/zakat_calculator/summary_tab.dart';
import 'package:new_islamic_app_uk/widgets/zakat_calculator/guide_tab.dart';
import 'package:new_islamic_app_uk/theme/app_theme.dart';
import 'package:new_islamic_app_uk/theme/app_colors.dart';
import 'package:new_islamic_app_uk/theme/app_text_styles.dart';
import 'package:new_islamic_app_uk/widgets/common/modern_app_bar.dart';

class ZakatCalculator extends StatefulWidget {
  const ZakatCalculator({Key? key}) : super(key: key);

  @override
  _ZakatCalculatorState createState() => _ZakatCalculatorState();
}

class _ZakatCalculatorState extends State<ZakatCalculator> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  final ZakatModel _zakatModel = ZakatModel();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Add listener to rebuild when tab changes
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: ModernAppBar(
        title: 'Zakat Calculator',
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Modern tab bar
          Container(
            margin: const EdgeInsets.all(AppTheme.spacing16),
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant,
              borderRadius: BorderRadius.circular(AppTheme.radiusCircular),
            ),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(AppTheme.radiusCircular),
                color: AppColors.primary,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.3),
                    blurRadius: AppTheme.elevationMedium * 2,
                    offset: const Offset(0, AppTheme.elevationMedium),
                  ),
                ],
              ),
              labelColor: Colors.white,
              unselectedLabelColor: AppColors.textSecondary,
              labelStyle: AppTextStyles.labelLarge,
              unselectedLabelStyle: AppTextStyles.labelLarge,
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              tabs: const [
                Tab(text: 'Calculate'),
                Tab(text: 'Summary'),
                Tab(text: 'Guide'),
              ],
            ),
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                CalculatorTab(
                  zakatModel: _zakatModel,
                  formKey: _formKey,
                  onCalculate: () {
                    if (_formKey.currentState!.validate()) {
                      _zakatModel.calculateNisabThresholds();
                      _tabController.animateTo(1); // Switch to summary tab
                    }
                  },
                ),
                SummaryTab(
                  zakatModel: _zakatModel,
                  onEditValues: () {
                    _tabController.animateTo(0); // Switch back to calculator tab
                  },
                ),
                const GuideTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}