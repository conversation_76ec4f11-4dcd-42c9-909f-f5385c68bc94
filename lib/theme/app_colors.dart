import 'package:flutter/material.dart';

/// Modern color palette based on the design images
class AppColors {
  // Primary Colors - Teal/Green theme from the images
  static const Color primary = Color(0xFF4A9B8E);
  static const Color primaryLight = Color(0xFF6BB6A9);
  static const Color primaryDark = Color(0xFF2E7D6B);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF5A7C76);
  static const Color secondaryLight = Color(0xFF7A9C96);
  static const Color secondaryDark = Color(0xFF3A5C56);
  
  // Accent Colors
  static const Color accent = Color(0xFF8FBC8F);
  static const Color accentLight = Color(0xFFAFDCAF);
  static const Color accentDark = Color(0xFF6F9C6F);
  
  // Background Colors
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF1A1A1A);
  static const Color textSecondary = Color(0xFF666666);
  static const Color textTertiary = Color(0xFF999999);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Card and Component Colors
  static const Color cardBackground = Color(0xFFFFFFFF);
  static const Color cardShadow = Color(0x1A000000);
  static const Color divider = Color(0xFFE0E0E0);
  static const Color border = Color(0xFFE5E5E5);
  
  // Category Colors (for different sections)
  static const List<Color> categoryColors = [
    Color(0xFF4A9B8E), // Teal
    Color(0xFF7B68EE), // Medium Slate Blue
    Color(0xFF20B2AA), // Light Sea Green
    Color(0xFF9370DB), // Medium Purple
    Color(0xFF3CB371), // Medium Sea Green
    Color(0xFF6495ED), // Cornflower Blue
    Color(0xFF40E0D0), // Turquoise
    Color(0xFF8A2BE2), // Blue Violet
    Color(0xFF00CED1), // Dark Turquoise
    Color(0xFF9932CC), // Dark Orchid
  ];
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient accentGradient = LinearGradient(
    colors: [accent, accentLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Helper method to get category color by index
  static Color getCategoryColor(int index) {
    return categoryColors[index % categoryColors.length];
  }
  
  // Helper method to get color with opacity
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
}
