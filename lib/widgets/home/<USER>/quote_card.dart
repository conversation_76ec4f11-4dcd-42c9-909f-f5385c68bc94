import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:share_plus/share_plus.dart';
import 'package:new_islamic_app_uk/URLs.dart';

class QuoteData {
  final String text;
  final String source;
  
  QuoteData(this.text, this.source);
  
  // Static method to get a daily quote
  static QuoteData getDailyQuote() {
    final quotes = [
      QuoteData(
        "The best among you are those who have the best character.",
        "Prophet Muhammad ﷺ"
      ),
      QuoteData(
        "Verily, with hardship comes ease.",
        "Quran 94:6"
      ),
      <PERSON>uote<PERSON><PERSON>(
        "Speak good or remain silent.",
        "Prophet Muhammad ﷺ"
      ),
      <PERSON>uote<PERSON><PERSON>(
        "The most beloved of deeds to <PERSON> are those that are most consistent, even if they are small.",
        "<PERSON> Muhammad ﷺ"
      ),
      <PERSON>uote<PERSON><PERSON>(
        "Allah does not look at your appearance or your possessions; but He looks at your heart and your deeds.",
        "Prophet Muhammad ﷺ"
      ),
      <PERSON>uo<PERSON><PERSON><PERSON>(
        "Be in this world as if you were a stranger or a traveler.",
        "Prophet Muhammad ﷺ"
      ),
      <PERSON><PERSON><PERSON><PERSON><PERSON>(
        "Indeed, <PERSON> will not change the condition of a people until they change what is in themselves.",
        "Quran 13:11"
      ),
      <PERSON>uote<PERSON>ata(
        "The strong person is not the one who can wrestle someone else down. The strong person is the one who can control himself when he is angry.",
        "Prophet <PERSON> ﷺ"
      ),
      QuoteData(
        "Whoever believes in <PERSON> and the Last Day, let him speak good or remain silent.",
        "Prophet <PERSON> ﷺ"
      ),
      QuoteData(
        "And when My servants ask you concerning Me - indeed I am near. I respond to the invocation of the supplicant when he calls upon Me.",
        "Quran 2:186"
      ),
      QuoteData(
        "The believer's shade on the Day of Resurrection will be his charity.",
        "Prophet Muhammad ﷺ"
      ),
      QuoteData(
        "The cure for ignorance is to question.",
        "Imam Ali (RA)"
      ),
      QuoteData(
        "He who has health has hope, and he who has hope has everything.",
        "Arabic Proverb"
      ),
      QuoteData(
        "Do not lose hope, nor be sad. You will surely be victorious if you are true believers.",
        "Quran 3:139"
      ),
      QuoteData(
        "The reward of deeds depends upon the intentions.",
        "Prophet Muhammad ﷺ"
      ),
    ];
    
    // Get a quote based on the day of the year to ensure it changes daily
    final dayOfYear = DateTime.now().difference(DateTime(DateTime.now().year, 1, 1)).inDays;
    final index = dayOfYear % quotes.length;
    
    return quotes[index];
  }
}

class QuoteCard extends StatefulWidget {
  const QuoteCard({Key? key}) : super(key: key);

  @override
  State<QuoteCard> createState() => _QuoteCardState();
}

class _QuoteCardState extends State<QuoteCard> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  final urls = URLs();
  bool _isExpanded = false;
  late QuoteData _quote;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );
    _quote = QuoteData.getDailyQuote();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleQuoteExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  void _shareQuote() {
    final String shareText = 
      "${_quote.text}\n\n- ${_quote.source}\n\nShared from Islamic App";
    
    try {
      Share.share(shareText);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sharing not available'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleQuoteExpansion,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white.withOpacity(0.18),
              Colors.white.withOpacity(0.08),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withOpacity(0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        FontAwesomeIcons.bookOpen,
                        color: Colors.white,
                        size: 14,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Today\'s Inspiration',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color: Colors.white.withOpacity(0.8),
                    size: 22,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 14),
            AnimatedCrossFade(
              firstChild: Text(
                _quote.text,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: Colors.white.withOpacity(0.9),
                  height: 1.5,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              secondChild: Text(
                _quote.text,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: Colors.white.withOpacity(0.9),
                  height: 1.5,
                ),
              ),
              crossFadeState: _isExpanded 
                  ? CrossFadeState.showSecond 
                  : CrossFadeState.showFirst,
              duration: const Duration(milliseconds: 300),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (_isExpanded)
                  _buildActionButton(
                    icon: FontAwesomeIcons.share,
                    label: "Share",
                    onTap: _shareQuote,
                  ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    _quote.source,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 600.ms, delay: 400.ms);
  }
  
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.15),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 12,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
} 