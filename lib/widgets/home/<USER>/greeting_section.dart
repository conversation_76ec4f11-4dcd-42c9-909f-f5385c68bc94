import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_animate/flutter_animate.dart';

class GreetingSection extends StatelessWidget {
  final String greeting;

  const GreetingSection({
    Key? key,
    required this.greeting,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Time of day icon
    final hour = DateTime.now().hour;
    IconData timeIcon;
    String timeOfDay;
    
    if (hour < 6) {
      timeIcon = FontAwesomeIcons.moon;
      timeOfDay = "Night";
    } else if (hour < 12) {
      timeIcon = FontAwesomeIcons.sun;
      timeOfDay = "Morning";
    } else if (hour < 18) {
      timeIcon = FontAwesomeIcons.cloudSun;
      timeOfDay = "Afternoon";
    } else {
      timeIcon = FontAwesomeIcons.solidMoon;
      timeOfDay = "Evening";
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.15),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                timeIcon,
                color: Colors.yellow.shade200,
                size: 18,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              timeOfDay,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.white70,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Text(
          greeting,
          style: GoogleFonts.poppins(
            fontSize: 24,
            fontWeight: FontWeight.w600,
            color: Colors.white,
            height: 1.2,
          ),
        ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.2, end: 0),
      ],
    );
  }
} 