import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/URLs.dart';

class HeaderBackground extends StatelessWidget {
  const HeaderBackground({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Stack(
      children: [
        // Background gradient
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(urls.colorScheme).withOpacity(0.95),
                Color(urls.colorScheme),
              ],
            ),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(30),
              bottomRight: Radius.circular(30),
            ),
          ),
        ),
        
        // Decorative elements
        _buildDecorativeCircle(
          top: -80,
          right: -80,
          size: 200,
          opacity: 0.08,
        ),
        
        _buildDecorativeCircle(
          bottom: 20,
          left: -60,
          size: 120,
          opacity: 0.08,
        ),
        
        // Decorative mosque icon
        Positioned(
          top: 60,
          right: 60,
          child: Icon(
            FontAwesomeIcons.mosque,
            size: 70,
            color: Colors.white.withOpacity(0.06),
          ),
        ),
        
        // Additional decorative elements for modern touch
        _buildDecorativeCircle(
          top: 120,
          left: 30,
          size: 60,
          opacity: 0.05,
        ),
        
        _buildDecorativeCircle(
          bottom: 80,
          right: 40,
          size: 90,
          opacity: 0.07,
        ),
        
        // Abstract shape for visual interest
        Positioned(
          bottom: -10,
          left: MediaQuery.of(context).size.width / 3,
          child: Container(
            width: 200,
            height: 40,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100),
              color: Colors.white.withOpacity(0.05),
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildDecorativeCircle({
    double? top,
    double? left,
    double? right,
    double? bottom,
    required double size,
    required double opacity,
  }) {
    return Positioned(
      top: top,
      left: left,
      right: right,
      bottom: bottom,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white.withOpacity(opacity),
        ),
      ),
    );
  }
} 