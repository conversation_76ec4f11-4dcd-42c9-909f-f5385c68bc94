import 'package:flutter/material.dart';
import 'package:new_islamic_app_uk/theme/app_theme.dart';
import 'package:new_islamic_app_uk/theme/app_colors.dart';
import 'package:new_islamic_app_uk/theme/app_text_styles.dart';

class SectionHeader extends StatelessWidget {
  final String title;
  final IconData? icon;
  final VoidCallback? onViewAll;

  const SectionHeader({
    Key? key,
    required this.title,
    this.icon,
    this.onViewAll,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing16,
        vertical: AppTheme.spacing12,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Circle with icon if provided
          if (icon != null) ...[
            Container(
              padding: const EdgeInsets.all(AppTheme.spacing8),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.1),
                    blurRadius: AppTheme.elevationLow * 2,
                    offset: const Offset(0, AppTheme.elevationLow),
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: AppColors.primary,
                size: AppTheme.iconSmall,
              ),
            ),
            const SizedBox(width: AppTheme.spacing12),
          ],

          // Main section title with flexible constraints
          Expanded(
            child: Text(
              title,
              style: AppTextStyles.titleLarge,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),

          // View All button if callback provided
          if (onViewAll != null) ...[
            const SizedBox(width: AppTheme.spacing8),
            InkWell(
              onTap: onViewAll,
              borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: AppColors.surfaceVariant,
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'View All',
                      style: AppTextStyles.labelMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacing4),
                    Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: AppColors.textSecondary,
                      size: AppTheme.iconSmall,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}