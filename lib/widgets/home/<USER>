import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';

class IslamicCalendar extends StatelessWidget {
  final String hijriDate;
  
  const IslamicCalendar({
    Key? key, 
    required this.hijriDate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    // Mock important dates for the month
    final importantDates = [
      {'date': '15', 'event': 'Middle of Sha\'ban', 'type': 'religious'},
      {'date': '21', 'event': 'Community Iftar', 'type': 'community'},
      {'date': '27', 'event': 'Laylatul Qadr', 'type': 'religious'},
    ];
    
    return Card(
      elevation: urls.cardElevation,
      shadowColor: Colors.black.withOpacity(0.05),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(urls.cardBorderRadius),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(urls.cardBorderRadius),
          color: Colors.white,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Color(urls.primaryColor).withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.calendar_month_rounded,
                    color: Color(urls.primaryColor),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Islamic Calendar',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(urls.textPrimary),
                        ),
                      ),
                      Text(
                        hijriDate,
                        style: GoogleFonts.poppins(
                          fontSize: 13,
                          color: Color(urls.textSecondary),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Calendar grid with current month's important dates
            _buildCalendarGrid(urls, importantDates),
            
            const SizedBox(height: 12),
            
            // Legend
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildLegendItem(urls, 'Religious Event', urls.primaryColor),
                const SizedBox(width: 16),
                _buildLegendItem(urls, 'Community Event', urls.accentColor),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildCalendarGrid(URLs urls, List<Map<String, String>> importantDates) {
    // Days of the week
    final weekDays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    
    return Column(
      children: [
        // Week days header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: weekDays.map((day) {
            final bool isFriday = day == 'Fri';
            return SizedBox(
              width: 32,
              child: Text(
                day,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: isFriday ? FontWeight.w600 : FontWeight.normal,
                  color: isFriday 
                      ? Color(urls.primaryColor) 
                      : Color(urls.textSecondary),
                ),
                textAlign: TextAlign.center,
              ),
            );
          }).toList(),
        ),
        
        const SizedBox(height: 8),
        
        // Calendar grid
        // This is a simplified example showing just one week
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            for (int i = 1; i <= 7; i++)
              _buildCalendarDay(urls, '${i + 14}', i == 5, importantDates),
          ],
        ),
        
        const SizedBox(height: 8),
        
        // Second week
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            for (int i = 1; i <= 7; i++)
              _buildCalendarDay(urls, '${i + 21}', i == 5, importantDates),
          ],
        ),
      ],
    );
  }
  
  Widget _buildCalendarDay(
    URLs urls, 
    String day, 
    bool isFriday, 
    List<Map<String, String>> importantDates
  ) {
    // Check if this day has an important event
    final important = importantDates.where((date) => date['date'] == day).toList();
    final hasEvent = important.isNotEmpty;
    final eventType = hasEvent ? important.first['type'] : null;
    
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: hasEvent 
            ? (eventType == 'religious' 
                ? Color(urls.primaryColor).withOpacity(0.2)
                : Color(urls.accentColor).withOpacity(0.2))
            : (isFriday 
                ? Color(urls.lightGray) 
                : Colors.transparent),
        border: isFriday && !hasEvent
            ? Border.all(color: Color(urls.primaryColor).withOpacity(0.3))
            : null,
      ),
      child: Center(
        child: Text(
          day,
          style: GoogleFonts.poppins(
            fontSize: 13,
            fontWeight: hasEvent || isFriday ? FontWeight.w600 : FontWeight.normal,
            color: hasEvent 
                ? (eventType == 'religious' 
                    ? Color(urls.primaryColor)
                    : Color(urls.accentColor))
                : (isFriday 
                    ? Color(urls.primaryColor) 
                    : Color(urls.textPrimary)),
          ),
        ),
      ),
    );
  }
  
  Widget _buildLegendItem(URLs urls, String label, int color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Color(color).withOpacity(0.2),
          ),
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 12,
            color: Color(urls.textSecondary),
          ),
        ),
      ],
    );
  }
} 