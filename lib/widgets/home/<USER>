import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:quran/quran.dart' as quran;
import 'package:audioplayers/audioplayers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:new_islamic_app_uk/features/quran/models/quran_models.dart';
import 'package:get/get.dart';
import 'package:new_islamic_app_uk/features/quran/services/quran_service.dart';
import 'package:new_islamic_app_uk/features/quran/screens/reader/reader_screen.dart';

class QuranVerseCard extends StatefulWidget {
  const QuranVerseCard({Key? key}) : super(key: key);

  @override
  State<QuranVerseCard> createState() => _QuranVerseCardState();
}

class _QuranVerseCardState extends State<QuranVerseCard> {
  bool _isBookmarked = false;
  bool _isPlaying = false;
  final AudioPlayer _audioPlayer = AudioPlayer();
  late QuranService _quranService;
  
  // Daily verse info
  late int _surahNumber;
  late int _verseNumber;
  late String _arabicText;
  late String _translation;
  late String _reference;
  String? _audioUrl;
  
  @override
  void initState() {
    super.initState();
    // Get the QuranService instance
    try {
      _quranService = Get.find<QuranService>();
    } catch (e) {
      _quranService = QuranService();
      Get.put(_quranService, permanent: true);
    }
    
    _loadDailyVerse();
    _checkIfBookmarked();
    
    // Set up audio player completion listener
    _audioPlayer.onPlayerComplete.listen((event) {
      if (mounted) {
        setState(() {
          _isPlaying = false;
        });
      }
    });
  }
  
  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
  
  void _loadDailyVerse() {
    // Generate verse based on day of year to ensure it changes daily
    final dayOfYear = DateTime.now().difference(DateTime(DateTime.now().year, 1, 1)).inDays;
    
    // Pick a surah based on the day 
    // Using modulo to cycle through some common short surahs
    final surahOptions = [1, 36, 55, 56, 67, 78, 93, 94, 95, 96, 97, 103, 105, 106, 108, 112, 113, 114];
    _surahNumber = surahOptions[dayOfYear % surahOptions.length];
    
    // Pick a verse number based on the day
    final verseCount = quran.getVerseCount(_surahNumber);
    _verseNumber = (dayOfYear % verseCount) + 1;
    
    // Get verse text
    _arabicText = quran.getVerse(_surahNumber, _verseNumber, verseEndSymbol: true);
    _translation = quran.getVerseTranslation(
      _surahNumber, 
      _verseNumber, 
      verseEndSymbol: false, 
      translation: quran.Translation.enSaheeh
    );
    
    // Get audio URL
    _audioUrl = quran.getAudioURLByVerse(_surahNumber, _verseNumber);
    
    // Create reference string
    final surahName = quran.getSurahNameEnglish(_surahNumber);
    _reference = 'Surah $surahName [${_surahNumber}:${_verseNumber}]';
  }
  
  Future<void> _checkIfBookmarked() async {
    try {
      // Get all bookmarks from the service
      final bookmarks = await _quranService.getBookmarks();
      
      // Check if current verse is in bookmarks
      setState(() {
        _isBookmarked = bookmarks.any((bookmark) => 
          bookmark.surahNumber == _surahNumber && 
          bookmark.verseNumber == _verseNumber
        );
      });
    } catch (e) {
      debugPrint('Error checking bookmarks: $e');
    }
  }
  
  Future<void> _toggleBookmark() async {
    try {
      final bookmarks = await _quranService.getBookmarks();
      
      if (_isBookmarked) {
        // Find the bookmark ID to remove
        final bookmarkToRemove = bookmarks.firstWhere(
          (bookmark) => 
            bookmark.surahNumber == _surahNumber && 
            bookmark.verseNumber == _verseNumber,
          orElse: () => throw Exception('Bookmark not found'),
        );
        
        // Remove the bookmark using its ID
        await _quranService.removeBookmark(bookmarkToRemove.id);
      } else {
        // Add a new bookmark
        await _quranService.addBookmark(
          _surahNumber,
          _verseNumber,
          note: 'Verse of the day: ${DateTime.now().toString().substring(0, 10)}',
        );
      }
      
      setState(() {
        _isBookmarked = !_isBookmarked;
      });
      
      // Show confirmation
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_isBookmarked 
              ? 'Verse bookmarked!' 
              : 'Bookmark removed'),
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error ${_isBookmarked ? 'removing' : 'adding'} bookmark: $e'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
  
  Future<void> _toggleAudio() async {
    if (_audioUrl == null) return;
    
    setState(() {
      _isPlaying = !_isPlaying;
    });
    
    if (_isPlaying) {
      // Play audio
      try {
        await _audioPlayer.play(UrlSource(_audioUrl!));
        
        // Show playing message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Audio is playing...'),
              behavior: SnackBarBehavior.floating,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } catch (e) {
        // Handle error
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error playing audio: ${e.toString()}'),
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 2),
            ),
          );
          setState(() {
            _isPlaying = false;
          });
        }
      }
    } else {
      // Stop audio
      await _audioPlayer.stop();
    }
  }
  
  void _navigateToQuranReader() {
    try {
      // Get the surah info for navigation
      final surah = _quranService.getAllSurahs().firstWhere(
        (s) => s.number == _surahNumber,
        orElse: () => SurahInfo(
          number: _surahNumber,
          nameArabic: quran.getSurahNameArabic(_surahNumber),
          nameEnglish: quran.getSurahNameEnglish(_surahNumber),
          nameTransliteration: quran.getSurahName(_surahNumber),
          versesCount: quran.getVerseCount(_surahNumber),
          placeOfRevelation: quran.getPlaceOfRevelation(_surahNumber),
        ),
      );
      
      // Navigate directly to the reader screen with the specific verse
      Navigator.of(context, rootNavigator: true).push(
        MaterialPageRoute(
          builder: (context) => QuranReaderScreen(
            surahInfo: surah,
            initialVerseNumber: _verseNumber,
          ),
        ),
      );
      
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Opening Quran...'),
          behavior: SnackBarBehavior.floating,
          duration: Duration(seconds: 1),
        ),
      );
    } catch (e) {
      // Try to use the basic navigation as fallback
      try {
        Navigator.of(context, rootNavigator: true).pushNamed('/quran');
        
        // Show message that we couldn't navigate to the specific verse
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Opening Quran (couldn\'t navigate to specific verse)'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      } catch (e) {
        // Handle error
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error navigating to Quran: ${e.toString()}'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
  
  void _shareVerse() {
    final String shareText = 
        "$_translation\n\n$_reference\n\nShared from Islamic App";
    
    Share.share(shareText);
  }

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Card(
      elevation: urls.cardElevation,
      shadowColor: Colors.black.withOpacity(0.05),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(urls.cardBorderRadius),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(urls.cardBorderRadius),
          color: Colors.white,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and title
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Color(urls.pastelPurple).withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.menu_book_rounded,
                    color: Color(urls.pastelPurple),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Verse of the Day',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(urls.textPrimary),
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(
                    Icons.share_outlined,
                    color: Color(urls.textSecondary),
                    size: 20,
                  ),
                  onPressed: _shareVerse,
                  tooltip: 'Share verse',
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Decorative element
            Container(
              width: 80,
              height: 4,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                gradient: LinearGradient(
                  colors: [
                    Color(urls.pastelPurple),
                    Color(urls.pastelPurple).withOpacity(0.3),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Arabic Text with long press to copy
            GestureDetector(
              onLongPress: () {
                Clipboard.setData(ClipboardData(text: _arabicText));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Arabic text copied to clipboard'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
              child: Center(
                child: Text(
                  _arabicText,
                  textDirection: TextDirection.rtl,
                  style: GoogleFonts.scheherazadeNew(
                    fontSize: 24,
                    height: 1.6,
                    fontWeight: FontWeight.w500,
                    color: Color(urls.textPrimary),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Translation with long press to copy
            GestureDetector(
              onLongPress: () {
                Clipboard.setData(ClipboardData(text: _translation));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Translation copied to clipboard'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
              child: Text(
                _translation,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  height: 1.6,
                  color: Color(urls.textSecondary),
                ),
                textAlign: TextAlign.start,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Reference
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Color(urls.lightGray),
                borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
              ),
              child: Text(
                _reference,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(urls.textSecondary),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Flexible(
                  child: _buildActionButton(
                    urls,
                    'Read More',
                    Icons.book_outlined,
                    urls.primaryColor,
                    onTap: _navigateToQuranReader,
                  ),
                ),
                const SizedBox(width: 4),
                Flexible(
                  child: _buildActionButton(
                    urls,
                    _isPlaying ? 'Stop' : 'Listen',
                    _isPlaying ? Icons.stop_outlined : Icons.volume_up_outlined,
                    urls.accentColor,
                    onTap: _toggleAudio,
                  ).animate(target: _isPlaying ? 1 : 0)
                    .shimmer(duration: const Duration(milliseconds: 1000), delay: const Duration(milliseconds: 200))
                    .custom(
                      builder: (context, value, child) => child ?? const SizedBox(),
                      begin: 0,
                      end: 1,
                    ),
                ),
                const SizedBox(width: 4),
                Flexible(
                  child: _buildActionButton(
                    urls,
                    _isBookmarked ? 'Bookmarked' : 'Bookmark',
                    _isBookmarked ? Icons.bookmark : Icons.bookmark_border_rounded,
                    urls.pastelPurple,
                    onTap: _toggleBookmark,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildActionButton(
    URLs urls,
    String label,
    IconData icon,
    int color, {
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          decoration: BoxDecoration(
            color: Color(color).withOpacity(0.1),
            borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 14,
                color: Color(color),
              ),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  label,
                  style: GoogleFonts.poppins(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: Color(color),
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 