import 'package:flutter/material.dart';
import 'package:new_islamic_app_uk/URLs.dart';

class HeaderDecoration extends StatelessWidget {
  final Widget child;

  const HeaderDecoration({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    final screenHeight = MediaQuery.of(context).size.height;
    
    return Stack(
      children: [
        // Background gradient - make it taller for better visibility
        Container(
          height: screenHeight * 0.28, // 28% of screen height for header space
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(urls.primaryColor).withOpacity(0.25), // Increased opacity
                Colors.white.withOpacity(0),
              ],
              stops: const [0.0, 1.0],
            ),
          ),
        ),
        
        // Decorative elements with improved visibility
        Positioned(
          top: -30,
          right: -20,
          child: Container(
            width: 120, // Larger size
            height: 120, // Larger size
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(urls.pastelPurple).withOpacity(0.4), // Increased opacity
                  Color(urls.pastelPurple).withOpacity(0.15),
                ],
              ),
            ),
          ),
        ),
        
        Positioned(
          top: 30,
          right: 40,
          child: Container(
            width: 60, // Larger size
            height: 60, // Larger size
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Color(urls.lightBlue).withOpacity(0.25), // Increased opacity
              boxShadow: [
                BoxShadow(
                  color: Color(urls.lightBlue).withOpacity(0.1),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
          ),
        ),
        
        // Additional decorative element
        Positioned(
          bottom: 20,
          left: 20,
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Color(urls.accentColor).withOpacity(0.2),
            ),
          ),
        ),
        
        // Content with proper layout
        SafeArea(child: child),
      ],
    );
  }
} 