import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';

class SectionHeader extends StatelessWidget {
  final String title;
  final IconData? icon;
  final VoidCallback? onViewAll;

  const SectionHeader({
    Key? key,
    required this.title,
    this.icon,
    this.onViewAll,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Circle with icon if provided
          if (icon != null) ...[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Color(urls.primaryColor).withOpacity(0.1),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Color(urls.primaryColor).withOpacity(0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: Color(urls.primaryColor),
                size: isSmallScreen ? 16 : 18,
              ),
            ),
            SizedBox(width: isSmallScreen ? 8 : 12),
          ],
          
          // Main section title with flexible constraints
          Flexible(
            fit: FlexFit.tight,
            child: Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: isSmallScreen ? 14 : 16,
                fontWeight: FontWeight.w600,
                color: Color(urls.textPrimary),
                letterSpacing: 0.3,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          
          // View All button if callback provided
          if (onViewAll != null) ...[
            const SizedBox(width: 4),
            InkWell(
              onTap: onViewAll,
              borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 8 : 10, 
                  vertical: isSmallScreen ? 4 : 6
                ),
                decoration: BoxDecoration(
                  color: Color(urls.lightGray),
                  borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'View All',
                      style: GoogleFonts.poppins(
                        fontSize: isSmallScreen ? 10 : 12,
                        fontWeight: FontWeight.w500,
                        color: Color(urls.textSecondary),
                      ),
                    ),
                    SizedBox(width: isSmallScreen ? 2 : 4),
                    Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: Color(urls.textSecondary),
                      size: isSmallScreen ? 10 : 12,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
} 