import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/theme/app_theme.dart';
import 'package:new_islamic_app_uk/theme/app_colors.dart';
import 'package:new_islamic_app_uk/theme/app_text_styles.dart';
import 'package:new_islamic_app_uk/widgets/common/modern_card.dart';

/// Legacy quick actions widget - use ModernQuickActions for new implementations
class QuickActions extends StatelessWidget {
  const QuickActions({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // List of quick actions for the app
    final quickActions = [
      {
        'title': 'Prayer Times',
        'icon': FontAwesomeIcons.clockRotateLeft,
        'color': AppColors.getCategoryColor(0),
        'route': '/prayer_times',
      },
      {
        'title': 'Qibla Finder',
        'icon': FontAwesomeIcons.compass,
        'color': AppColors.getCategoryColor(1),
        'route': '/qibla_compass',
      },
      {
        'title': 'Quran',
        'icon': FontAwesomeIcons.bookQuran,
        'color': AppColors.getCategoryColor(2),
        'route': '/quran',
      },
      {
        'title': 'Duas',
        'icon': FontAwesomeIcons.handsPraying,
        'color': AppColors.getCategoryColor(3),
        'route': '/duas',
      },
      {
        'title': 'Zakat Calc',
        'icon': FontAwesomeIcons.calculator,
        'color': AppColors.getCategoryColor(4),
        'route': '/zakat_calculator',
      },
      {
        'title': 'Events',
        'icon': FontAwesomeIcons.calendarDays,
        'color': AppColors.getCategoryColor(5),
        'route': '/events',
      },
      {
        'title': 'Audio',
        'icon': FontAwesomeIcons.headphones,
        'color': AppColors.getCategoryColor(6),
        'route': '/audio',
      },
      {
        'title': 'Hadith',
        'icon': FontAwesomeIcons.bookOpen,
        'color': AppColors.getCategoryColor(7),
        'route': '/hadith',
      },
    ];

    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacing8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                ),
                child: Icon(
                  FontAwesomeIcons.tableCells,
                  color: AppColors.primary,
                  size: AppTheme.iconSmall,
                ),
              ),
              const SizedBox(width: AppTheme.spacing12),
              Text(
                'Quick Actions',
                style: AppTextStyles.titleLarge,
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacing16),

          // Grid of quick action buttons
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              childAspectRatio: 0.85,
              crossAxisSpacing: AppTheme.spacing12,
              mainAxisSpacing: AppTheme.spacing12,
            ),
            itemCount: quickActions.length,
            itemBuilder: (context, index) {
              final action = quickActions[index];
              return _buildQuickActionItem(
                context,
                action['title'] as String,
                action['icon'] as IconData,
                action['color'] as Color,
                action['route'] as String,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionItem(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    String route,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          debugPrint('Navigating to $route');
          try {
            // Check for the '/quran' route specifically to ensure it works properly
            if (route == '/quran') {
              Navigator.of(context, rootNavigator: true).pushNamed(route);
            } else {
              Navigator.of(context).pushNamed(route);
            }
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Could not navigate to $title. ${e.toString()}'),
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        splashColor: color.withOpacity(0.2),
        highlightColor: color.withOpacity(0.1),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 44,
              height: 44,
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                boxShadow: [
                  BoxShadow(
                    color: color.withOpacity(0.2),
                    blurRadius: AppTheme.elevationLow * 2,
                    offset: const Offset(0, AppTheme.elevationLow),
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: color,
                size: AppTheme.iconMedium,
              ),
            ),
            const SizedBox(height: AppTheme.spacing8),
            Text(
              title,
              style: AppTextStyles.labelSmall.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}