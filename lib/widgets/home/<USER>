import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../theme/app_theme.dart';
import '../../models/prayer_time.dart';

class ModernPrayerTimesCard extends StatelessWidget {
  final List<PrayerTimeModel> prayerTimes;
  final String? nextPrayer;
  final String? timeRemaining;

  const ModernPrayerTimesCard({
    Key? key,
    required this.prayerTimes,
    this.nextPrayer,
    this.timeRemaining,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      decoration: BoxDecoration(
        gradient: AppTheme.tealGradient,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppTheme.accentTeal.withOpacity(0.3),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with next prayer info
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Prayer Times',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    if (nextPrayer != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Next: $nextPrayer',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ],
                ),
                if (timeRemaining != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      timeRemaining!,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),

            const SizedBox(height: 20),

            // Prayer times grid
            if (prayerTimes.isNotEmpty)
              _buildPrayerTimesGrid()
            else
              _buildLoadingState(),
          ],
        ),
      ),
    );
  }

  Widget _buildPrayerTimesGrid() {
    final prayers = [
      {'name': 'Fajr', 'time': _getPrayerTime('Fajr')},
      {'name': 'Zuhr', 'time': _getPrayerTime('Zuhr')},
      {'name': 'Asr', 'time': _getPrayerTime('Asr')},
      {'name': 'Maghrib', 'time': _getPrayerTime('Maghrib')},
      {'name': 'Isha', 'time': _getPrayerTime('Isha')},
    ];

    return Column(
      children: [
        for (int i = 0; i < prayers.length; i += 2)
          Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                Expanded(
                  child: _buildPrayerTimeItem(
                    prayers[i]['name']!,
                    prayers[i]['time']!,
                    _isNextPrayer(prayers[i]['name']!),
                  ),
                ),
                if (i + 1 < prayers.length) ...[
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildPrayerTimeItem(
                      prayers[i + 1]['name']!,
                      prayers[i + 1]['time']!,
                      _isNextPrayer(prayers[i + 1]['name']!),
                    ),
                  ),
                ],
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildPrayerTimeItem(String name, String time, bool isNext) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: isNext
          ? Colors.white.withOpacity(0.2)
          : Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: isNext
          ? Border.all(color: Colors.white.withOpacity(0.3), width: 1)
          : null,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            name,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: isNext ? FontWeight.w600 : FontWeight.w500,
              color: Colors.white,
            ),
          ),
          Text(
            time,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      height: 120,
      child: const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
          strokeWidth: 2,
        ),
      ),
    );
  }

  String _getPrayerTime(String prayerName) {
    try {
      if (prayerTimes.isEmpty) return '--:--';
      final prayer = prayerTimes.first;

      switch (prayerName.toLowerCase()) {
        case 'fajr':
          return prayer.fajrJamah ?? '--:--';
        case 'zuhr':
          return prayer.zuhrJamah ?? '--:--';
        case 'asr':
          return prayer.asrJamah ?? '--:--';
        case 'maghrib':
          return prayer.maghribJamah ?? '--:--';
        case 'isha':
          return prayer.ishaJamah ?? '--:--';
        default:
          return '--:--';
      }
    } catch (e) {
      return '--:--';
    }
  }

  bool _isNextPrayer(String prayerName) {
    return nextPrayer?.toLowerCase() == prayerName.toLowerCase();
  }
}
