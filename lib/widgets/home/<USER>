import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:new_islamic_app_uk/widgets/home/<USER>/header_app_bar.dart';
import 'package:new_islamic_app_uk/widgets/home/<USER>/greeting_section.dart';
import 'package:new_islamic_app_uk/widgets/home/<USER>/date_info.dart';
import 'package:new_islamic_app_uk/widgets/home/<USER>/quote_card.dart';
import 'package:new_islamic_app_uk/widgets/home/<USER>/header_background.dart';

class HomeHeader extends StatefulWidget {
  final String hijriDate;
  final String greeting;
  final VoidCallback onMenuPressed;

  const HomeHeader({
    Key? key,
    required this.hijriDate,
    required this.greeting,
    required this.onMenuPressed,
  }) : super(key: key);

  @override
  State<HomeHeader> createState() => _HomeHeaderState();
}

class _HomeHeaderState extends State<HomeHeader> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.zero,
      child: Stack(
        children: [
          // Background with decorative elements
          const HeaderBackground(),
          
          // Content overlay
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Custom App Bar
              HeaderAppBar(onMenuPressed: widget.onMenuPressed),
              
              // Main header content
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Greeting and location
                    GreetingSection(greeting: widget.greeting),
                    
                    const SizedBox(height: 12),
                    
                    // Date information with horizontal scroll
                    DateInfo(
                      hijriDate: widget.hijriDate,
                      onPrayerTimesTap: () {
                        // Scroll to prayer times section
                        try {
                          Scrollable.ensureVisible(
                            (context.findRenderObject()?.paintBounds.bottomCenter ?? Offset.zero) as BuildContext,
                            duration: const Duration(milliseconds: 500),
                            curve: Curves.easeInOut,
                          );
                        } catch (e) {
                          // Handle silently
                        }
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Quote card
                    const QuoteCard(),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms);
  }
}

class QuoteData {
  final String text;
  final String source;
  
  QuoteData(this.text, this.source);
} 