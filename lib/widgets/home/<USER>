import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:new_islamic_app_uk/controllers/home_controller.dart';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/widgets/home/<USER>';
import 'package:new_islamic_app_uk/URLs.dart';

class HomeContent extends StatelessWidget {
  final HomeController controller;
  final ScrollController scrollController;

  const HomeContent({
    Key? key,
    required this.controller,
    required this.scrollController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    final screenWidth = MediaQuery.of(context).size.width;
    
    return CustomScrollView(
      controller: scrollController,
      physics: const BouncingScrollPhysics(),
      slivers: [
        // Header with decorative elements
        SliverToBoxAdapter(
          child: HeaderDecoration(
            child: Column(
              children: [
                HomeHeader(
                  hijriDate: controller.hijriDate,
                  greeting: controller.getGreeting(),
                  onMenuPressed: () {
                    Scaffold.of(context).openDrawer();
                  },
                ),
                
                // Masjid information - wrapped in Padding
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: MosqueInfoCard(),
                ),
              ],
            ),
          ),
        ),
       
        // Main Content
        SliverToBoxAdapter(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              
              // Prayer Times Card - Always at the top for quick access
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: PrayerTimesCard(
                  fajrTime: controller.fajrTime,
                  zuhrTime: controller.zuhrTime,
                  asrTime: controller.asrTime,
                  maghribTime: controller.maghribTime,
                  ishaTime: controller.ishaTime,
                  hijriDate: controller.hijriDate,
                  currentDate: DateTime.now().toString().substring(0, 10),
                  nextPrayer: controller.nextPrayer,
                  timeRemaining: _convertTimeStringToMilliseconds(controller.timeRemaining),
                ),
              ),
              
              // Daily Islamic Knowledge Section
              SectionHeader(
                title: 'Daily Islamic Knowledge', 
                icon: Icons.book_rounded,
                onViewAll: () {},
              ),
              
              // Tab Bar for Daily Content
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: DailyContentTabs(),
              ),
              
              // Quran Verse Card
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: QuranVerseCard(),
              ),
              
              // Mosque Activities Section
              SectionHeader(
                title: 'Mosque Activities',
                icon: Icons.mosque_rounded,
                onViewAll: () {},
              ),
              
              // Featured Events
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: FeaturedEventsCard(
                  events: controller.upcomingEvents,
                ),
              ),
              
              // Jumuah Times with enhanced UI
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                child: JumuahTimesCard(
                  jumuahTimes: controller.jumuahTimes,
                  nextJumuahDay: controller.nextJumuahDay,
                  daysRemaining: _parseDaysRemaining(controller.daysRemaining),
                ),
              ),
              
              // Quick Actions with updated set of actions
              SectionHeader(
                title: 'Quick Actions',
                icon: Icons.grid_view_rounded,
              ),
              
              // Replace Container with Padding to avoid overflow
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: const QuickActions(),
              ),
              
              const SizedBox(height: 16),
              
              // Community Section
              SectionHeader(
                title: 'Community',
                icon: Icons.people_alt_rounded,
                onViewAll: () {},
              ),
              
              // Announcements
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: AnnouncementsCard(
                  announcements: controller.announcements,
                ),
              ),
              
              // Donate Card (without showing progress)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                child: DonateCard(
                  donationUrl: controller.donationUrl,
                  campaignTitle: controller.campaignTitle,
                  campaignDescription: controller.campaignDescription,
                  showProgress: false,
                  targetAmount: controller.targetAmount,
                  raisedAmount: controller.raisedAmount,
                ),
              ),
              
              // Footer with extra space to prevent bottom navigation overlap
              const Footer(),
              
              const SizedBox(height: 100), // Extra space at bottom
            ],
          ),
        ),
      ],
    );
  }

  // Helper method to convert time strings like "2h 3m" to milliseconds
  int _convertTimeStringToMilliseconds(String timeString) {
    try {
      // If the timeString is already a number (seconds), parse it directly
      return int.parse(timeString) * 1000;
    } catch (e) {
      // Handle time strings in the format "Xh Ym"
      int totalMilliseconds = 0;
      
      // Extract hours
      if (timeString.contains('h')) {
        final hoursPart = timeString.split('h')[0].trim();
        try {
          final hours = int.parse(hoursPart);
          totalMilliseconds += hours * 60 * 60 * 1000;
        } catch (e) {
          // Ignore parsing errors
        }
      }
      
      // Extract minutes
      if (timeString.contains('m')) {
        String minutesPart;
        if (timeString.contains('h')) {
          minutesPart = timeString.split('h')[1].split('m')[0].trim();
        } else {
          minutesPart = timeString.split('m')[0].trim();
        }
        
        try {
          final minutes = int.parse(minutesPart);
          totalMilliseconds += minutes * 60 * 1000;
        } catch (e) {
          // Ignore parsing errors
        }
      }
      
      // If we couldn't parse anything, return a default value (30 minutes)
      return totalMilliseconds > 0 ? totalMilliseconds : 30 * 60 * 1000;
    }
  }
  
  // Helper method to parse days remaining string to int
  int _parseDaysRemaining(String daysString) {
    if (daysString == 'Today') {
      return 0;
    } else if (daysString == 'Tomorrow') {
      return 1;
    } else {
      // Extract number from strings like "3 days"
      try {
        return int.parse(daysString.split(' ')[0]);
      } catch (e) {
        // Default to 3 days if parsing fails
        return 3;
      }
    }
  }
} 