import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/screens/prayer_reminder_dashboard.dart';
import 'dart:async';
import 'package:intl/intl.dart';

class EnhancedPrayerTimesCard extends StatefulWidget {
  final String fajrTime;
  final String zuhrTime;
  final String asrTime;
  final String maghribTime;
  final String ishaTime;
  final String hijriDate;
  final String nextPrayer;

  const EnhancedPrayerTimesCard({
    Key? key,
    required this.fajrTime,
    required this.zuhrTime,
    required this.asrTime,
    required this.maghribTime,
    required this.ishaTime,
    required this.hijriDate,
    required this.nextPrayer,
  }) : super(key: key);

  @override
  State<EnhancedPrayerTimesCard> createState() => _EnhancedPrayerTimesCardState();
}

class _EnhancedPrayerTimesCardState extends State<EnhancedPrayerTimesCard> {
  Timer? _timer;
  String _timeRemaining = '';
  String _currentTime = '';

  @override
  void initState() {
    super.initState();
    _updateTime();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTime();
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _updateTime() {
    final now = DateTime.now();
    setState(() {
      _currentTime = DateFormat('HH:mm:ss').format(now);
      _timeRemaining = _calculateTimeRemaining();
    });
  }

  String _calculateTimeRemaining() {
    final now = DateTime.now();
    final today = DateFormat('yyyy-MM-dd').format(now);
    
    try {
      // Parse prayer times for today
      final prayers = {
        'Fajr': _parseTime(today, widget.fajrTime),
        'Zuhr': _parseTime(today, widget.zuhrTime),
        'Asr': _parseTime(today, widget.asrTime),
        'Maghrib': _parseTime(today, widget.maghribTime),
        'Isha': _parseTime(today, widget.ishaTime),
      };

      // Find next prayer
      DateTime? nextPrayerTime;
      for (final entry in prayers.entries) {
        if (entry.value.isAfter(now)) {
          nextPrayerTime = entry.value;
          break;
        }
      }

      // If no prayer today, use tomorrow's Fajr
      if (nextPrayerTime == null) {
        final tomorrow = DateFormat('yyyy-MM-dd').format(now.add(const Duration(days: 1)));
        nextPrayerTime = _parseTime(tomorrow, widget.fajrTime);
      }

      final difference = nextPrayerTime.difference(now);
      if (difference.inHours > 0) {
        return '${difference.inHours}h ${difference.inMinutes.remainder(60)}m ${difference.inSeconds.remainder(60)}s';
      } else {
        return '${difference.inMinutes}m ${difference.inSeconds.remainder(60)}s';
      }
    } catch (e) {
      return 'Loading...';
    }
  }

  DateTime _parseTime(String date, String timeStr) {
    try {
      if (timeStr.contains('AM') || timeStr.contains('PM')) {
        return DateFormat('yyyy-MM-dd hh:mm a').parse('$date $timeStr');
      }
      return DateFormat('yyyy-MM-dd HH:mm').parse('$date $timeStr');
    } catch (e) {
      return DateTime.now();
    }
  }

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Card(
      elevation: 3,
      shadowColor: URLs.primaryColorObj.withOpacity(0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              URLs.primaryColorObj,
              URLs.primaryLightObj,
            ],
          ),
        ),
        child: Column(
          children: [
            // Header with next prayer countdown
            _buildHeader(urls),
            
            // Prayer times grid
            _buildPrayerTimesGrid(urls),
            
            // Footer with date and actions
            _buildFooter(urls),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(URLs urls) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Next Prayer',
                    style: GoogleFonts.poppins(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    widget.nextPrayer,
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _currentTime,
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Text(
                  'Time Remaining',
                  style: GoogleFonts.poppins(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _timeRemaining,
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1.2,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrayerTimesGrid(URLs urls) {
    final prayers = [
      {'name': 'Fajr', 'time': widget.fajrTime, 'icon': FontAwesomeIcons.sun},
      {'name': 'Zuhr', 'time': widget.zuhrTime, 'icon': FontAwesomeIcons.cloudSun},
      {'name': 'Asr', 'time': widget.asrTime, 'icon': FontAwesomeIcons.cloud},
      {'name': 'Maghrib', 'time': widget.maghribTime, 'icon': FontAwesomeIcons.moon},
      {'name': 'Isha', 'time': widget.ishaTime, 'icon': FontAwesomeIcons.star},
    ];

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          for (int i = 0; i < prayers.length; i += 2)
            Padding(
              padding: EdgeInsets.only(bottom: i < prayers.length - 2 ? 16 : 0),
              child: Row(
                children: [
                  Expanded(
                    child: _buildPrayerItem(
                      prayers[i]['name'] as String,
                      prayers[i]['time'] as String,
                      prayers[i]['icon'] as IconData,
                      widget.nextPrayer == prayers[i]['name'],
                      urls,
                    ),
                  ),
                  const SizedBox(width: 16),
                  if (i + 1 < prayers.length)
                    Expanded(
                      child: _buildPrayerItem(
                        prayers[i + 1]['name'] as String,
                        prayers[i + 1]['time'] as String,
                        prayers[i + 1]['icon'] as IconData,
                        widget.nextPrayer == prayers[i + 1]['name'],
                        urls,
                      ),
                    )
                  else
                    const Expanded(child: SizedBox()),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPrayerItem(String name, String time, IconData icon, bool isNext, URLs urls) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isNext ? URLs.primaryColorObj.withOpacity(0.1) : Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: isNext ? Border.all(color: URLs.primaryColorObj, width: 2) : null,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isNext ? URLs.primaryColorObj : Colors.grey[300],
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: isNext ? Colors.white : Colors.grey[600],
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: isNext ? URLs.primaryColorObj : Colors.grey[700],
                  ),
                ),
                Text(
                  time,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter(URLs urls) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Today',
                style: GoogleFonts.poppins(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 12,
                ),
              ),
              Text(
                DateFormat('EEEE, MMM d').format(DateTime.now()),
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                widget.hijriDate,
                style: GoogleFonts.poppins(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 12,
                ),
              ),
            ],
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PrayerReminderDashboard(),
                ),
              );
            },
            icon: const Icon(Icons.notifications_outlined, size: 18),
            label: Text(
              'Settings',
              style: GoogleFonts.poppins(fontSize: 12),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: URLs.primaryColorObj,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
          ),
        ],
      ),
    );
  }
}
