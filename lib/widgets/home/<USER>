import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:new_islamic_app_uk/models/event.dart';
import 'package:intl/intl.dart';
import 'package:new_islamic_app_uk/event_show_page.dart';

class FeaturedEventsCard extends StatefulWidget {
  final List<EventModel> events;

  const FeaturedEventsCard({
    Key? key,
    required this.events,
  }) : super(key: key);

  @override
  _FeaturedEventsCardState createState() => _FeaturedEventsCardState();
}

class _FeaturedEventsCardState extends State<FeaturedEventsCard> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    if (widget.events.isEmpty) {
      return _buildEmptyState();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildEventCarousel(),
          _buildCarouselIndicator(),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms, delay: 200.ms).slideY(begin: 0.1, end: 0);
  }

  Widget _buildEmptyState() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          const SizedBox(height: 20),
          Icon(
            FontAwesomeIcons.calendarXmark,
            size: 40,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          Text(
            'No upcoming events',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Check back later for new events',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms, delay: 200.ms).slideY(begin: 0.1, end: 0);
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(
            FontAwesomeIcons.calendarDay,
            color: Color(URLs().colorScheme),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Upcoming Events',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              // Navigate to all events page
            },
            style: TextButton.styleFrom(
              foregroundColor: Color(URLs().colorScheme),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'View All',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward, size: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventCarousel() {
    return CarouselSlider.builder(
      itemCount: widget.events.length,
      itemBuilder: (context, index, realIndex) {
        final event = widget.events[index];
        return _buildEventCard(event);
      },
      options: CarouselOptions(
        height: 180,
        viewportFraction: 0.9,
        enlargeCenterPage: true,
        enableInfiniteScroll: widget.events.length > 1,
        onPageChanged: (index, reason) {
          setState(() {
            _currentIndex = index;
          });
        },
        autoPlay: widget.events.length > 1,
        autoPlayInterval: const Duration(seconds: 5),
      ),
    );
  }

  Widget _buildEventCard(EventModel event) {
    // Extract event details
    String eventDate = event.event_date.isNotEmpty
        ? _formatEventDate(event.event_date)
        : _extractDate(event.title, event.date);
    
    String location = _extractLocation(event.content);
    
    String time = event.start_time.isNotEmpty
        ? _formatTime(event.start_time)
        : _extractTime(event.title);
    
    // Generate a color based on the event title
    final Color cardColor = _getColorForEvent(event.title);
    
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => EventShowPage(event: event),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 5, vertical: 10),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cardColor.withOpacity(0.8),
              cardColor,
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: cardColor.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Event icon background
            Positioned(
              right: -20,
              bottom: -20,
              child: Icon(
                FontAwesomeIcons.mosque,
                size: 120,
                color: Colors.white.withOpacity(0.1),
              ),
            ),
            
            // Event content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date badge
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.calendar_today,
                          color: Colors.white,
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          eventDate,
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Event title
                  Text(
                    event.title,
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const Spacer(),
                  
                  // Event details
                  Row(
                    children: [
                      // Location
                      Expanded(
                        child: Row(
                          children: [
                            Icon(
                              Icons.location_on,
                              color: Colors.white.withOpacity(0.9),
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                location,
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color: Colors.white.withOpacity(0.9),
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Time
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              color: Colors.white,
                              size: 12,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              time,
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Generate a color based on the event title
  Color _getColorForEvent(String title) {
    final List<Color> colors = [
      Color(URLs().colorScheme),
      Colors.blue[700]!,
      Colors.purple[700]!,
      Colors.teal[700]!,
      Colors.amber[700]!,
      Colors.indigo[700]!,
    ];
    
    // Use the title's hash code to pick a color
    final int index = title.hashCode.abs() % colors.length;
    return colors[index];
  }

  String _formatEventDate(String dateStr) {
    try {
      final DateTime date = DateTime.parse(dateStr);
      return DateFormat('EEE, MMM d').format(date);
    } catch (e) {
      return dateStr;
    }
  }

  String _formatTime(String timeStr) {
    try {
      final DateTime time = DateFormat('HH:mm').parse(timeStr);
      return DateFormat('h:mm a').format(time);
    } catch (e) {
      return timeStr;
    }
  }

  String _extractLocation(String content) {
    try {
      if (content.contains('Location:')) {
        final locationStart = content.indexOf('Location:') + 9;
        final locationEnd = content.indexOf('<', locationStart);
        if (locationEnd > locationStart) {
          return content.substring(locationStart, locationEnd).trim();
        }
      }
    } catch (e) {
      print('Error extracting location: $e');
    }
    return 'Masjid Sunnah';
  }

  String _extractDate(String title, String date) {
    try {
      // First try to extract from title (e.g., "Event - Date")
      if (title.contains(' - ')) {
        return title.split(' - ')[0];
      }
      
      // Then try to format the date from the date field
      if (date.isNotEmpty) {
        final DateTime parsedDate = DateTime.parse(date);
        return DateFormat('EEE, MMM d').format(parsedDate);
      }
    } catch (e) {
      print('Error extracting date: $e');
    }
    return 'Upcoming';
  }

  String _extractTime(String title) {
    try {
      if (title.contains(' - ')) {
        return title.split(' - ')[1];
      }
    } catch (e) {
      print('Error extracting time: $e');
    }
    return 'TBA';
  }

  Widget _buildCarouselIndicator() {
    if (widget.events.length <= 1) return const SizedBox.shrink();
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16, top: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          widget.events.length,
          (index) => GestureDetector(
            onTap: () {
              setState(() {
                _currentIndex = index;
              });
            },
            child: AnimatedContainer(
              duration: Duration(milliseconds: 300),
              width: _currentIndex == index ? 16 : 8,
              height: 8,
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: _currentIndex == index
                    ? Color(URLs().colorScheme)
                    : Colors.grey[300],
              ),
            ),
          ),
        ),
      ),
    );
  }
} 