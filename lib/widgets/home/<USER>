import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';

class Footer extends StatelessWidget {
  const Footer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(urls.cardBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: <PERSON>umn(
        children: [
          // Mosque logo and name
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(urls.primaryColor).withOpacity(0.9),
                  Color(urls.primaryColor),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(urls.cardBorderRadius),
                topRight: Radius.circular(urls.cardBorderRadius),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.mosque_rounded,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'Masjid Sunnah Nelson',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          
          // Social media links
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildSocialButton(Icons.facebook, Color(urls.coolGradient[0])),
                _buildSocialButton(Icons.message_outlined, Color(urls.coolGradient[1])),
                _buildSocialButton(Icons.ondemand_video_rounded, Color(urls.pastelPink)),
                _buildSocialButton(Icons.camera_alt_outlined, Color(urls.accentColor)),
              ],
            ),
          ),
          
          // Contact information
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Color(urls.lightGray),
              borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
            ),
            child: Column(
              children: [
                _buildContactRow(
                  Icons.location_on_outlined,
                  '123 Main Street, Nelson, BB9 7QT',
                  urls,
                ),
                const SizedBox(height: 16),
                _buildContactRow(
                  Icons.phone_outlined,
                  '01282 123456',
                  urls,
                ),
                const SizedBox(height: 16),
                _buildContactRow(
                  Icons.email_outlined,
                  '<EMAIL>',
                  urls,
                ),
              ],
            ),
          ),
          
          // Copyright
          Padding(
            padding: const EdgeInsets.all(24),
            child: Text(
              '© 2023 Masjid Sunnah Nelson. All rights reserved.',
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Color(urls.textSecondary),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSocialButton(IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.15),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {},
          borderRadius: BorderRadius.circular(30),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Icon(
              icon,
              color: color,
              size: 22,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContactRow(IconData icon, String text, URLs urls) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Color(urls.primaryColor).withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            size: 18,
            color: Color(urls.primaryColor),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            text,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Color(urls.textSecondary),
            ),
          ),
        ),
      ],
    );
  }
} 