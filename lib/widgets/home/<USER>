import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../theme/app_theme.dart';
import '../../widgets/common/modern_card.dart';

class ModernQuickActions extends StatelessWidget {
  final VoidCallback? onQuranTap;
  final VoidCallback? onDuasTap;
  final VoidCallback? onHadithTap;
  final VoidCallback? onQiblahTap;
  final VoidCallback? onZakatTap;
  final VoidCallback? onEventsTap;

  const ModernQuickActions({
    Key? key,
    this.onQuranTap,
    this.onDuasTap,
    this.onHadithTap,
    this.onQiblahTap,
    this.onZakatTap,
    this.onEventsTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: AppTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          // First row - Main Islamic features
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  title: 'Quran',
                  icon: FontAwesomeIcons.bookQuran,
                  color: const Color(0xFF4CAF50),
                  onTap: onQuranTap,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionCard(
                  title: 'Duas',
                  icon: FontAwesomeIcons.handsPraying,
                  color: const Color(0xFF2196F3),
                  onTap: onDuasTap,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionCard(
                  title: 'Hadith',
                  icon: FontAwesomeIcons.scroll,
                  color: const Color(0xFFFF9800),
                  onTap: onHadithTap,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Second row - Utilities
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  title: 'Qiblah',
                  icon: FontAwesomeIcons.compass,
                  color: const Color(0xFF9C27B0),
                  onTap: onQiblahTap,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionCard(
                  title: 'Zakat',
                  icon: FontAwesomeIcons.coins,
                  color: const Color(0xFF607D8B),
                  onTap: onZakatTap,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionCard(
                  title: 'Events',
                  icon: FontAwesomeIcons.calendar,
                  color: const Color(0xFFE91E63),
                  onTap: onEventsTap,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard({
    required String title,
    required IconData icon,
    required Color color,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: FaIcon(
                icon,
                size: 24,
                color: color,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: AppTheme.textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class ModernDailyContent extends StatelessWidget {
  final String verseText;
  final String verseReference;
  final String hadithText;
  final String hadithReference;

  const ModernDailyContent({
    Key? key,
    required this.verseText,
    required this.verseReference,
    required this.hadithText,
    required this.hadithReference,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Daily Islamic Knowledge',
            style: AppTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          // Daily Verse Card
          GradientCard(
            gradient: const LinearGradient(
              colors: [Color(0xFF4CAF50), Color(0xFF8BC34A)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            margin: EdgeInsets.zero,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      FontAwesomeIcons.bookQuran,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Verse of the Day',
                      style: AppTheme.textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  verseText,
                  style: AppTheme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white,
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  verseReference,
                  style: AppTheme.textTheme.bodySmall?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Daily Hadith Card
          GradientCard(
            gradient: const LinearGradient(
              colors: [Color(0xFF2196F3), Color(0xFF03DAC6)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            margin: EdgeInsets.zero,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      FontAwesomeIcons.scroll,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Hadith of the Day',
                      style: AppTheme.textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  hadithText,
                  style: AppTheme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white,
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  hadithReference,
                  style: AppTheme.textTheme.bodySmall?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
