import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../controllers/home_controller.dart';
import '../../theme/app_theme.dart';
import '../../widgets/common/modern_card.dart';
import 'modern_home_header.dart';
import 'modern_prayer_times_card.dart';
import 'modern_quick_actions.dart';

class ModernHomeContent extends StatelessWidget {
  final HomeController controller;
  final ScrollController scrollController;

  const ModernHomeContent({
    Key? key,
    required this.controller,
    required this.scrollController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      controller: scrollController,
      physics: const BouncingScrollPhysics(),
      slivers: [
        // Modern Header
        SliverToBoxAdapter(
          child: ModernHomeHeader(
            greeting: controller.getGreeting(),
            hijriDate: controller.hijriDate,
            onMenuPressed: () {
              Scaffold.of(context).openDrawer();
            },
          ),
        ),
        
        // Mosque Info Card
        const SliverToBoxAdapter(
          child: ModernMosqueInfoCard(),
        ),
        
        // Prayer Times Card
        SliverToBoxAdapter(
          child: ModernPrayerTimesCard(
            prayerTimes: controller.prayerTimes,
            nextPrayer: controller.nextPrayer,
            timeRemaining: controller.timeRemaining,
          ),
        ),
        
        // Quick Actions
        SliverToBoxAdapter(
          child: ModernQuickActions(
            onQuranTap: () => _navigateToQuran(context),
            onDuasTap: () => _navigateToDuas(context),
            onHadithTap: () => _navigateToHadith(context),
            onQiblahTap: () => _navigateToQiblah(context),
            onZakatTap: () => _navigateToZakat(context),
            onEventsTap: () => _navigateToEvents(context),
          ),
        ),
        
        // Daily Islamic Knowledge
        SliverToBoxAdapter(
          child: ModernDailyContent(
            verseText: controller.dailyVerse ?? "And whoever relies upon Allah - then He is sufficient for him. Indeed, Allah will accomplish His purpose.",
            verseReference: "Quran 65:3",
            hadithText: controller.dailyHadith ?? "The best of people are those who benefit others.",
            hadithReference: "Sahih Bukhari",
          ),
        ),
        
        // Community Section
        SliverToBoxAdapter(
          child: _buildCommunitySection(context),
        ),
        
        // Events Section
        SliverToBoxAdapter(
          child: _buildEventsSection(context),
        ),
        
        // Footer spacing
        const SliverToBoxAdapter(
          child: SizedBox(height: 100),
        ),
      ],
    );
  }

  Widget _buildCommunitySection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Community',
            style: AppTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: FeatureCard(
                  title: 'Announcements',
                  description: 'Latest mosque updates',
                  icon: FontAwesomeIcons.bullhorn,
                  iconColor: const Color(0xFFFF9800),
                  onTap: () => _navigateToAnnouncements(context),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: FeatureCard(
                  title: 'Donate',
                  description: 'Support the mosque',
                  icon: FontAwesomeIcons.heart,
                  iconColor: const Color(0xFFE91E63),
                  onTap: () => _navigateToDonate(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEventsSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Upcoming Events',
                style: AppTheme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextButton(
                onPressed: () => _navigateToEvents(context),
                child: Text(
                  'View All',
                  style: AppTheme.textTheme.labelLarge?.copyWith(
                    color: AppTheme.primaryGreen,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          if (controller.upcomingEvents.isNotEmpty)
            ...controller.upcomingEvents.take(3).map((event) => 
              ModernCard(
                margin: const EdgeInsets.only(bottom: 12),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryGreen.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        FontAwesomeIcons.calendar,
                        color: AppTheme.primaryGreen,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            event.title,
                            style: AppTheme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            event.date,
                            style: AppTheme.textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                    const Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: AppTheme.textLight,
                    ),
                  ],
                ),
                onTap: () => _navigateToEventDetails(context, event),
              ),
            ).toList()
          else
            ModernCard(
              child: Column(
                children: [
                  const Icon(
                    FontAwesomeIcons.calendar,
                    color: AppTheme.textLight,
                    size: 32,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'No upcoming events',
                    style: AppTheme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // Navigation methods
  void _navigateToQuran(BuildContext context) {
    Navigator.pushNamed(context, '/quran');
  }

  void _navigateToDuas(BuildContext context) {
    // Navigate to duas page
  }

  void _navigateToHadith(BuildContext context) {
    Navigator.pushNamed(context, '/dailyHadith');
  }

  void _navigateToQiblah(BuildContext context) {
    // Navigate to qiblah page
  }

  void _navigateToZakat(BuildContext context) {
    // Navigate to zakat calculator
  }

  void _navigateToEvents(BuildContext context) {
    // Navigate to events page
  }

  void _navigateToAnnouncements(BuildContext context) {
    // Navigate to announcements
  }

  void _navigateToDonate(BuildContext context) {
    // Navigate to donate page
  }

  void _navigateToEventDetails(BuildContext context, dynamic event) {
    // Navigate to event details
  }
}
