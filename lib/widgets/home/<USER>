import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/daily_hadith/widgets/daily_hadith_card.dart';
import 'package:new_islamic_app_uk/features/arabic_word/widgets/arabic_word_card.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/widgets/prayer_reminder_widget.dart';

class DailyContentTabs extends StatefulWidget {
  const DailyContentTabs({Key? key}) : super(key: key);

  @override
  State<DailyContentTabs> createState() => _DailyContentTabsState();
}

class _DailyContentTabsState extends State<DailyContentTabs> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final urls = URLs();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return Column(
      children: [
        // Custom Tab Bar
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 2),
          height: 52,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(urls.buttonBorderRadius),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.03),
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(4),
            child: TabBar(
              controller: _tabController,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(urls.buttonBorderRadius - 4),
                color: Color(urls.primaryColor),
                boxShadow: [
                  BoxShadow(
                    color: Color(urls.primaryColor).withOpacity(0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              labelColor: Colors.white,
              unselectedLabelColor: Color(urls.textSecondary),
              tabs: [
                _buildTabItem(Icons.auto_stories, 'Hadith', isSmallScreen),
                _buildTabItem(Icons.translate, 'Arabic', isSmallScreen),
              ],
              labelStyle: GoogleFonts.poppins(
                fontSize: isSmallScreen ? 11 : 13,
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: GoogleFonts.poppins(
                fontSize: isSmallScreen ? 11 : 13,
                fontWeight: FontWeight.w500,
              ),
              padding: EdgeInsets.zero,
              labelPadding: const EdgeInsets.symmetric(horizontal: 4),
            ),
          ),
        ),
        
        // Tab Bar View - Using Expanded instead of fixed height
        Padding(
          padding: const EdgeInsets.only(top: 16),
          child: SizedBox(
            height: 400, // Slightly taller to accommodate content
            child: TabBarView(
              controller: _tabController,
              children: const [
                // Daily Hadith Tab
                SingleChildScrollView(
                  physics: BouncingScrollPhysics(),
                  child: Padding(
                    padding: EdgeInsets.only(bottom: 16),
                    child: DailyHadithCard(),
                  ),
                ),
                
                // Arabic Word Tab
                SingleChildScrollView(
                  physics: BouncingScrollPhysics(),
                  child: Padding(
                    padding: EdgeInsets.only(bottom: 16),
                    child: ArabicWordCard(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildTabItem(IconData icon, String label, bool isSmallScreen) {
    if (isSmallScreen) {
      // On small screens, show only icon with compact label
      return Tab(
        height: 44,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 14),
            const SizedBox(height: 2),
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                label,
                style: const TextStyle(fontSize: 10),
              ),
            ),
          ],
        ),
      );
    } else {
      // On larger screens, show icon and label side by side
      return Tab(
        height: 44,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                label,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      );
    }
  }
} 