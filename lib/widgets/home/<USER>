import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/models/jumuah.dart';

class JumuahTimesCard extends StatelessWidget {
  final List<JumuahModel> jumuahTimes;
  final String nextJumuahDay;
  final int daysRemaining;

  const JumuahTimesCard({
    Key? key,
    required this.jumuahTimes,
    required this.nextJumuahDay,
    required this.daysRemaining,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Card(
      elevation: urls.cardElevation,
      shadowColor: Colors.black.withOpacity(0.05),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(urls.cardBorderRadius),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(urls.cardBorderRadius),
          color: Colors.white,
        ),
        child: Column(
          children: [
            // Card Header - Jumuah Countdown
            _buildJumuahHeader(urls),
            
            // Separator
            Divider(
              height: 1,
              thickness: 1,
              color: Color(urls.lightGray),
            ),
            
            // Jumuah Times List
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'This Week\'s Jumuah Times',
                    style: GoogleFonts.poppins(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: Color(urls.textPrimary),
                    ),
                  ),
                  const SizedBox(height: 16),
                  ...jumuahTimes.map((jumuah) => _buildJumuahTimeItem(urls, jumuah)).toList(),
                ],
              ),
            ),
            
            // Special Note Section
            _buildSpecialNoteSection(urls),
          ],
        ),
      ),
    );
  }
  
  Widget _buildJumuahHeader(URLs urls) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [
            Color(urls.pastelPurple),
            Color(urls.primaryColor),
          ],
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(urls.cardBorderRadius),
          topRight: Radius.circular(urls.cardBorderRadius),
        ),
      ),
      child: Row(
        children: [
          // Mosque Icon with Circle
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                Icons.mosque_rounded,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Next Jumuah Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Next Jumuah',
                  style: GoogleFonts.poppins(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14,
                  ),
                ),
                Text(
                  nextJumuahDay,
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 20,
                  ),
                ),
              ],
            ),
          ),
          
          // Days Remaining Counter
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
            ),
            child: Column(
              children: [
                Text(
                  daysRemaining.toString(),
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                    fontSize: 24,
                  ),
                ),
                Text(
                  'Days',
                  style: GoogleFonts.poppins(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildJumuahTimeItem(URLs urls, JumuahModel jumuah) {
    // Extract times from content
    final Map<String, String> times = _extractJumuahTimes(jumuah.content);
    final String khutbahTime = times['khutbah'] ?? 'TBA';
    final String jamaatTime = times['jamaat'] ?? 'TBA';
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Color(urls.lightGray),
        borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.event_available_outlined,
              color: Color(urls.primaryColor),
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  jumuah.title,
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    color: Color(urls.textPrimary),
                  ),
                ),
                Text(
                  'Khutbah: $khutbahTime • Jamaat: $jamaatTime',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Color(urls.textSecondary),
                  ),
                ),
              ],
            ),
          ),
          
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
              border: Border.all(
                color: Color(urls.primaryColor).withOpacity(0.2),
              ),
            ),
            child: Text(
              jamaatTime,
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Color(urls.primaryColor),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSpecialNoteSection(URLs urls) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Color(urls.primaryColor).withOpacity(0.05),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(urls.cardBorderRadius),
          bottomRight: Radius.circular(urls.cardBorderRadius),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Color(urls.primaryColor),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Khutbah begins 15 minutes before the Jumuah prayer. Please come early to benefit from the reminder.',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Color(urls.textSecondary),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // Helper method to extract Jumuah times from content
  Map<String, String> _extractJumuahTimes(String content) {
    Map<String, String> result = {
      'khutbah': 'TBA',
      'jamaat': 'TBA',
    };
    
    try {
      if (content.contains('Khutbah:')) {
        final khutbahStart = content.indexOf('Khutbah:') + 8;
        final khutbahEnd = content.indexOf('<', khutbahStart);
        if (khutbahEnd > khutbahStart) {
          result['khutbah'] = content.substring(khutbahStart, khutbahEnd).trim();
        }
      }
      
      if (content.contains('Jamaat:')) {
        final jamaatStart = content.indexOf('Jamaat:') + 7;
        final jamaatEnd = content.indexOf('<', jamaatStart);
        if (jamaatEnd > jamaatStart) {
          result['jamaat'] = content.substring(jamaatStart, jamaatEnd).trim();
        }
      }
    } catch (e) {
      print('Error extracting Jumuah times: $e');
    }
    
    return result;
  }
} 