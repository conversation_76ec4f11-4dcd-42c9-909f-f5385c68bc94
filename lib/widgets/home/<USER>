import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/controllers/home_controller.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/screens/prayer_reminder_dashboard.dart';
import 'package:new_islamic_app_uk/features/quran/quran_module.dart';
import 'package:new_islamic_app_uk/dua/dua_category.dart';
import 'package:new_islamic_app_uk/events.dart';
import 'package:new_islamic_app_uk/qiblah_direction.dart';
import 'package:new_islamic_app_uk/zakat_calculator.dart';
import 'package:new_islamic_app_uk/features/hadith/screens/hadith_home_screen.dart';
import 'package:new_islamic_app_uk/about.dart';
import 'package:intl/intl.dart';
import 'dart:async';

class RedesignedHomeContent extends StatefulWidget {
  final HomeController controller;

  const RedesignedHomeContent({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<RedesignedHomeContent> createState() => _RedesignedHomeContentState();
}

class _RedesignedHomeContentState extends State<RedesignedHomeContent> {
  Timer? _timer;
  String _currentTime = '';
  String _timeRemaining = '';

  @override
  void initState() {
    super.initState();
    _updateTime();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTime();
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _updateTime() {
    final now = DateTime.now();
    setState(() {
      _currentTime = DateFormat('HH:mm').format(now);
      _timeRemaining = _calculateTimeRemaining();
    });
  }

  String _calculateTimeRemaining() {
    final now = DateTime.now();
    final today = DateFormat('yyyy-MM-dd').format(now);

    try {
      final prayers = {
        'Fajr': _parseTime(today, widget.controller.fajrTime),
        'Zuhr': _parseTime(today, widget.controller.zuhrTime),
        'Asr': _parseTime(today, widget.controller.asrTime),
        'Maghrib': _parseTime(today, widget.controller.maghribTime),
        'Isha': _parseTime(today, widget.controller.ishaTime),
      };

      DateTime? nextPrayerTime;
      for (final entry in prayers.entries) {
        if (entry.value.isAfter(now)) {
          nextPrayerTime = entry.value;
          break;
        }
      }

      if (nextPrayerTime == null) {
        final tomorrow = DateFormat('yyyy-MM-dd').format(now.add(const Duration(days: 1)));
        nextPrayerTime = _parseTime(tomorrow, widget.controller.fajrTime);
      }

      final difference = nextPrayerTime.difference(now);
      if (difference.inHours > 0) {
        return '${difference.inHours}h ${difference.inMinutes.remainder(60)}m';
      } else {
        return '${difference.inMinutes}m';
      }
    } catch (e) {
      return 'Loading...';
    }
  }

  DateTime _parseTime(String date, String timeStr) {
    try {
      if (timeStr.contains('AM') || timeStr.contains('PM')) {
        return DateFormat('yyyy-MM-dd hh:mm a').parse('$date $timeStr');
      }
      return DateFormat('yyyy-MM-dd HH:mm').parse('$date $timeStr');
    } catch (e) {
      return DateTime.now();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Beautiful Header with Prayer Times
            _buildPrayerTimesHeader(),

            // Main Content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Quick Actions Section
                  _buildSectionTitle('Quick Actions'),
                  const SizedBox(height: 16),
                  _buildQuickActionsGrid(),

                  const SizedBox(height: 32),

                  // Featured Islamic Content
                  _buildSectionTitle('Islamic Content'),
                  const SizedBox(height: 16),
                  _buildIslamicContentCards(),

                  const SizedBox(height: 32),

                  // Community Section
                  _buildSectionTitle('Community'),
                  const SizedBox(height: 16),
                  _buildCommunityCards(),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrayerTimesHeader() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xFF5E72E4).withOpacity(0.9),
            Color(0xFF11CDEF).withOpacity(0.9),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              // Header Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Assalamu Alaikum',
                        style: GoogleFonts.poppins(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        'Masjid Sunnah Nelson',
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      _currentTime,
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Next Prayer Card
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Next Prayer',
                              style: GoogleFonts.poppins(
                                color: Colors.white.withOpacity(0.9),
                                fontSize: 14,
                              ),
                            ),
                            Text(
                              widget.controller.nextPrayer,
                              style: GoogleFonts.poppins(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              'Time Remaining',
                              style: GoogleFonts.poppins(
                                color: Colors.white.withOpacity(0.9),
                                fontSize: 14,
                              ),
                            ),
                            Text(
                              _timeRemaining,
                              style: GoogleFonts.poppins(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Prayer Times Row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildPrayerTimeItem('Fajr', widget.controller.fajrTime),
                        _buildPrayerTimeItem('Zuhr', widget.controller.zuhrTime),
                        _buildPrayerTimeItem('Asr', widget.controller.asrTime),
                        _buildPrayerTimeItem('Maghrib', widget.controller.maghribTime),
                        _buildPrayerTimeItem('Isha', widget.controller.ishaTime),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Hijri Date
              Text(
                widget.controller.hijriDate,
                style: GoogleFonts.poppins(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPrayerTimeItem(String name, String time) {
    final isNext = widget.controller.nextPrayer == name;

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isNext ? Colors.white.withOpacity(0.3) : Colors.white.withOpacity(0.1),
            shape: BoxShape.circle,
            border: isNext ? Border.all(color: Colors.white, width: 2) : null,
          ),
          child: Icon(
            _getPrayerIcon(name),
            color: Colors.white,
            size: 16,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          name,
          style: GoogleFonts.poppins(
            color: Colors.white.withOpacity(0.9),
            fontSize: 10,
            fontWeight: isNext ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
        Text(
          time,
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  IconData _getPrayerIcon(String prayer) {
    switch (prayer) {
      case 'Fajr': return FontAwesomeIcons.sun;
      case 'Zuhr': return FontAwesomeIcons.cloudSun;
      case 'Asr': return FontAwesomeIcons.cloud;
      case 'Maghrib': return FontAwesomeIcons.moon;
      case 'Isha': return FontAwesomeIcons.star;
      default: return FontAwesomeIcons.clock;
    }
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: Colors.grey[800],
      ),
    );
  }

  Widget _buildQuickActionsGrid() {
    return Row(
      children: [
        Expanded(
          child: _buildActionCard(
            title: 'Prayer\nReminders',
            icon: FontAwesomeIcons.bell,
            gradient: [Colors.orange.shade300, Colors.orange.shade600],
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const PrayerReminderDashboard()),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildActionCard(
            title: 'Qiblah\nDirection',
            icon: FontAwesomeIcons.compass,
            gradient: [Colors.indigo.shade300, Colors.indigo.shade600],
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => QiblahDirectionPage()),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildIslamicContentCards() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildContentCard(
                title: 'Holy Quran',
                subtitle: 'Read & Listen',
                icon: FontAwesomeIcons.bookQuran,
                gradient: [Colors.green.shade300, Colors.green.shade600],
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const QuranModule()),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildContentCard(
                title: 'Duas & Azkar',
                subtitle: 'Daily Supplications',
                icon: FontAwesomeIcons.handsPraying,
                gradient: [Colors.purple.shade300, Colors.purple.shade600],
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => DuaCategoryPage()),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildContentCard(
                title: 'Hadith',
                subtitle: 'Prophetic Sayings',
                icon: FontAwesomeIcons.scroll,
                gradient: [Colors.teal.shade300, Colors.teal.shade600],
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => HadithHomeScreen()),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildContentCard(
                title: 'Zakat Calculator',
                subtitle: 'Calculate Charity',
                icon: FontAwesomeIcons.handHoldingDollar,
                gradient: [Colors.amber.shade300, Colors.amber.shade600],
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => ZakatCalculatorPage()),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCommunityCards() {
    return Column(
      children: [
        _buildLargeCard(
          title: 'Mosque Events',
          subtitle: 'Stay updated with community activities',
          icon: FontAwesomeIcons.calendar,
          gradient: [Colors.blue.shade300, Colors.blue.shade600],
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => EventsPage()),
          ),
        ),
        const SizedBox(height: 16),
        _buildLargeCard(
          title: 'About Our Mosque',
          subtitle: 'Learn about Masjid Sunnah Nelson',
          icon: FontAwesomeIcons.mosque,
          gradient: [Colors.cyan.shade300, Colors.cyan.shade600],
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => AboutPage()),
          ),
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required String title,
    required IconData icon,
    required List<Color> gradient,
    required VoidCallback onTap,
  }) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: gradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: gradient[0].withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: 32,
                ),
                const SizedBox(height: 8),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContentCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required List<Color> gradient,
    required VoidCallback onTap,
  }) {
    return Container(
      height: 140,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: gradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: gradient[0].withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: 36,
                ),
                const SizedBox(height: 12),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.poppins(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLargeCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required List<Color> gradient,
    required VoidCallback onTap,
  }) {
    return Container(
      width: double.infinity,
      height: 100,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: gradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: gradient[0].withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: 40,
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        title,
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: GoogleFonts.poppins(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white.withOpacity(0.8),
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
