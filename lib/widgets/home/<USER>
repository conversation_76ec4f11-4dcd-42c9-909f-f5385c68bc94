import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';

class MosqueInfoCard extends StatelessWidget {
  const MosqueInfoCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    return Card(
      elevation: urls.cardElevation,
      shadowColor: Colors.black.withOpacity(0.05),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(urls.cardBorderRadius),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(urls.cardBorderRadius),
          color: Colors.white,
        ),
        child: Column(
          children: [
            // Mosque Logo and Info
            Row(
              children: [
                // Logo Circle
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(urls.primaryColor),
                        Color(urls.lightBlue),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Color(urls.primaryColor).withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Icon(
                      Icons.mosque_rounded,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Mosque Title and Subtitle
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Masjid Sunnah',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Color(urls.textPrimary),
                          letterSpacing: 0.3,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Nelson, Lancashire',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Color(urls.textSecondary),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Favorite Button
                Container(
                  decoration: BoxDecoration(
                    color: Color(urls.lightGray),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.notifications_none_rounded,
                      color: Color(urls.primaryColor),
                    ),
                    onPressed: () {},
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Mosque Stats
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Color(urls.lightGray),
                borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem(
                    icon: Icons.event_available_rounded,
                    label: 'Events',
                    value: '12',
                    color: urls.accentColor,
                  ),
                  _buildDivider(),
                  _buildStatItem(
                    icon: Icons.people_alt_rounded,
                    label: 'Community',
                    value: '350+',
                    color: urls.pastelPurple,
                  ),
                  _buildDivider(),
                  _buildStatItem(
                    icon: Icons.volunteer_activism_rounded,
                    label: 'Donations',
                    value: '£4.3K',
                    color: urls.colorGold,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required int color,
  }) {
    final urls = URLs();
    
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Color(color).withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: Color(color),
            size: 18,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: GoogleFonts.poppins(
            fontSize: 15,
            fontWeight: FontWeight.w600,
            color: Color(urls.textPrimary),
          ),
        ),
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 12,
            color: Color(urls.textSecondary),
          ),
        ),
      ],
    );
  }
  
  Widget _buildDivider() {
    return Container(
      height: 40,
      width: 1,
      color: Colors.grey.withOpacity(0.2),
    );
  }
} 