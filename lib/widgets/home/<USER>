import 'package:flutter/material.dart';
import 'package:flutter_countdown_timer/flutter_countdown_timer.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/theme/app_theme.dart';
import 'package:new_islamic_app_uk/theme/app_colors.dart';
import 'package:new_islamic_app_uk/theme/app_text_styles.dart';
import 'package:new_islamic_app_uk/widgets/common/modern_card.dart';

/// Modern prayer times card widget based on the design images
class ModernPrayerTimesCard extends StatelessWidget {
  final String fajrTime;
  final String zuhrTime;
  final String asrTime;
  final String maghribTime;
  final String ishaTime;
  final String hijriDate;
  final String currentDate;
  final String nextPrayer;
  final int timeRemaining;

  const ModernPrayerTimesCard({
    Key? key,
    required this.fajrTime,
    required this.zuhrTime,
    required this.asrTime,
    required this.maghribTime,
    required this.ishaTime,
    required this.hijriDate,
    required this.currentDate,
    required this.nextPrayer,
    required this.timeRemaining,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      padding: EdgeInsets.zero,
      child: Column(
        children: [
          // Next Prayer Header
          _buildNextPrayerHeader(),
          
          // Prayer Times Grid
          _buildPrayerTimesGrid(),
          
          // Date Footer
          _buildDateFooter(),
        ],
      ),
    );
  }
  
  Widget _buildNextPrayerHeader() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppTheme.radiusMedium),
          topRight: Radius.circular(AppTheme.radiusMedium),
        ),
      ),
      child: Row(
        children: [
          // Prayer Icon
          Container(
            padding: const EdgeInsets.all(AppTheme.spacing12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
            ),
            child: const Icon(
              FontAwesomeIcons.clock,
              color: Colors.white,
              size: AppTheme.iconMedium,
            ),
          ),
          const SizedBox(width: AppTheme.spacing16),
          
          // Next Prayer Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Next Prayer',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                const SizedBox(height: AppTheme.spacing4),
                Text(
                  nextPrayer,
                  style: AppTextStyles.titleLarge.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
          ),
          
          // Countdown Timer
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacing16,
              vertical: AppTheme.spacing8,
            ),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppTheme.radiusCircular),
            ),
            child: CountdownTimer(
              endTime: DateTime.now().millisecondsSinceEpoch + timeRemaining,
              textStyle: AppTextStyles.labelLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
              onEnd: () {},
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildPrayerTimesGrid() {
    final prayerTimes = [
      {'name': 'Fajr', 'time': fajrTime, 'icon': FontAwesomeIcons.cloudSun},
      {'name': 'Zuhr', 'time': zuhrTime, 'icon': FontAwesomeIcons.sun},
      {'name': 'Asr', 'time': asrTime, 'icon': FontAwesomeIcons.cloudSun},
      {'name': 'Maghrib', 'time': maghribTime, 'icon': FontAwesomeIcons.cloudMoon},
      {'name': 'Isha', 'time': ishaTime, 'icon': FontAwesomeIcons.moon},
    ];
    
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacing16),
      child: Column(
        children: [
          for (int i = 0; i < prayerTimes.length; i++)
            Padding(
              padding: EdgeInsets.only(
                bottom: i < prayerTimes.length - 1 ? AppTheme.spacing12 : 0,
              ),
              child: _buildPrayerTimeItem(
                prayerTimes[i]['name'] as String,
                prayerTimes[i]['time'] as String,
                prayerTimes[i]['icon'] as IconData,
                nextPrayer == prayerTimes[i]['name'],
              ),
            ),
        ],
      ),
    );
  }
  
  Widget _buildPrayerTimeItem(String name, String time, IconData icon, bool isNext) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing12),
      decoration: BoxDecoration(
        color: isNext 
            ? AppColors.primary.withOpacity(0.1)
            : AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: isNext 
            ? Border.all(color: AppColors.primary, width: 2)
            : null,
      ),
      child: Row(
        children: [
          // Prayer Icon
          Container(
            padding: const EdgeInsets.all(AppTheme.spacing8),
            decoration: BoxDecoration(
              color: isNext 
                  ? AppColors.primary.withOpacity(0.2)
                  : AppColors.surface,
              borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
              boxShadow: [
                BoxShadow(
                  color: AppColors.cardShadow,
                  blurRadius: AppTheme.elevationLow * 2,
                  offset: const Offset(0, AppTheme.elevationLow),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: isNext 
                  ? AppColors.primary
                  : AppColors.textSecondary,
              size: AppTheme.iconSmall,
            ),
          ),
          const SizedBox(width: AppTheme.spacing12),
          
          // Prayer Name and Time
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: AppTextStyles.labelMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: AppTheme.spacing4),
                Text(
                  time,
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          
          // Next prayer indicator
          if (isNext)
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacing8,
                vertical: AppTheme.spacing4,
              ),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(AppTheme.radiusCircular),
              ),
              child: Text(
                'Next',
                style: AppTextStyles.labelSmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  Widget _buildDateFooter() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppTheme.radiusMedium),
          bottomRight: Radius.circular(AppTheme.radiusMedium),
        ),
      ),
      child: Row(
        children: [
          // Gregorian Date
          Expanded(
            child: Row(
              children: [
                Icon(
                  FontAwesomeIcons.calendar,
                  size: AppTheme.iconSmall,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: AppTheme.spacing8),
                Text(
                  currentDate,
                  style: AppTextStyles.bodySmall,
                ),
              ],
            ),
          ),
          
          // Hijri Date
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Icon(
                  FontAwesomeIcons.mosque,
                  size: AppTheme.iconSmall,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: AppTheme.spacing8),
                Text(
                  hijriDate,
                  style: AppTextStyles.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
