import 'package:flutter/material.dart';
import 'package:flutter_countdown_timer/flutter_countdown_timer.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/features/prayer_reminders/screens/prayer_reminder_dashboard.dart';
import 'package:new_islamic_app_uk/models/prayer_time.dart';

class PrayerTimesCard extends StatelessWidget {
  final String fajrTime;
  final String zuhrTime;
  final String asrTime;
  final String maghribTime;
  final String ishaTime;
  final String hijriDate;
  final String currentDate;
  final String nextPrayer;
  final int timeRemaining;

  const PrayerTimesCard({
    Key? key,
    required this.fajrTime,
    required this.zuhrTime,
    required this.asrTime,
    required this.maghribTime,
    required this.ishaTime,
    required this.hijriDate,
    required this.currentDate,
    required this.nextPrayer,
    required this.timeRemaining,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    final size = MediaQuery.of(context).size;
    
    return Card(
      elevation: urls.cardElevation,
      shadowColor: Colors.black.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(urls.cardBorderRadius),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(urls.cardBorderRadius),
          color: Colors.white,
        ),
        child: Column(
          children: [
            // Card Header - Next Prayer Countdown
            _buildNextPrayerHeader(urls),
            
            // Prayer Times Grid
            _buildPrayerTimesGrid(urls),
            
            // Date Info Footer
            _buildDateFooter(urls),
          ],
        ),
      ),
    );
  }
  
  Widget _buildNextPrayerHeader(URLs urls) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(urls.primaryColor),
            Color(urls.lightBlue),
          ],
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(urls.cardBorderRadius),
          topRight: Radius.circular(urls.cardBorderRadius),
        ),
      ),
      child: Row(
        children: [
          // Prayer Icon
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.access_time_rounded,
              color: Colors.white,
              size: 22,
            ),
          ),
          const SizedBox(width: 16),
          
          // Next Prayer Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Next Prayer',
                  style: GoogleFonts.poppins(
                    color: Colors.white.withOpacity(0.9),
                    fontWeight: FontWeight.w500,
                    fontSize: 13,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  nextPrayer,
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 18,
                  ),
                ),
              ],
            ),
          ),
          
          // Countdown Timer
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
            ),
            child: CountdownTimer(
              endTime: DateTime.now().millisecondsSinceEpoch + timeRemaining,
              textStyle: GoogleFonts.poppins(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
              onEnd: () {},
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildPrayerTimesGrid(URLs urls) {
    final prayerTimes = [
      {'name': 'Fajr', 'time': fajrTime, 'icon': Icons.wb_twilight},
      {'name': 'Zuhr', 'time': zuhrTime, 'icon': Icons.wb_sunny},
      {'name': 'Asr', 'time': asrTime, 'icon': Icons.wb_sunny_outlined},
      {'name': 'Maghrib', 'time': maghribTime, 'icon': Icons.nightlight_round},
      {'name': 'Isha', 'time': ishaTime, 'icon': Icons.nights_stay},
    ];
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          for (int i = 0; i < prayerTimes.length; i += 2)
            Padding(
              padding: EdgeInsets.only(bottom: i < prayerTimes.length - 2 ? 16 : 0),
              child: Row(
                children: [
                  // First prayer in the row
                  Expanded(
                    child: _buildPrayerTimeItem(
                      urls,
                      prayerTimes[i]['name'] as String,
                      prayerTimes[i]['time'] as String,
                      prayerTimes[i]['icon'] as IconData,
                      nextPrayer == prayerTimes[i]['name'],
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Second prayer in the row if exists
                  i + 1 < prayerTimes.length
                      ? Expanded(
                          child: _buildPrayerTimeItem(
                            urls,
                            prayerTimes[i + 1]['name'] as String,
                            prayerTimes[i + 1]['time'] as String,
                            prayerTimes[i + 1]['icon'] as IconData,
                            nextPrayer == prayerTimes[i + 1]['name'],
                          ),
                        )
                      : Expanded(child: Container()),
                ],
              ),
            ),
        ],
      ),
    );
  }
  
  Widget _buildPrayerTimeItem(URLs urls, String name, String time, IconData icon, bool isNext) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isNext 
            ? Color(urls.lightBlue).withOpacity(0.1)
            : Color(urls.lightGray),
        borderRadius: BorderRadius.circular(urls.defaultBorderRadius),
        border: isNext 
            ? Border.all(color: Color(urls.lightBlue), width: 1.5)
            : null,
      ),
      child: Row(
        children: [
          // Prayer Icon
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isNext 
                  ? Color(urls.lightBlue).withOpacity(0.2)
                  : Colors.white,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: isNext 
                  ? Color(urls.lightBlue)
                  : Color(urls.textSecondary),
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          
          // Prayer Name and Time
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: GoogleFonts.poppins(
                    color: Color(urls.textSecondary),
                    fontWeight: FontWeight.w500,
                    fontSize: 13,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  time,
                  style: GoogleFonts.poppins(
                    color: Color(urls.textPrimary),
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildDateFooter(URLs urls) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Color(urls.lightGray),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(urls.cardBorderRadius),
          bottomRight: Radius.circular(urls.cardBorderRadius),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Gregorian Date
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 16,
                color: Color(urls.textSecondary),
              ),
              const SizedBox(width: 8),
              Text(
                currentDate,
                style: GoogleFonts.poppins(
                  color: Color(urls.textSecondary),
                  fontWeight: FontWeight.w500,
                  fontSize: 13,
                ),
              ),
            ],
          ),
          
          // Hijri Date
          Row(
            children: [
              Icon(
                Icons.brightness_4_outlined,
                size: 16,
                color: Color(urls.textSecondary),
              ),
              const SizedBox(width: 8),
              Text(
                hijriDate,
                style: GoogleFonts.poppins(
                  color: Color(urls.textSecondary),
                  fontWeight: FontWeight.w500,
                  fontSize: 13,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
} 
