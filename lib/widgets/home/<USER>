import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';

class QuickActions extends StatelessWidget {
  const QuickActions({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final urls = URLs();
    
    // List of quick actions for the app
    final quickActions = [
      {
        'title': 'Prayer Times',
        'icon': Icons.access_time_rounded,
        'color': urls.primaryColor,
        'route': '/prayer_times',
      },
      {
        'title': 'Qibla Finder',
        'icon': Icons.explore_outlined,
        'color': urls.accentColor,
        'route': '/qibla_compass',
      },
      {
        'title': 'Quran',
        'icon': Icons.menu_book_rounded,
        'color': urls.pastelPurple,
        'route': '/quran',
      },
      {
        'title': 'Duas',
        'icon': Icons.favorite_outline_rounded,
        'color': urls.pastelPink,
        'route': '/duas',
      },
      {
        'title': '<PERSON>aka<PERSON>',
        'icon': Icons.calculate_outlined,
        'color': urls.colorGold,
        'route': '/zakat_calculator',
      },
      {
        'title': 'Events',
        'icon': Icons.event_available_outlined,
        'color': urls.primaryColor,
        'route': '/events',
      },
      {
        'title': 'Audio',
        'icon': Icons.headphones_outlined,
        'color': urls.accentColor,
        'route': '/audio',
      },
      {
        'title': 'Hadith',
        'icon': Icons.auto_stories_outlined,
        'color': urls.pastelPurple,
        'route': '/hadith',
      },
    ];
    
    return Card(
      elevation: urls.cardElevation,
      shadowColor: Colors.black.withOpacity(0.05),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(urls.cardBorderRadius),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(urls.cardBorderRadius),
          color: Colors.white,
        ),
        child: LayoutBuilder(
          builder: (context, constraints) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Grid of quick action buttons with proper constraints and fixed height
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 4,
                    childAspectRatio: 0.9,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: quickActions.length,
                  itemBuilder: (context, index) {
                    final action = quickActions[index];
                    return _buildQuickActionItem(
                      context,
                      urls,
                      action['title'] as String,
                      action['icon'] as IconData,
                      action['color'] as int,
                      action['route'] as String,
                    );
                  },
                ),
              ],
            );
          }
        ),
      ),
    );
  }
  
  Widget _buildQuickActionItem(
    BuildContext context, 
    URLs urls, 
    String title, 
    IconData icon, 
    int color,
    String route,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          debugPrint('Navigating to $route');
          try {
            // Check for the '/quran' route specifically to ensure it works properly
            if (route == '/quran') {
              Navigator.of(context, rootNavigator: true).pushNamed(route);
            } else {
              Navigator.of(context).pushNamed(route);
            }
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Could not navigate to $title. ${e.toString()}'),
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(12),
        splashColor: Color(color).withOpacity(0.2),
        highlightColor: Color(color).withOpacity(0.1),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Color(color).withOpacity(0.1),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Color(color).withOpacity(0.2),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: Color(color),
                size: 24,
              ),
            ),
            const SizedBox(height: 6),
            Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: 11,
                fontWeight: FontWeight.w500,
                color: Color(urls.textPrimary),
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
} 