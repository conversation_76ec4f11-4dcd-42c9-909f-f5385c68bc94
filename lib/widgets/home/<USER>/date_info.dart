import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';

class DateInfo extends StatelessWidget {
  final String hijriDate;
  final VoidCallback? onPrayerTimesTap;

  const DateInfo({
    Key? key,
    required this.hijriDate,
    this.onPrayerTimesTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final formatter = DateFormat('EEEE, d MMMM yyyy');
    final formattedDate = formatter.format(now);

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      physics: const BouncingScrollPhysics(),
      child: Row(
        children: [
          _buildDateChip(
            icon: Icons.calendar_today_rounded,
            label: formattedDate,
            color: Colors.white.withOpacity(0.15),
          ),
          const SizedBox(width: 8),
          _buildDateChip(
            icon: FontAwesomeIcons.solidMoon,
            label: hijriDate,
            color: Colors.white.withOpacity(0.2),
          ),
          const SizedBox(width: 8),
          _buildDateChip(
            icon: Icons.location_on_rounded,
            label: "Nelson, UK",
            color: Colors.white.withOpacity(0.15),
          ),
          const SizedBox(width: 8),
          _buildDateChip(
            icon: Icons.wb_sunny_rounded, 
            label: "Prayer Times",
            color: Colors.amber.withOpacity(0.25),
            isHighlighted: true,
            onTap: onPrayerTimesTap,
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms, delay: 200.ms);
  }
  
  Widget _buildDateChip({
    required IconData icon, 
    required String label, 
    required Color color,
    bool isHighlighted = false,
    VoidCallback? onTap,
  }) {
    final chip = Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          color: isHighlighted 
              ? Colors.amber.withOpacity(0.5) 
              : Colors.white.withOpacity(0.15),
          width: 1,
        ),
        boxShadow: isHighlighted ? [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: isHighlighted ? Colors.white : Colors.white.withOpacity(0.9),
            size: 14,
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.w500,
              color: isHighlighted ? Colors.white : Colors.white.withOpacity(0.9),
            ),
          ),
        ],
      ),
    );
    
    if (onTap != null) {
      return GestureDetector(
        onTap: onTap,
        child: chip,
      );
    }
    
    return chip;
  }
} 