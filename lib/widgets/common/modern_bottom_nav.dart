import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../theme/app_theme.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_text_styles.dart';

/// Modern bottom navigation bar based on the design images
class ModernBottomNav extends StatelessWidget {
  final int currentIndex;
  final ValueChanged<int> onTap;
  final List<ModernBottomNavItem> items;

  const ModernBottomNav({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: AppTheme.elevationMedium * 2,
            offset: const Offset(0, -AppTheme.elevationMedium),
          ),
        ],
      ),
      child: Safe<PERSON>rea(
        child: Container(
          height: 70,
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacing16,
            vertical: AppTheme.spacing8,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isSelected = index == currentIndex;
              
              return Expanded(
                child: GestureDetector(
                  onTap: () => onTap(index),
                  behavior: HitTestBehavior.opaque,
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    padding: const EdgeInsets.symmetric(
                      vertical: AppTheme.spacing8,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          padding: const EdgeInsets.all(AppTheme.spacing8),
                          decoration: BoxDecoration(
                            color: isSelected 
                                ? AppColors.primary.withOpacity(0.1)
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                          ),
                          child: Icon(
                            item.icon,
                            color: isSelected 
                                ? AppColors.primary 
                                : AppColors.textTertiary,
                            size: AppTheme.iconMedium,
                          ),
                        ),
                        const SizedBox(height: AppTheme.spacing4),
                        Text(
                          item.label,
                          style: AppTextStyles.labelSmall.copyWith(
                            color: isSelected 
                                ? AppColors.primary 
                                : AppColors.textTertiary,
                            fontWeight: isSelected 
                                ? FontWeight.w600 
                                : FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}

/// Bottom navigation item model
class ModernBottomNavItem {
  final IconData icon;
  final String label;
  final int? badge;

  const ModernBottomNavItem({
    required this.icon,
    required this.label,
    this.badge,
  });
}

/// Default bottom navigation items for the app
class AppBottomNavItems {
  static List<ModernBottomNavItem> get defaultItems => [
    const ModernBottomNavItem(
      icon: FontAwesomeIcons.house,
      label: 'Home',
    ),
    const ModernBottomNavItem(
      icon: FontAwesomeIcons.clockRotateLeft,
      label: 'Prayer Times',
    ),
    const ModernBottomNavItem(
      icon: FontAwesomeIcons.handsPraying,
      label: 'Duas',
    ),
    const ModernBottomNavItem(
      icon: FontAwesomeIcons.bookQuran,
      label: 'Quran',
    ),
    const ModernBottomNavItem(
      icon: FontAwesomeIcons.ellipsis,
      label: 'More',
    ),
  ];
}

/// Floating action button style bottom nav (alternative design)
class FloatingBottomNav extends StatelessWidget {
  final int currentIndex;
  final ValueChanged<int> onTap;
  final List<ModernBottomNavItem> items;

  const FloatingBottomNav({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacing16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
            boxShadow: [
              BoxShadow(
                color: AppColors.cardShadow,
                blurRadius: AppTheme.elevationHigh * 2,
                offset: const Offset(0, AppTheme.elevationHigh),
              ),
            ],
          ),
          child: SafeArea(
            child: Container(
              height: 60,
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacing20,
                vertical: AppTheme.spacing8,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: items.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;
                  final isSelected = index == currentIndex;
                  
                  return GestureDetector(
                    onTap: () => onTap(index),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacing12,
                        vertical: AppTheme.spacing8,
                      ),
                      decoration: BoxDecoration(
                        color: isSelected 
                            ? AppColors.primary 
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(AppTheme.radiusCircular),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            item.icon,
                            color: isSelected 
                                ? Colors.white 
                                : AppColors.textTertiary,
                            size: AppTheme.iconMedium,
                          ),
                          if (isSelected) ...[
                            const SizedBox(width: AppTheme.spacing8),
                            Text(
                              item.label,
                              style: AppTextStyles.labelMedium.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
