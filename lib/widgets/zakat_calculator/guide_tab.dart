import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class GuideTab extends StatelessWidget {
  const GuideTab({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildIntroCard(),
            const SizedBox(height: 24),
            
            // Zakat Basics
            _buildSectionTitle('Understanding Zakat'),
            const SizedBox(height: 16),
            _buildGuideCard(
              title: 'What is Zaka<PERSON>?',
              icon: FontAwesomeIcons.handHoldingDollar,
              iconColor: Color(URLs().colorScheme),
              content: '<PERSON><PERSON><PERSON> is one of the five pillars of Islam. It is a form of obligatory charity that has a specific calculation method. Every Muslim who possesses wealth above a certain threshold (Nisab) for one lunar year is obligated to pay 2.5% of that wealth as Zakat.',
              delay: 0,
            ),
            _buildGuideCard(
              title: 'Who Must Pay Zakat?',
              icon: FontAwesomeIcons.userCheck,
              iconColor: Colors.green[700]!,
              content: 'Zakat is obligatory upon every Muslim who possesses wealth equal to or greater than the Nisab threshold for one lunar year (Hawl). The Nisab is approximately the value of 87.48 grams of gold or 612.36 grams of silver, whichever is less.',
              delay: 100,
            ),
            _buildGuideCard(
              title: 'When to Pay Zakat?',
              icon: FontAwesomeIcons.calendarCheck,
              iconColor: Colors.blue[700]!,
              content: 'Zakat is due after one lunar year (Hawl) has passed on the wealth that reaches the Nisab threshold. Many Muslims choose to pay their Zakat during Ramadan as good deeds are multiplied in reward during this blessed month. However, you can pay your Zakat at any time of the year when it becomes due.',
              delay: 200,
            ),
            
            const SizedBox(height: 24),
            
            // Zakatable Assets
            _buildSectionTitle('Zakatable Assets'),
            const SizedBox(height: 16),
            _buildAssetsList(
              title: 'What Assets are Zakatable?',
              icon: FontAwesomeIcons.moneyBillWave,
              iconColor: Colors.amber[800]!,
              assets: [
                'Gold and silver',
                'Cash and bank balances',
                'Stocks and shares intended for resale',
                'Business inventory and merchandise',
                'Rental income and investment properties',
                'Retirement accounts that you have access to',
                'Loans that are expected to be repaid',
              ],
              delay: 300,
            ),
            _buildAssetsList(
              title: 'What Assets are NOT Zakatable?',
              icon: FontAwesomeIcons.ban,
              iconColor: Colors.red[700]!,
              assets: [
                'Personal residence',
                'Personal vehicles',
                'Furniture and household items',
                'Clothing and personal effects',
                'Tools of your trade or profession',
                'Debt that you owe to others',
              ],
              delay: 400,
            ),
            
            const SizedBox(height: 24),
            
            // Recipients
            _buildSectionTitle('Zakat Recipients'),
            const SizedBox(height: 16),
            _buildRecipientsCard(delay: 500),
            
            const SizedBox(height: 24),
            
            // Sadaqah
            _buildSectionTitle('Sadaqah (Voluntary Charity)'),
            const SizedBox(height: 16),
            _buildGuideCard(
              title: 'What is Sadaqah?',
              icon: FontAwesomeIcons.heartCircleCheck,
              iconColor: Colors.pink[400]!,
              content: 'Sadaqah is voluntary charity given beyond the obligatory Zakat. There is no specific amount or time for Sadaqah - it can be given at any time and in any amount. The Prophet Muhammad (peace be upon him) encouraged Muslims to give Sadaqah regularly, even if it is a small amount.',
              delay: 600,
            ),
            
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildIntroCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(URLs().colorScheme).withOpacity(0.8),
            Color(URLs().colorScheme),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Color(URLs().colorScheme).withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: Colors.white.withOpacity(0.2),
                child: Icon(
                  FontAwesomeIcons.bookOpen,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Zakat Guide',
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      'Understanding the principles of Zakat',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            '"Take from their wealth a charity by which you purify them and cause them increase."',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontStyle: FontStyle.italic,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Quran 9:103',
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.white.withOpacity(0.8),
            ),
            textAlign: TextAlign.right,
          ),
        ],
      ),
    ).animate().fadeIn(duration: 400.ms).slideY(begin: 0.1, end: 0);
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Colors.grey[800],
      ),
    );
  }

  Widget _buildGuideCard({
    required String title,
    required IconData icon,
    required Color iconColor,
    required String content,
    required int delay,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 18,
                backgroundColor: iconColor.withOpacity(0.1),
                child: Icon(
                  icon,
                  size: 16,
                  color: iconColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[800],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            content,
            style: GoogleFonts.poppins(
              fontSize: 14,
              height: 1.5,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 400.ms, delay: Duration(milliseconds: delay)).slideY(begin: 0.1, end: 0);
  }

  Widget _buildAssetsList({
    required String title,
    required IconData icon,
    required Color iconColor,
    required List<String> assets,
    required int delay,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 18,
                backgroundColor: iconColor.withOpacity(0.1),
                child: Icon(
                  icon,
                  size: 16,
                  color: iconColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[800],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...assets.map((asset) => _buildAssetItem(asset, iconColor)),
        ],
      ),
    ).animate().fadeIn(duration: 400.ms, delay: Duration(milliseconds: delay)).slideY(begin: 0.1, end: 0);
  }

  Widget _buildAssetItem(String asset, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.circle,
            size: 8,
            color: color,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              asset,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecipientsCard({required int delay}) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 18,
                backgroundColor: Colors.purple[100],
                child: Icon(
                  FontAwesomeIcons.handHolding,
                  size: 16,
                  color: Colors.purple[700],
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Who Can Receive Zakat?',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[800],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'The Quran (9:60) specifies eight categories of people who can receive Zakat:',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
          _buildRecipientItem(
            number: '1',
            title: 'The Poor (Al-Fuqara)',
            description: 'Those who don\'t have enough to meet their basic needs',
          ),
          _buildRecipientItem(
            number: '2',
            title: 'The Needy (Al-Masakin)',
            description: 'Those in difficulty who are unable to meet their basic needs',
          ),
          _buildRecipientItem(
            number: '3',
            title: 'Zakat Administrators',
            description: 'Those employed to collect and distribute Zakat',
          ),
          _buildRecipientItem(
            number: '4',
            title: 'Those Whose Hearts Are to Be Reconciled',
            description: 'New Muslims and those who need strengthening in faith',
          ),
          _buildRecipientItem(
            number: '5',
            title: 'Those in Bondage',
            description: 'Captives and those in slavery or bondage',
          ),
          _buildRecipientItem(
            number: '6',
            title: 'Those in Debt',
            description: 'People burdened with debt for legitimate needs',
          ),
          _buildRecipientItem(
            number: '7',
            title: 'In the Cause of Allah',
            description: 'Those striving in the path of Allah',
          ),
          _buildRecipientItem(
            number: '8',
            title: 'The Wayfarer',
            description: 'Travelers who are stranded and in need',
            isLast: true,
          ),
        ],
      ),
    ).animate().fadeIn(duration: 400.ms, delay: Duration(milliseconds: delay)).slideY(begin: 0.1, end: 0);
  }

  Widget _buildRecipientItem({
    required String number,
    required String title,
    required String description,
    bool isLast = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 24,
          height: 24,
          margin: const EdgeInsets.only(top: 2),
          decoration: BoxDecoration(
            color: Color(URLs().colorScheme),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              number,
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              Text(
                description,
                style: GoogleFonts.poppins(
                  fontSize: 13,
                  color: Colors.grey[600],
                ),
              ),
              if (!isLast) const SizedBox(height: 12),
            ],
          ),
        ),
      ],
    );
  }
} 