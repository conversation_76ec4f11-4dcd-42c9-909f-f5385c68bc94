import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:new_islamic_app_uk/models/zakat_model.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:flutter_animate/flutter_animate.dart';

class CalculatorTab extends StatefulWidget {
  final ZakatModel zakatModel;
  final GlobalKey<FormState> formKey;
  final VoidCallback onCalculate;

  const CalculatorTab({
    Key? key,
    required this.zakatModel,
    required this.formKey,
    required this.onCalculate,
  }) : super(key: key);

  @override
  _CalculatorTabState createState() => _CalculatorTabState();
}

class _CalculatorTabState extends State<CalculatorTab> {
  bool _showGoldWeightInput = false;
  bool _showSilverWeightInput = false;
  bool _showMetalPrices = false;
  
  // Controllers for weight inputs
  final TextEditingController _goldWeightController = TextEditingController();
  final TextEditingController _silverWeightController = TextEditingController();
  final TextEditingController _goldPriceController = TextEditingController();
  final TextEditingController _silverPriceController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    _goldPriceController.text = widget.zakatModel.goldPrice.toString();
    _silverPriceController.text = widget.zakatModel.silverPrice.toString();
  }
  
  @override
  void dispose() {
    _goldWeightController.dispose();
    _silverWeightController.dispose();
    _goldPriceController.dispose();
    _silverPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Form(
        key: widget.formKey,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Metal prices section
              _buildMetalPricesSection(),
              const SizedBox(height: 24),
              
              // Assets section
              _buildSectionTitle('Your Assets', FontAwesomeIcons.coins),
              const SizedBox(height: 16),
              
              // Gold section
              _buildAssetCard(
                title: 'Gold',
                icon: FontAwesomeIcons.solidCircle,
                iconColor: Colors.amber[700]!,
                children: [
                  _buildCurrencyTextField(
                    label: 'Gold Value',
                    hint: 'Value of gold you own',
                    onChanged: (value) {
                      setState(() {
                        widget.zakatModel.goldValue = double.tryParse(value) ?? 0;
                      });
                    },
                    suffixIcon: IconButton(
                      icon: Icon(
                        _showGoldWeightInput 
                            ? Icons.expand_less
                            : Icons.expand_more,
                        color: Color(URLs().colorScheme),
                      ),
                      onPressed: () {
                        setState(() {
                          _showGoldWeightInput = !_showGoldWeightInput;
                        });
                      },
                    ),
                  ),
                  if (_showGoldWeightInput)
                    _buildWeightInputField(
                      label: 'Gold Weight (grams)',
                      controller: _goldWeightController,
                      onCalculate: () {
                        final weight = double.tryParse(_goldWeightController.text) ?? 0;
                        final value = widget.zakatModel.calculateGoldValueFromGrams(weight);
                        setState(() {
                          widget.zakatModel.goldValue = value;
                        });
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Gold value calculated: ${widget.zakatModel.currencyFormatter.format(value)}'),
                            backgroundColor: Color(URLs().colorScheme),
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        );
                      },
                    ).animate().fadeIn(duration: 300.ms).slideY(begin: 0.2, end: 0),
                ],
              ),
              
              // Silver section
              _buildAssetCard(
                title: 'Silver',
                icon: FontAwesomeIcons.solidCircle,
                iconColor: Colors.blueGrey[400]!,
                children: [
                  _buildCurrencyTextField(
                    label: 'Silver Value',
                    hint: 'Value of silver you own',
                    onChanged: (value) {
                      setState(() {
                        widget.zakatModel.silverValue = double.tryParse(value) ?? 0;
                      });
                    },
                    suffixIcon: IconButton(
                      icon: Icon(
                        _showSilverWeightInput 
                            ? Icons.expand_less
                            : Icons.expand_more,
                        color: Color(URLs().colorScheme),
                      ),
                      onPressed: () {
                        setState(() {
                          _showSilverWeightInput = !_showSilverWeightInput;
                        });
                      },
                    ),
                  ),
                  if (_showSilverWeightInput)
                    _buildWeightInputField(
                      label: 'Silver Weight (grams)',
                      controller: _silverWeightController,
                      onCalculate: () {
                        final weight = double.tryParse(_silverWeightController.text) ?? 0;
                        final value = widget.zakatModel.calculateSilverValueFromGrams(weight);
                        setState(() {
                          widget.zakatModel.silverValue = value;
                        });
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Silver value calculated: ${widget.zakatModel.currencyFormatter.format(value)}'),
                            backgroundColor: Color(URLs().colorScheme),
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        );
                      },
                    ).animate().fadeIn(duration: 300.ms).slideY(begin: 0.2, end: 0),
                ],
              ),
              
              // Cash section
              _buildAssetCard(
                title: 'Cash & Savings',
                icon: FontAwesomeIcons.moneyBill,
                iconColor: Colors.green[700]!,
                children: [
                  _buildCurrencyTextField(
                    label: 'Cash in Hand',
                    hint: 'Cash you physically possess',
                    onChanged: (value) {
                      setState(() {
                        widget.zakatModel.cashInHand = double.tryParse(value) ?? 0;
                      });
                    },
                  ),
                  const SizedBox(height: 12),
                  _buildCurrencyTextField(
                    label: 'Bank Deposits',
                    hint: 'Money in bank accounts',
                    onChanged: (value) {
                      setState(() {
                        widget.zakatModel.bankDeposits = double.tryParse(value) ?? 0;
                      });
                    },
                  ),
                  const SizedBox(height: 12),
                  _buildCurrencyTextField(
                    label: 'Money Loaned to Others',
                    hint: 'Money expected to be returned',
                    onChanged: (value) {
                      setState(() {
                        widget.zakatModel.cashLoaned = double.tryParse(value) ?? 0;
                      });
                    },
                  ),
                  const SizedBox(height: 12),
                  _buildCurrencyTextField(
                    label: 'Other Cash Assets',
                    hint: 'Any other cash-equivalent assets',
                    onChanged: (value) {
                      setState(() {
                        widget.zakatModel.otherCash = double.tryParse(value) ?? 0;
                      });
                    },
                  ),
                ],
              ),
              
              // Investments section
              _buildAssetCard(
                title: 'Investments',
                icon: FontAwesomeIcons.chartLine,
                iconColor: Colors.blue[700]!,
                children: [
                  _buildCurrencyTextField(
                    label: 'Stocks & Shares',
                    hint: 'Value of stocks and shares owned',
                    onChanged: (value) {
                      setState(() {
                        widget.zakatModel.stocks = double.tryParse(value) ?? 0;
                      });
                    },
                  ),
                  const SizedBox(height: 12),
                  _buildCurrencyTextField(
                    label: 'Business Assets',
                    hint: 'Value of business assets',
                    onChanged: (value) {
                      setState(() {
                        widget.zakatModel.businessAssets = double.tryParse(value) ?? 0;
                      });
                    },
                  ),
                  const SizedBox(height: 12),
                  _buildCurrencyTextField(
                    label: 'Investment Properties',
                    hint: 'Value of real estate for investment',
                    onChanged: (value) {
                      setState(() {
                        widget.zakatModel.realEstate = double.tryParse(value) ?? 0;
                      });
                    },
                  ),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // Liabilities section
              _buildSectionTitle('Your Liabilities', FontAwesomeIcons.circleMinus),
              const SizedBox(height: 16),
              
              _buildAssetCard(
                title: 'Debts & Expenses',
                icon: FontAwesomeIcons.handHoldingDollar,
                iconColor: Colors.red[700]!,
                children: [
                  _buildCurrencyTextField(
                    label: 'Debts & Loans',
                    hint: 'Money you owe to others',
                    onChanged: (value) {
                      setState(() {
                        widget.zakatModel.debts = double.tryParse(value) ?? 0;
                      });
                    },
                  ),
                  const SizedBox(height: 12),
                  _buildCurrencyTextField(
                    label: 'Due Expenses',
                    hint: 'Expenses that are due for payment',
                    onChanged: (value) {
                      setState(() {
                        widget.zakatModel.expenses = double.tryParse(value) ?? 0;
                      });
                    },
                  ),
                ],
              ),
              
              const SizedBox(height: 40),
              
              // Calculate button
              Center(
                child: ElevatedButton(
                  onPressed: widget.onCalculate,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(URLs().colorScheme),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    elevation: 5,
                    shadowColor: Color(URLs().colorScheme).withOpacity(0.5),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.calculate_rounded, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Calculate Zakat',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ).animate().fadeIn(duration: 400.ms).scale(begin: const Offset(0.95, 0.95)),
              ),
              
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMetalPricesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              _showMetalPrices = !_showMetalPrices;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Row(
              children: [
                Icon(
                  FontAwesomeIcons.coins,
                  size: 16,
                  color: Color(URLs().colorScheme),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Metal Prices & Nisab Thresholds',
                    style: GoogleFonts.poppins(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                ),
                Icon(
                  _showMetalPrices ? Icons.expand_less : Icons.expand_more,
                  color: Color(URLs().colorScheme),
                ),
              ],
            ),
          ),
        ),
        if (_showMetalPrices)
          Container(
            margin: const EdgeInsets.only(top: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.grey[200]!),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Current Metal Prices',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(URLs().colorScheme),
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildPriceInputField(
                        label: 'Gold (per ounce)',
                        controller: _goldPriceController,
                        color: Colors.amber[700]!,
                        onChanged: (value) {
                          final price = double.tryParse(value) ?? widget.zakatModel.goldPrice;
                          setState(() {
                            widget.zakatModel.goldPrice = price;
                            widget.zakatModel.calculateNisabThresholds();
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildPriceInputField(
                        label: 'Silver (per ounce)',
                        controller: _silverPriceController,
                        color: Colors.blueGrey[400]!,
                        onChanged: (value) {
                          final price = double.tryParse(value) ?? widget.zakatModel.silverPrice;
                          setState(() {
                            widget.zakatModel.silverPrice = price;
                            widget.zakatModel.calculateNisabThresholds();
                          });
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'Nisab Thresholds',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(URLs().colorScheme),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[200]!),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Gold Nisab (87.48g):',
                            style: GoogleFonts.poppins(
                              fontSize: 13,
                              color: Colors.grey[700],
                            ),
                          ),
                          Text(
                            widget.zakatModel.currencyFormatter.format(widget.zakatModel.goldNisab),
                            style: GoogleFonts.poppins(
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                              color: Colors.amber[800],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Silver Nisab (612.36g):',
                            style: GoogleFonts.poppins(
                              fontSize: 13,
                              color: Colors.grey[700],
                            ),
                          ),
                          Text(
                            widget.zakatModel.currencyFormatter.format(widget.zakatModel.silverNisab),
                            style: GoogleFonts.poppins(
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                              color: Colors.blueGrey[700],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Note: Zakat is due when your wealth equals or exceeds the lower of these two thresholds for one lunar year.',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontStyle: FontStyle.italic,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ).animate().fadeIn(duration: 300.ms).slideY(begin: 0.2, end: 0),
      ],
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 18,
          color: Color(URLs().colorScheme),
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.grey[800],
          ),
        ),
      ],
    );
  }

  Widget _buildAssetCard({
    required String title,
    required IconData icon,
    required Color iconColor,
    required List<Widget> children,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: iconColor),
              const SizedBox(width: 8),
              Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    ).animate().fadeIn(duration: 400.ms).slideY(begin: 0.1, end: 0);
  }

  Widget _buildCurrencyTextField({
    required String label,
    required String hint,
    required Function(String) onChanged,
    Widget? suffixIcon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 6),
        TextFormField(
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[400],
            ),
            prefixText: '£ ',
            prefixStyle: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            suffixIcon: suffixIcon,
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Color(URLs().colorScheme), width: 1.5),
            ),
            filled: true,
            fillColor: Colors.grey[50],
          ),
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          keyboardType: TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
          ],
          onChanged: onChanged,
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              try {
                double.parse(value);
              } catch (e) {
                return 'Please enter a valid number';
              }
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPriceInputField({
    required String label,
    required TextEditingController controller,
    required Color color,
    required Function(String) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 13,
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 6),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            prefixText: '£ ',
            prefixStyle: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: color,
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: color, width: 1.5),
            ),
            filled: true,
            fillColor: Colors.white,
          ),
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: color,
          ),
          keyboardType: TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
          ],
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildWeightInputField({
    required String label,
    required TextEditingController controller,
    required VoidCallback onCalculate,
  }) {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: controller,
                  decoration: InputDecoration(
                    hintText: 'Enter weight',
                    hintStyle: GoogleFonts.poppins(
                      fontSize: 13,
                      color: Colors.grey[400],
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Color(URLs().colorScheme), width: 1.5),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: onCalculate,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Color(URLs().colorScheme),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  'Calculate',
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
} 