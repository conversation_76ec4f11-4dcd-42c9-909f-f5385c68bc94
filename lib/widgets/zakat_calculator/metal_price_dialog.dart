import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:flutter_animate/flutter_animate.dart';

class MetalPriceDialog extends StatefulWidget {
  final double initialGoldPrice;
  final double initialSilverPrice;

  const MetalPriceDialog({
    Key? key,
    required this.initialGoldPrice,
    required this.initialSilverPrice,
  }) : super(key: key);

  @override
  _MetalPriceDialogState createState() => _MetalPriceDialogState();
}

class _MetalPriceDialogState extends State<MetalPriceDialog> {
  late TextEditingController _goldPriceController;
  late TextEditingController _silverPriceController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _goldPriceController = TextEditingController(text: widget.initialGoldPrice.toString());
    _silverPriceController = TextEditingController(text: widget.initialSilverPrice.toString());
  }

  @override
  void dispose() {
    _goldPriceController.dispose();
    _silverPriceController.dispose();
    super.dispose();
  }

  Future<void> _fetchLatestPrices() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate API call with a delay
    await Future.delayed(const Duration(seconds: 2));

    // In a real app, you would fetch actual prices from an API
    // For now, we'll just use some realistic values
    setState(() {
      _goldPriceController.text = '2450.75';
      _silverPriceController.text = '28.50';
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Update Metal Prices',
              style: GoogleFonts.sourceSans3(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(URLs().colorScheme),
              ),
            ).animate().fadeIn(duration: 300.ms).slideX(begin: -0.1, end: 0),
            const SizedBox(height: 8),
            Text(
              'Enter current market prices for accurate Zakat calculation',
              style: GoogleFonts.sourceSans3(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ).animate().fadeIn(duration: 300.ms, delay: 100.ms).slideX(begin: -0.1, end: 0),
            const SizedBox(height: 20),
            _buildPriceField(
              label: 'Gold Price (per ounce)',
              controller: _goldPriceController,
              icon: Icons.attach_money,
              color: Colors.amber[800]!,
            ).animate().fadeIn(duration: 300.ms, delay: 200.ms),
            const SizedBox(height: 16),
            _buildPriceField(
              label: 'Silver Price (per ounce)',
              controller: _silverPriceController,
              icon: Icons.attach_money,
              color: Colors.blueGrey[400]!,
            ).animate().fadeIn(duration: 300.ms, delay: 300.ms),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton.icon(
                  icon: _isLoading 
                      ? SizedBox(
                          width: 20, 
                          height: 20, 
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Color(URLs().colorScheme),
                          ),
                        )
                      : Icon(Icons.refresh),
                  label: Text(_isLoading ? 'Fetching...' : 'Fetch Latest'),
                  onPressed: _isLoading ? null : _fetchLatestPrices,
                  style: TextButton.styleFrom(
                    foregroundColor: Color(URLs().colorScheme),
                  ),
                ).animate().fadeIn(duration: 300.ms, delay: 400.ms),
                Row(
                  children: [
                    TextButton(
                      child: Text('Cancel'),
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      child: Text('Update'),
                      onPressed: () {
                        final goldPrice = double.tryParse(_goldPriceController.text) ?? widget.initialGoldPrice;
                        final silverPrice = double.tryParse(_silverPriceController.text) ?? widget.initialSilverPrice;
                        
                        Navigator.of(context).pop({
                          'goldPrice': goldPrice,
                          'silverPrice': silverPrice,
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Color(URLs().colorScheme),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ],
                ).animate().fadeIn(duration: 300.ms, delay: 500.ms),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceField({
    required String label,
    required TextEditingController controller,
    required IconData icon,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.sourceSans3(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            prefixIcon: Icon(icon, color: color),
            prefixText: '£ ',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor: Colors.white,
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Color(URLs().colorScheme), width: 2),
            ),
          ),
          keyboardType: TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
          ],
        ),
      ],
    );
  }
} 