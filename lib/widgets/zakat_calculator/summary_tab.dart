import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/models/zakat_model.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class SummaryTab extends StatefulWidget {
  final ZakatModel zakatModel;
  final VoidCallback onEditValues;

  const SummaryTab({
    Key? key,
    required this.zakatModel,
    required this.onEditValues,
  }) : super(key: key);

  @override
  _SummaryTabState createState() => _SummaryTabState();
}

class _SummaryTabState extends State<SummaryTab> {
  @override
  Widget build(BuildContext context) {
    double totalWealth = widget.zakatModel.calculateTotalWealth();
    double totalLiabilities = widget.zakatModel.calculateTotalLiabilities();
    double netWorth = widget.zakatModel.calculateNetWorth();
    double zakatAmount = widget.zakatModel.calculateZakat();
    bool isZakatDue = widget.zakatModel.isZakatDue();
    
    // Calculate percentages for visualization
    double assetsPercentage = totalWealth > 0 ? (totalWealth / (totalWealth + totalLiabilities)) : 0;
    double liabilitiesPercentage = totalWealth > 0 ? (totalLiabilities / (totalWealth + totalLiabilities)) : 0;
    
    // Get the applicable Nisab threshold
    double applicableNisab = widget.zakatModel.goldNisab < widget.zakatModel.silverNisab 
        ? widget.zakatModel.goldNisab 
        : widget.zakatModel.silverNisab;
    
    // Calculate percentage of wealth to Nisab
    double wealthToNisabRatio = netWorth > 0 ? (netWorth / applicableNisab) : 0;
    double wealthToNisabPercentage = wealthToNisabRatio > 1 ? 1 : wealthToNisabRatio;
    
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Zakat Status Card
            _buildZakatStatusCard(isZakatDue, zakatAmount, netWorth),
            const SizedBox(height: 24),
            
            // Wealth Breakdown
            _buildSectionTitle('Your Wealth Breakdown'),
            const SizedBox(height: 16),
            
            _buildWealthBreakdownCard(
              totalWealth: totalWealth,
              totalLiabilities: totalLiabilities,
              netWorth: netWorth,
              assetsPercentage: assetsPercentage,
            ),
            const SizedBox(height: 24),
            
            // Nisab Comparison
            _buildSectionTitle('Nisab Threshold Comparison'),
            const SizedBox(height: 16),
            
            _buildNisabComparisonCard(
              netWorth: netWorth,
              applicableNisab: applicableNisab,
              wealthToNisabPercentage: wealthToNisabPercentage,
              isZakatDue: isZakatDue,
            ),
            const SizedBox(height: 24),
            
            // Sadaqah Suggestion
            _buildSadaqahSuggestionCard(),
            const SizedBox(height: 32),
            
            // Edit Values Button
            Center(
              child: ElevatedButton.icon(
                icon: const Icon(Icons.edit_rounded, size: 18),
                label: Text(
                  'Edit Values',
                  style: GoogleFonts.poppins(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[200],
                  foregroundColor: Colors.grey[800],
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                  elevation: 0,
                ),
                onPressed: widget.onEditValues,
              ).animate().fadeIn(duration: 400.ms).scale(begin: const Offset(0.95, 0.95)),
            ),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Colors.grey[800],
      ),
    );
  }

  Widget _buildZakatStatusCard(bool isZakatDue, double zakatAmount, double netWorth) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isZakatDue 
              ? [Color(URLs().colorScheme).withOpacity(0.8), Color(URLs().colorScheme)]
              : [Colors.grey[300]!, Colors.grey[400]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: isZakatDue 
                ? Color(URLs().colorScheme).withOpacity(0.3)
                : Colors.grey.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: Colors.white.withOpacity(0.2),
                child: Icon(
                  isZakatDue ? Icons.check_circle_outline_rounded : Icons.info_outline_rounded,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isZakatDue ? 'Zakat is Due' : 'No Zakat Due',
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      isZakatDue 
                          ? 'Your wealth exceeds the Nisab threshold'
                          : 'Your wealth is below the Nisab threshold',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (isZakatDue) ...[
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  Text(
                    'Your Zakat Amount',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.zakatModel.currencyFormatter.format(zakatAmount),
                    style: GoogleFonts.poppins(
                      fontSize: 28,
                      fontWeight: FontWeight.w700,
                      color: Color(URLs().colorScheme),
                    ),
                  ).animate().fadeIn(duration: 600.ms).scale(begin: const Offset(0.9, 0.9)),
                  const SizedBox(height: 4),
                  Text(
                    '2.5% of your net wealth',
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      color: Colors.grey[500],
                    ),
                  ),
                  const SizedBox(height: 16),
                  LinearPercentIndicator(
                    percent: 0.025, // 2.5%
                    lineHeight: 8,
                    animation: true,
                    animationDuration: 1500,
                    backgroundColor: Colors.grey[200],
                    progressColor: Color(URLs().colorScheme),
                    barRadius: const Radius.circular(4),
                    padding: EdgeInsets.zero,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Net Wealth: ${widget.zakatModel.currencyFormatter.format(netWorth)}',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        '2.5%',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Color(URLs().colorScheme),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ] else ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                'You are not obligated to pay Zakat at this time as your wealth does not meet the minimum threshold (Nisab).',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ],
      ),
    ).animate().fadeIn(duration: 400.ms).slideY(begin: 0.1, end: 0);
  }

  Widget _buildWealthBreakdownCard({
    required double totalWealth,
    required double totalLiabilities,
    required double netWorth,
    required double assetsPercentage,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildAmountDisplay(
                label: 'Total Assets',
                amount: totalWealth,
                color: Colors.green[700]!,
                icon: FontAwesomeIcons.circleArrowUp,
              ),
              _buildAmountDisplay(
                label: 'Total Liabilities',
                amount: totalLiabilities,
                color: Colors.red[700]!,
                icon: FontAwesomeIcons.circleArrowDown,
              ),
            ],
          ),
          const SizedBox(height: 24),
          LinearPercentIndicator(
            percent: assetsPercentage,
            lineHeight: 10,
            animation: true,
            animationDuration: 1500,
            backgroundColor: Colors.red[100],
            progressColor: Colors.green[700],
            barRadius: const Radius.circular(5),
            padding: EdgeInsets.zero,
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Assets',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.green[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                'Liabilities',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.red[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Color(URLs().colorScheme).withOpacity(0.1),
                  child: Icon(
                    FontAwesomeIcons.scaleBalanced,
                    size: 16,
                    color: Color(URLs().colorScheme),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Net Zakatable Wealth',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[700],
                        ),
                      ),
                      Text(
                        widget.zakatModel.currencyFormatter.format(netWorth),
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Color(URLs().colorScheme),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 400.ms, delay: 100.ms).slideY(begin: 0.1, end: 0);
  }

  Widget _buildNisabComparisonCard({
    required double netWorth,
    required double applicableNisab,
    required double wealthToNisabPercentage,
    required bool isZakatDue,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularPercentIndicator(
                radius: 60,
                lineWidth: 10,
                percent: wealthToNisabPercentage,
                center: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      isZakatDue ? Icons.check : Icons.close,
                      color: isZakatDue ? Colors.green[700] : Colors.red[700],
                      size: 24,
                    ),
                    Text(
                      isZakatDue ? 'Eligible' : 'Not Eligible',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: isZakatDue ? Colors.green[700] : Colors.red[700],
                      ),
                    ),
                  ],
                ),
                backgroundColor: Colors.grey[200]!,
                progressColor: isZakatDue ? Colors.green[700]! : Colors.red[700]!,
                animation: true,
                animationDuration: 1500,
                circularStrokeCap: CircularStrokeCap.round,
              ).animate().fadeIn(duration: 600.ms, delay: 200.ms).scale(begin: const Offset(0.8, 0.8)),
            ],
          ),
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              children: [
                _buildComparisonRow(
                  label: 'Your Net Wealth',
                  value: widget.zakatModel.currencyFormatter.format(netWorth),
                  color: Color(URLs().colorScheme),
                ),
                const SizedBox(height: 12),
                _buildComparisonRow(
                  label: 'Applicable Nisab',
                  value: widget.zakatModel.currencyFormatter.format(applicableNisab),
                  color: Colors.grey[700]!,
                ),
                const SizedBox(height: 12),
                _buildComparisonRow(
                  label: 'Status',
                  value: isZakatDue ? 'Above Nisab' : 'Below Nisab',
                  color: isZakatDue ? Colors.green[700]! : Colors.red[700]!,
                  isBold: true,
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Zakat is obligatory when your wealth equals or exceeds the Nisab threshold for one lunar year.',
            style: GoogleFonts.poppins(
              fontSize: 13,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ).animate().fadeIn(duration: 400.ms, delay: 200.ms).slideY(begin: 0.1, end: 0);
  }

  Widget _buildSadaqahSuggestionCard() {
    return StatefulBuilder(
      builder: (context, setState) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    FontAwesomeIcons.handHoldingHeart,
                    size: 18,
                    color: Colors.pink[400],
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Sadaqah Suggestion',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'In addition to Zakat, consider giving Sadaqah (voluntary charity). The Prophet Muhammad (peace be upon him) encouraged regular charity, even in small amounts.',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Text(
                    '0.5%',
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                  ),
                  Expanded(
                    child: Slider(
                      value: widget.zakatModel.sadaqahPercentage,
                      min: 0.5,
                      max: 10.0,
                      divisions: 19,
                      activeColor: Colors.pink[400],
                      inactiveColor: Colors.grey[300],
                      label: '${widget.zakatModel.sadaqahPercentage.toStringAsFixed(1)}%',
                      onChanged: (value) {
                        setState(() {
                          widget.zakatModel.sadaqahPercentage = value;
                        });
                      },
                    ),
                  ),
                  Text(
                    '10.0%',
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                  decoration: BoxDecoration(
                    color: Colors.pink[50],
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '${widget.zakatModel.sadaqahPercentage.toStringAsFixed(1)}%',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.pink[400],
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '= ${widget.zakatModel.currencyFormatter.format(widget.zakatModel.calculateSuggestedSadaqah())}',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.pink[700],
                        ),
                      ),
                    ],
                  ),
                ).animate().fadeIn(duration: 400.ms, delay: 300.ms).scale(begin: const Offset(0.9, 0.9)),
              ),
            ],
          ),
        ).animate().fadeIn(duration: 400.ms, delay: 300.ms).slideY(begin: 0.1, end: 0);
      },
    );
  }

  Widget _buildAmountDisplay({
    required String label,
    required double amount,
    required Color color,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 14, color: color),
            const SizedBox(width: 6),
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          widget.zakatModel.currencyFormatter.format(amount),
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildComparisonRow({
    required String label,
    required String value,
    required Color color,
    bool isBold = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
          ),
        ),
        Text(
          value,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: isBold ? FontWeight.w600 : FontWeight.w500,
            color: color,
          ),
        ),
      ],
    );
  }
} 