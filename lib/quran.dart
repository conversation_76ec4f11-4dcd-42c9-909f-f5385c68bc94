import 'package:audioplayers/audioplayers.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:new_islamic_app_uk/URLs.dart';
import 'package:new_islamic_app_uk/read_quran.dart';
import 'package:new_islamic_app_uk/widget/playerWidget.dart';

import 'package:quran/quran.dart' as quran;

class QuranPage extends StatefulWidget {
  var id;
  var name;
  var image;

  QuranPage({Key? key, this.id, this.name, this.image}) : super(key: key);

  @override
  State<QuranPage> createState() => _QuranPageState();
}



class _QuranPageState extends State<QuranPage> {

  var connectivityResult;
    var player = AudioPlayer();

  @override
  Widget build(BuildContext context) {
    var _crossAxisSpacing = 8;
    var _screenWidth = MediaQuery.of(context).size.width;
    var _crossAxisCount = 1;
    var _width = (_screenWidth - ((_crossAxisCount - 1) * _crossAxisSpacing)) /
        _crossAxisCount;
    var cellHeight = 190;
    var _aspectRatio = _width / cellHeight;

    var _crossAxisSpacingList = 8;
    var _screenWidthList = MediaQuery.of(context).size.width;
    var _crossAxisCountList = 1;
    var _widthList = (_screenWidthList -
            ((_crossAxisCountList - 1) * _crossAxisSpacingList)) /
        _crossAxisCountList;
    var cellHeightList = 100;
    var _aspectRatioList = _widthList / cellHeightList;

    // TODO: implement build
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          image: DecorationImage(image: AssetImage("assets/bg.jpeg"), fit: BoxFit.cover)
        ),
        child: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(top: 70, bottom: 0),
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(),
              child: Padding(
                padding: const EdgeInsets.only(bottom: 0.0),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Image.asset(""),
                      Text(
                        'القرأن الكريم',
                        style: TextStyle(
                          fontSize: 50,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 30.0),
                        child: Text(
                          "The Holy Quran is the matchless Kalaam of that Lord عَزَّوَجَلَّ Who is the only Deity, the only Creator and the real Lord of the entire universe.",
                          style: GoogleFonts.notoNaskhArabic(
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // ),
        
            // connectivityResult != ConnectivityResult.none
            //     ? Padding(
            //         padding: const EdgeInsets.symmetric(horizontal: 10),
            //         child: ClipRRect(
            //           borderRadius: BorderRadius.circular(8),
            //           child: Container(
            //             padding: EdgeInsets.all(10),
            //             color: Colors.red.withOpacity(0.3),
            //             child: Center(
            //                 child: Text("Internet is required for audio file")),
            //           ),
            //         ),
            //       )
            //     : Container(),
        
            Padding(
              padding: EdgeInsets.all(6.0),
              child: ListView.builder(
                  physics: PageScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: quran.totalSurahCount,
                  itemBuilder: (context, int index) {
                    return GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (BuildContext context) => new readQuranPage(
                              id: index + 1,
                              surahName: quran.getSurahName(index + 1),
                              player: player,
                            ),
                          ),
                        ).then((value) {
                          player.stop();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(top: 0),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Card(
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10.0),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              child: ListTile(
                                leading: ClipRRect(
                                    borderRadius: BorderRadius.circular(40),
                                    child: Container(
                                      padding: EdgeInsets.all(10),
                                      height: 55,
                                      width: 55,
                                      child: Image.asset("assets/quran.png"),
                                    )),
                                title: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.all(0),
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Text(
                                          quran.getSurahNameArabic(index + 1),
                                          style: GoogleFonts.openSans(
                                              fontSize: 18,
                                              fontWeight: FontWeight.w600),
                                        ),
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(top: 4.0),
                                      child: Text(
                                        "${quran.getSurahNameEnglish(index + 1)}",
                                        style: TextStyle(
                                            color: Colors.grey, fontSize: 14),
                                      ),
                                    ),
                                  ],
                                ),
        
                                trailing: Wrap(
                                  spacing: 8,
                                  children: [
                                    InkWell(
                                      onTap: () {
                                        showDialog(
                                            context: context,
                                            builder: (BuildContext context) =>
                                                Padding(
                                                  padding: const EdgeInsets.only(top: 90.0, bottom: 90),
                                                  child: AlertDialog(
                                                    
                                                    
                                                    title: Text("Surah Info"),
                                                    content: Column(
                                                      children: [
                                                        Card(
                                                          elevation: 0,
                                                          color: Colors.green.withOpacity(0.1),
                                                          child: ListTile(
                                                            title:
                                                                Text("Surah Name in Arabic", style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green),),
                                                            subtitle:
                                                                Text("${quran.getSurahNameArabic(index + 1)}"),
                                                            // trailing: Text(
                                                            //     "${quran.getSurahNameArabic(index + 1)}"),
                                                          ),
                                                        ),
                                                        // Card(
                                                        //   child: ListTile(
                                                        //     title:
                                                        //         Text("Surah Name"),
                                                        //     trailing: Text(
                                                        //         "${quran.getSurahName(index + 1)}"),
                                                        //   ),
                                                        // ),
                                                        Card(
                                                          elevation: 0,
                                                          color: Colors.green.withOpacity(0.1),
                                                          child: ListTile(
                                                            title:
                                                                Text("Surah Name in English", style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green),),
                                                            subtitle:
                                                                Text("${quran.getSurahNameEnglish(index + 1)}"),
                                                            // trailing: Text(
                                                            //     "${quran.getSurahNameEnglish(index + 1)}"),
                                                          ),
                                                        ),
                                                        Card(
                                                          elevation: 0,
                                                          color: Colors.green.withOpacity(0.1),
                                                          child: ListTile(
                                                            title: Text(
                                                                "Total Verses", style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green),),
                                                            subtitle: Text(
                                                                "${quran.getVerseCount(index + 1)}"),
                                                          ),
                                                        ),
                                                        Card(
                                                          elevation: 0,
                                                          color: Colors.green.withOpacity(0.1),
                                                          child: ListTile(
                                                            title: Text(
                                                                "Surah Number", style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green),),
                                                            subtitle: Text(
                                                                "${index + 1}"),
                                                          ),
                                                        ),
                                                        Card(
                                                          elevation: 0,
                                                          color: Colors.green.withOpacity(0.1),
                                                          child: ListTile(
                                                            title: Text(
                                                                "Place of Revelation", style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green),),
                                                            subtitle: Text(
                                                                "${quran.getPlaceOfRevelation(index + 1)}"),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    actions: <Widget>[
                                                      TextButton(
                                                        onPressed: () {
                                                          Navigator.of(context)
                                                              .pop();
                                                        },
                                                        child: Container(
                                                          padding:
                                                              const EdgeInsets.all(
                                                                  0),
                                                          child: const Text("CLOSE"),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ));
                                      },
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(10),
                                        child: Container(
                                          color: Colors.grey[100],
                                          padding: EdgeInsets.symmetric(
                                              vertical: 8, horizontal: 8),
                                          child: Column(
                                            children: [
                                              Icon(
                                                Icons.info_outline, size: 30,
                                                color: Colors.green,
                                              ),
                                              // Text("Info")
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
        //                             InkWell(
        //                               onTap: () async {
        //                                 if (connectivityResult !=
        //                                     ConnectivityResult.none) {
        //                                   Navigator.push(
        //                                     context,
        //                                     MaterialPageRoute(
        //                                       builder: (BuildContext context) =>
        //                                           PlayerWidget(
        //                                         surahNameInEnglish:
        //                                             quran.getSurahNameEnglish(
        //                                                 index + 1),
        //                                         player: player,
        //                                         surahName:
        //                                             quran.getSurahNameArabic(
        //                                                 index + 1),
        //                                         url: quran.getAudioURLBySurah(
        //                                             index + 1),
        //                                       ),
        //                                     ),
        //                                   ).then((value) {
        //                                     player.stop();
        //                                   });
        //                                 } else {
        //                                   Fluttertoast.showToast(
        //                                       msg:
        //                                           "Please Connect to Internet and Try Again...",
        //                                       toastLength: Toast.LENGTH_SHORT,
        //                                       gravity: ToastGravity.BOTTOM,
        //                                       timeInSecForIosWeb: 1,
        //                                       backgroundColor: Colors.black87,
        //                                       textColor: Colors.white,
        //                                       fontSize: 15.0);
        //                                 }
        // // }
        //                               },
        //                               child: ClipRRect(
        //                                 borderRadius: BorderRadius.circular(5),
        //                                 child: Container(
        //                                   color: Colors.greenAccent,
        //                                   padding: EdgeInsets.symmetric(
        //                                       vertical: 4, horizontal: 8),
        //                                   child: Column(
        //                                     children: [
        //                                       Icon(
        //                                         Icons.volume_down,
        //                                         color: Colors.green,
        //                                       ),
        //                                       Text("Listen")
        //                                     ],
        //                                   ),
        //                                 ),
        //                               ),
        //                             ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
            ),
          ],
        // ),
            // );
        
              // quranPak.length >= 1
              //     ? Padding(
              //         padding: EdgeInsets.all(6.0),
              //         child: ListView.builder(
              //             physics: PageScrollPhysics(),
              //             shrinkWrap: true,
              //             itemCount: quranPak.length,
              //             itemBuilder: (context, int index) {
              //               return GestureDetector(
              //                 onTap: () {
              //                   Navigator.push(
              //                     context,
              //                     MaterialPageRoute(
              //                       builder: (BuildContext context) =>
              //                           new readQuranPage(
              //                         content: quranPak[index].content,
              //                         title: quranPak[index].title,
              //                       ),
              //                     ),
              //                   ).then((value) {});
              //                 },
              //                 child: Padding(
              //                   padding: const EdgeInsets.only(top: 0),
              //                   child: ClipRRect(
              //                     borderRadius: BorderRadius.circular(8),
              //                     child: Card(
              //                       elevation: 0,
              //                       shape: RoundedRectangleBorder(
              //                         borderRadius: BorderRadius.circular(10.0),
              //                       ),
              //                       child: ListTile(
              //                         leading: ClipRRect(
              //                             borderRadius: BorderRadius.circular(40),
              //                             child: Container(
              //                               padding: EdgeInsets.all(10),
              //                               height: 55,
              //                               width: 55,
              //                               color: Color(URLs().colorScheme)
              //                                   .withOpacity(0.2),
              //                               child: Image.asset("assets/logo.png"),
              //                             )),
              //                         title: Padding(
              //                           padding: EdgeInsets.only(top: 20),
              //                           child: Column(
              //                             children: [
              //                               Padding(
              //                                 padding: EdgeInsets.all(0),
              //                                 child: Align(
              //                                   alignment: Alignment.centerLeft,
              //                                   child: Padding(
              //                                     padding: const EdgeInsets.only(
              //                                         top: 10.0),
              //                                     child: Text(
              //                                       quranPak[index].title,
              //                                       style: GoogleFonts.openSans(
              //                                           fontSize: 18,
              //                                           fontWeight:
              //                                               FontWeight.bold),
              //                                     ),
              //                                   ),
              //                                 ),
              //                               ),
              //                             ],
              //                           ),
              //                         ),
              //                         trailing: Padding(
              //                           padding: const EdgeInsets.only(top: 23.0),
              //                           child: Icon(Icons.arrow_forward_ios,
              //                               size: 18,
              //                               color: Color(0xFF888888)
              //                                   .withOpacity(0.3)),
              //                         ),
              //                       ),
              //                     ),
              //                   ),
              //                 ),
              //               );
              //             }),
              //       )
              //     : GridView.count(
              //         physics: NeverScrollableScrollPhysics(),
              //         shrinkWrap: true,
              //         crossAxisSpacing: 10.0,
              //         crossAxisCount: 1,
              //         childAspectRatio: _aspectRatioList,
              //         children: List.generate(8, (index) {
              //           return ClipRRect(
              //             borderRadius: BorderRadius.circular(8),
              //             child: Padding(
              //               padding: const EdgeInsets.symmetric(
              //                   vertical: 6, horizontal: 8),
              //               child: Container(
              //                 child: Shimmer.fromColors(
              //                     baseColor: Colors.grey.withOpacity(0.3),
              //                     highlightColor: Colors.white,
              //                     child: Container(
              //                       height: 100,
              //                       width: double.infinity,
              //                       color: Colors.grey.withOpacity(0.3),
              //                     )),
              //               ),
              //             ),
              //           );
              //         }),
              //       ),
            // ],
          ),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    // GetQuran();
  }

  // List<AllduaModel> quranPak = [];

  // Future<AllduaModel> GetQuran() async {
  //   try {
  //     var dio = Dio();
  //     Response response = await dio.get(URLs().WEBURL +
  //         "/wp-json/wp/v2/posts?categories=${URLs().quran_category_id}&order=asc&per_page=100");
  //     if (response.statusCode == 200) {
  //       var parsedData =
  //           response.data.map((res) => AllduaModel.fromJson(res)).toList();
  //       parsedData.forEach(
  //         (f) async {
  //           quranPak.add(f);
  //         },
  //       );
  //       setState(() {
  //         return quranPak;
  //       });
  //     }
  //   } catch (e) {
  //     print(e);
  //   }
  // }
}
