class Dua {
  final String id;
  final String arabic;
  final String transliteration;
  final String translation;
  final String reference;
  final bool isFavorite;

  Dua({
    required this.id,
    required this.arabic,
    required this.transliteration,
    required this.translation,
    required this.reference,
    this.isFavorite = false,
  });

  factory Dua.fromJson(Map<String, dynamic> json) {
    return Dua(
      id: json['id'],
      arabic: json['arabic'],
      transliteration: json['transliteration'],
      translation: json['translation'],
      reference: json['reference'],
      isFavorite: json['isFavorite'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'arabic': arabic,
      'transliteration': transliteration,
      'translation': translation,
      'reference': reference,
      'isFavorite': isFavorite,
    };
  }

  Dua copyWith({
    String? id,
    String? arabic,
    String? transliteration,
    String? translation,
    String? reference,
    bool? isFavorite,
  }) {
    return Dua(
      id: id ?? this.id,
      arabic: arabic ?? this.arabic,
      transliteration: transliteration ?? this.transliteration,
      translation: translation ?? this.translation,
      reference: reference ?? this.reference,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }
}

class duaCategoryModel {
  final String? name;
  final String? img;
  final String? slug;
  final int? count;
  final String? id;
  
  duaCategoryModel({
    this.name, 
    this.img, 
    this.id, 
    this.slug, 
    this.count
  });
  
  factory duaCategoryModel.fromJson(Map<String, dynamic> json) {
    return duaCategoryModel(
      name: json['name'],
      img: json['description'],
      id: json['id'],
      slug: json['slug'],
      count: json['count'],
    );
  }
}

class AllduaModel {
  final String? title;
  final String? img;
  final String? date;
  final String? id;
  final String? content;
  final String? categoryName;
  final String? categoryID;

  AllduaModel({
    this.title,
    this.img,
    this.id,
    this.date,
    this.content,
    this.categoryName,
    this.categoryID
  });
  
  factory AllduaModel.fromJson(Map<String, dynamic> json) {
    return AllduaModel(
      title: json['title']['rendered'],
      date: json['date'],
      img: json['jetpack_featured_media_url'],
      id: json['id'],
      content: json['content']['rendered']
    );
  }
}

class Zikr {
  final String id;
  final String arabic;
  final String transliteration;
  final String translation;
  final String reference;
  final int repeat;
  int currentCount;

  Zikr({
    required this.id,
    required this.arabic,
    required this.transliteration,
    required this.translation,
    required this.reference,
    required this.repeat,
    this.currentCount = 0,
  });

  factory Zikr.fromJson(Map<String, dynamic> json) {
    return Zikr(
      id: json['id'],
      arabic: json['arabic'],
      transliteration: json['transliteration'],
      translation: json['translation'],
      reference: json['reference'],
      repeat: json['repeat'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'arabic': arabic,
      'transliteration': transliteration,
      'translation': translation,
      'reference': reference,
      'repeat': repeat,
      'currentCount': currentCount,
    };
  }

  Zikr copyWith({
    String? id,
    String? arabic,
    String? transliteration,
    String? translation,
    String? reference,
    int? repeat,
    int? currentCount,
  }) {
    return Zikr(
      id: id ?? this.id,
      arabic: arabic ?? this.arabic,
      transliteration: transliteration ?? this.transliteration,
      translation: translation ?? this.translation,
      reference: reference ?? this.reference,
      repeat: repeat ?? this.repeat,
      currentCount: currentCount ?? this.currentCount,
    );
  }
}
