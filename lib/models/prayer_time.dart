// TODO: This class received Posts data from the network
class PrayerTimeModel {
  var date;
  var fajr;
  var fajrJamah;
  var sunrise;
  var zuhr;
  var zuhrJamah;
  var asr;
  var asrJamah;
  var maghrib;
  var maghribJamah;
  var isha;
  var ishaJamah;
  var hijri_date;

  var dateTomorrow;
  var fajrTomorrow;
  var fajrJamahTomorrow;
  var sunriseTomorrow;
  var zuhrTomorrow;
  var zuhrJamahTomorrow;
  var asrTomorrow;
  var asrJamahTomorrow;
  var maghribTomorrow;
  var maghribJamahTomorrow;
  var ishaTomorrow;
  var ishaJamahTomorrow;
  var hijri_dateTomorrow;

  PrayerTimeModel(
      {this.date,
      this.fajr,
      this.fajrJamah,
      this.sunrise,
      this.zuhr,
      this.zuhrJamah,
      this.asr,
      this.asrJamah,
      this.maghrib,
      this.maghribJamah,
      this.isha,
      this.ishaJamah,
      this.hijri_date,
      this.dateTomorrow,
      this.fajrTomorrow,
      this.fajrJamahTomorrow,
      this.sunriseTomorrow,
      this.zuhrTomorrow,
      this.zuhrJamahTomorrow,
      this.asrTomorrow,
      this.asrJamahTomorrow,
      this.maghribTomorrow,
      this.maghribJamahTomorrow,
      this.ishaTomorrow,
      this.ishaJamahTomorrow,
      this.hijri_dateTomorrow,
      
      
      });
  factory PrayerTimeModel.fromJson(Map<String, dynamic> json) {
    return PrayerTimeModel(
        date: json['d_date'],
        fajr: json['fajr_begins'],
        fajrJamah: json['fajr_jamah'],
        sunrise: json["sunrise"],
        zuhr: json['zuhr_begins'],
        zuhrJamah: json['zuhr_jamah'],
        asr: json['asr_mithl_1'],
        asrJamah: json['asr_jamah'],
        maghrib: json['maghrib_begins'],
        maghribJamah: json['maghrib_jamah'],
        isha: json['isha_begins'],
        ishaJamah: json['isha_jamah'],
        hijri_date: json['hijri_date_convert'],

        dateTomorrow: json['tomorrow']['d_date'],
        fajrTomorrow: json['tomorrow']['fajr_begins'],
        fajrJamahTomorrow: json['tomorrow']['fajr_jamah'],
        sunriseTomorrow: json['tomorrow']["sunrise"],
        zuhrTomorrow: json['tomorrow']['zuhr_begins'],
        zuhrJamahTomorrow: json['tomorrow']['zuhr_jamah'],
        asrTomorrow: json['tomorrow']['asr_mithl_1'],
        asrJamahTomorrow: json['tomorrow']['asr_jamah'],
        maghribTomorrow: json['tomorrow']['maghrib_begins'],
        maghribJamahTomorrow: json['tomorrow']['maghrib_jamah'],
        ishaTomorrow: json['tomorrow']['isha_begins'],
        ishaJamahTomorrow: json['tomorrow']['isha_jamah'],
        hijri_dateTomorrow: json['tomorrow']['hijri_date_convert'],



        );
  }

  // Map<String, dynamic> toMap() {
  //   return {
  //     'blogID': id,
  //     'title': title,
  //     'content': content,
  //     "img": img,
  //     "categoryName": categoryName,
  //     "CatID": categoryID,
  //   };
  // }

  // Map<String, dynamic> toProgramsMap() {
  //   return {'blogID': id, 'title': title, 'content': content};
  // }
}


// class PrayerTimeModel {
//   var dDate;
//   var fajrBegins;
//   var fajrJamah;
//   var sunrise;
//   var zuhrBegins;
//   var zuhrJamah;
//   var asrMithl1;
//   var asrMithl2;
//   var asrJamah;
//   var maghribBegins;
//   var maghribJamah;
//   var ishaBegins;
//   var ishaJamah;
//   var isRamadan;
//   var hijriDate;

//   PrayerTimeModel(
//       {this.dDate,
//       this.fajrBegins,
//       this.fajrJamah,
//       this.sunrise,
//       this.zuhrBegins,
//       this.zuhrJamah,
//       this.asrMithl1,
//       this.asrMithl2,
//       this.asrJamah,
//       this.maghribBegins,
//       this.maghribJamah,
//       this.ishaBegins,
//       this.ishaJamah,
//       this.isRamadan,
//       this.hijriDate});

//   PrayerTimeModel.fromJson(Map<String, dynamic> json) {
//     dDate = json['d_date'];
//     fajrBegins = json['fajr_begins'];
//     fajrJamah = json['fajr_jamah'];
//     sunrise = json['sunrise'];
//     zuhrBegins = json['zuhr_begins'];
//     zuhrJamah = json['zuhr_jamah'];
//     asrMithl1 = json['asr_mithl_1'];
//     asrMithl2 = json['asr_mithl_2'];
//     asrJamah = json['asr_jamah'];
//     maghribBegins = json['maghrib_begins'];
//     maghribJamah = json['maghrib_jamah'];
//     ishaBegins = json['isha_begins'];
//     ishaJamah = json['isha_jamah'];
//     isRamadan = json['is_ramadan'];
//     hijriDate = json['hijri_date'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['d_date'] = this.dDate;
//     data['fajr_begins'] = this.fajrBegins;
//     data['fajr_jamah'] = this.fajrJamah;
//     data['sunrise'] = this.sunrise;
//     data['zuhr_begins'] = this.zuhrBegins;
//     data['zuhr_jamah'] = this.zuhrJamah;
//     data['asr_mithl_1'] = this.asrMithl1;
//     data['asr_mithl_2'] = this.asrMithl2;
//     data['asr_jamah'] = this.asrJamah;
//     data['maghrib_begins'] = this.maghribBegins;
//     data['maghrib_jamah'] = this.maghribJamah;
//     data['isha_begins'] = this.ishaBegins;
//     data['isha_jamah'] = this.ishaJamah;
//     data['is_ramadan'] = this.isRamadan;
//     data['hijri_date'] = this.hijriDate;
//     return data;
//   }
// }

