class EventModel {
  final int id;
  final String title;
  final String content;
  final String excerpt;
  final String date;
  
  // Additional fields needed by events.dart
  final String img;
  final String start_time;
  final String end_time;
  final String event_date;
  final String event_day;
  final String fee;

  EventModel({
    required this.id,
    required this.title,
    required this.content,
    required this.excerpt,
    required this.date,
    this.img = '',
    this.start_time = '',
    this.end_time = '',
    this.event_date = '',
    this.event_day = '',
    this.fee = '0',
  });

  factory EventModel.fromJson(Map<String, dynamic> json) {
    // Extract image URL from content if available
    String imageUrl = '';
    String contentRendered = json['content']?['rendered'] ?? '';
    
    if (contentRendered.contains('src="')) {
      try {
        final srcIndex = contentRendered.indexOf('src="');
        if (srcIndex != -1) {
          final startIndex = srcIndex + 5; // Length of 'src="'
          final endIndex = contentRendered.indexOf('"', startIndex);
          if (endIndex != -1) {
            imageUrl = contentRendered.substring(startIndex, endIndex);
          }
        }
      } catch (e) {
        print('Error extracting image URL: $e');
      }
    }
    
    // Extract event details from ACF fields if available
    String startTime = '';
    String endTime = '';
    String eventDate = '';
    String eventDay = '';
    String fee = '0';
    
    if (json['acf'] != null) {
      startTime = json['acf']['start_time'] ?? '';
      endTime = json['acf']['end_time'] ?? '';
      eventDate = json['acf']['event_date'] ?? '';
      eventDay = json['acf']['event_day'] ?? '';
      fee = json['acf']['fee']?.toString() ?? '0';
    }
    
    return EventModel(
      id: json['id'] ?? 0,
      title: json['title']?['rendered'] ?? '',
      content: contentRendered,
      excerpt: json['excerpt']?['rendered'] ?? '',
      date: json['date'] ?? '',
      img: imageUrl,
      start_time: startTime,
      end_time: endTime,
      event_date: eventDate,
      event_day: eventDay,
      fee: fee,
    );
  }
}
