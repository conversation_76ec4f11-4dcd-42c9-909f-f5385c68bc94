import 'package:intl/intl.dart';

class ZakatModel {
  // Precious metals
  double goldValue = 0;
  double goldPrice = 2400; // Default price per ounce in GBP
  double silverValue = 0;
  double silverPrice = 30; // Default price per ounce in GBP
  
  // Cash values
  double cashInHand = 0;
  double bankDeposits = 0;
  double cashLoaned = 0;
  double otherCash = 0;
  
  // Investment values
  double stocks = 0;
  double shares = 0;
  double bonds = 0;
  double realEstate = 0;
  double businessAssets = 0;
  
  // Liabilities
  double debts = 0;
  double expenses = 0;
  
  // Sadaqah suggestion percentage
  double sadaqahPercentage = 2.5;
  
  // Currency formatter
  final currencyFormatter = NumberFormat.currency(locale: 'en_GB', symbol: '£');
  
  // Nisab thresholds
  double goldNisab = 0; // Value of 87.48g of gold
  double silverNisab = 0; // Value of 612.36g of silver
  
  // Constants
  static const double GOLD_NISAB_GRAMS = 87.48; // 87.48 grams of gold
  static const double SILVER_NISAB_GRAMS = 612.36; // 612.36 grams of silver
  static const double GOLD_GRAMS_PER_OUNCE = 28.35;
  static const double SILVER_GRAMS_PER_OUNCE = 28.35;
  static const double ZAKAT_RATE = 0.025; // 2.5%
  
  ZakatModel() {
    calculateNisabThresholds();
  }
  
  void calculateNisabThresholds() {
    // Gold Nisab: 87.48 grams (approx 3.086 ounces)
    goldNisab = (GOLD_NISAB_GRAMS / GOLD_GRAMS_PER_OUNCE) * goldPrice;
    
    // Silver Nisab: 612.36 grams (approx 21.58 ounces)
    silverNisab = (SILVER_NISAB_GRAMS / SILVER_GRAMS_PER_OUNCE) * silverPrice;
  }
  
  double calculateTotalWealth() {
    double totalGold = goldValue;
    double totalSilver = silverValue;
    double totalCash = cashInHand + bankDeposits + cashLoaned + otherCash;
    double totalInvestments = stocks + shares + bonds + realEstate + businessAssets;
    
    return totalGold + totalSilver + totalCash + totalInvestments;
  }
  
  double calculateTotalLiabilities() {
    return debts + expenses;
  }
  
  double calculateNetWorth() {
    return calculateTotalWealth() - calculateTotalLiabilities();
  }
  
  double calculateZakat() {
    double netWorth = calculateNetWorth();
    
    // Use the lower of the two Nisab thresholds (typically silver)
    double applicableNisab = goldNisab < silverNisab ? goldNisab : silverNisab;
    
    if (netWorth >= applicableNisab) {
      return netWorth * ZAKAT_RATE; // 2.5% of net worth
    } else {
      return 0; // Below Nisab threshold, no Zakat due
    }
  }
  
  double calculateSuggestedSadaqah() {
    return calculateNetWorth() * (sadaqahPercentage / 100);
  }
  
  bool isZakatDue() {
    double netWorth = calculateNetWorth();
    double applicableNisab = goldNisab < silverNisab ? goldNisab : silverNisab;
    return netWorth >= applicableNisab;
  }
  
  // Convert grams to ounces
  double gramsToOunces(double grams) {
    return grams / GOLD_GRAMS_PER_OUNCE;
  }
  
  // Calculate gold value from weight in grams
  double calculateGoldValueFromGrams(double grams) {
    return gramsToOunces(grams) * goldPrice;
  }
  
  // Calculate silver value from weight in grams
  double calculateSilverValueFromGrams(double grams) {
    return gramsToOunces(grams) * silverPrice;
  }
} 