class JumuahModel {
  final int id;
  final String title;
  final String content;
  final String excerpt;
  final String date;

  JumuahModel({
    required this.id,
    required this.title,
    required this.content,
    required this.excerpt,
    required this.date,
  });

  factory JumuahModel.fromJson(Map<String, dynamic> json) {
    return JumuahModel(
      id: json['id'] ?? 0,
      title: json['title']?['rendered'] ?? '',
      content: json['content']?['rendered'] ?? '',
      excerpt: json['excerpt']?['rendered'] ?? '',
      date: json['date'] ?? '',
    );
  }
}

// class JumuahModel {
//   var title;
//   var jumuah_khutbah;
//   var jumuah_entry_time;

//   JumuahModel({
//     this.title,
//     this.jumuah_khutbah,
//     this.jumuah_entry_time,
//   });
//   factory JumuahModel.fromJson(Map<String, dynamic> json) {
//     return JumuahModel(
//       title: json['jumuah'][0],
//       jumuah_entry_time: json['jumuah'][1],
//       jumuah_khutbah: json['jumuah'][2],
//     );
//   }
// }
