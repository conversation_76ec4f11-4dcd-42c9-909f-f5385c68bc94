name: new_islamic_app_uk
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.2.6 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  android_alarm_manager_plus: ^4.0.7
  # assorted_layout_widgets: ^11.0.0
  carousel_slider: ^5.0.0
  connectivity_plus: ^6.1.0
  cupertino_icons: ^1.0.8
  dio: ^5.7.0
  audioplayers: ^6.1.0
  flutter_countdown_timer: ^4.1.0
  flutter_qiblah: ^3.0.4
  # flutter_ringtone_player: ^4.0.0+2
  flutter_ringtone_player: ^4.0.0+3
  flutter_svg: ^2.0.10+1
  flutter_widget_from_html_core: ^0.15.2
  # fluttertoast: ^8.2.8
  google_fonts: ^6.2.1
  home_widget: ^0.7.0
  html_unescape: ^2.0.0
  intl: ^0.20.2
  quran: ^1.3.3
  hadith: ^1.0.0
  share_plus: ^10.1.4
  clipboard: ^0.1.3
  shimmer: ^3.0.0
  # slide_countdown: ^1.5.2
  slide_countdown: ^2.0.2
  # splash_screen_view: ^3.0.0
  splash_view: ^2.0.1
  workmanager: ^0.5.2
  font_awesome_flutter: ^10.7.0
  animated_notch_bottom_bar: ^1.0.2
  smooth_compass_plus: ^0.0.1+4
  url_launcher: ^6.3.1
  url_launcher_ios: ^6.3.1
  flutter_animate: ^4.2.0
  lottie: ^2.6.0
  percent_indicator: ^4.2.3
  uuid: ^4.3.3

  # Prayer Reminder Dependencies - Updated version
  awesome_notifications: ^0.10.1
  flutter_native_timezone: ^2.0.0
  shared_preferences: ^2.5.2
  path_provider: ^2.1.2
  cached_network_image: ^3.4.1
  hijri_calendar: ^1.0.7+7
  provider: ^6.1.2
  get: ^4.7.2
  flutter_staggered_animations: ^1.1.1
  flutter_slidable: ^3.1.2
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.14.1

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0


flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/logo2.png"
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/data/
    - assets/images/
    - assets/logo2.png
    - assets/event-cover.png
    - assets/bg.jpeg
    - assets/pattern.jpg

  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
