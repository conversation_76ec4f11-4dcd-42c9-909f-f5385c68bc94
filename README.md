# Masjid Sunnah App

An Islamic app for the Masjid Sunnah community in Nelson, UK.

## Prayer Reminder Notifications Setup

The app includes a comprehensive prayer reminder notification system using Awesome Notifications 0.10.1. Follow these steps to set it up properly:

### 1. Dependencies

Make sure the following dependencies are added to your `pubspec.yaml`:

```yaml
dependencies:
  awesome_notifications: ^0.10.1
  flutter_native_timezone: ^2.0.0
  shared_preferences: ^2.5.2
  path_provider: ^2.1.2
```

Run `flutter pub get` to install these dependencies.

### 2. Android Setup

#### Permissions

Ensure the following permissions are added to your `AndroidManifest.xml`:

```xml
<uses-permission android:name="android.permission.INTERNET"/>
<uses-permission android:name="android.permission.VIBRATE"/>
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
<uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM"/>
<uses-permission android:name="android.permission.USE_EXACT_ALARM"/>
<uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
<uses-permission android:name="android.permission.WAKE_LOCK"/>
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
```

#### Notification Icon

Create a notification icon at:
- `android/app/src/main/res/drawable/ic_notification.xml`

#### Adhan Sound Files

Add your adhan sound files to:
- `android/app/src/main/res/raw/`

Required sound files:
- `adhan_default.mp3`
- `adhan_fajr.mp3`
- `adhan_mecca.mp3`
- `adhan_madina.mp3`
- `adhan_aqsa.mp3`
- `notification_short.mp3`
- `notification_sound.mp3`

### 3. iOS Setup

#### Podfile Modifications

Make sure your `ios/Podfile` includes the following modifications:

```ruby
post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    
    # Start of the Awesome Notifications configuration
    target.build_configurations.each do |config|
      # Ensure minimum iOS version is set to 12.0 for Awesome Notifications
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
      
      # Fix for the bridging headers issue
      config.build_settings['BUILD_LIBRARY_FOR_DISTRIBUTION'] = 'NO'
      
      # For Awesome Notifications 0.10.x
      if target.name == 'awesome_notifications'
        config.build_settings['ONLY_ACTIVE_ARCH'] = 'NO'
        config.build_settings['VALID_ARCHS'] = 'arm64 arm64e x86_64'
        config.build_settings['SWIFT_OPTIMIZATION_LEVEL'] = '-Onone'
        config.build_settings['ENABLE_BITCODE'] = 'NO'
        config.build_settings['SWIFT_VERSION'] = '5.0'
      end
    end
  end
  
  # This is necessary for Xcode 14
  installer.generated_projects.each do |project|
    project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
      end
    end
  end
end

################  Awesome Notifications pod modification  ###################
awesome_pod_file = File.expand_path(File.join('plugins', 'awesome_notifications', 'ios', 'Scripts', 'AwesomePodFile'), '.symlinks')
if File.exist?(awesome_pod_file)
  require awesome_pod_file
  update_awesome_pod_build_settings(installer)
end
################  Awesome Notifications pod modification  ###################

################  Awesome Notifications pod modification  ###################
awesome_pod_file = File.expand_path(File.join('plugins', 'awesome_notifications', 'ios', 'Scripts', 'AwesomePodFile'), '.symlinks')
if File.exist?(awesome_pod_file)
  require awesome_pod_file
  update_awesome_main_target_settings('Runner', File.dirname(File.realpath(__FILE__)), flutter_root)
end
################  Awesome Notifications pod modification  ###################
```

#### Info.plist

Ensure the following entries are added to your `Info.plist`:

```xml
<key>UIBackgroundModes</key>
<array>
    <string>audio</string>
    <string>fetch</string>
    <string>processing</string>
    <string>remote-notification</string>
</array>
<key>NSCalendarsUsageDescription</key>
<string>We need to access your calendar to schedule prayer reminders</string>
<key>NSLocalNotificationsUsageDescription</key>
<string>We need to send you notifications for prayer times</string>
<key>NSCriticalAlertsUsageDescription</key>
<string>Prayer time notifications may need to alert you even when your phone is in silent mode</string>
```

#### Adhan Sound Files

Add your adhan sound files to the iOS project:
1. Open the iOS project in Xcode
2. Right-click on the Runner folder
3. Select "Add Files to 'Runner'"
4. Add your adhan sound files
5. Make sure "Copy items if needed" is checked
6. Select "Create folder references" (blue folder icon)
7. Click "Add"

### 4. Assets Setup

Create an `assets/audio` directory in your project and add the adhan sound files:
- `assets/audio/adhan_default.mp3`
- `assets/audio/adhan_fajr.mp3`
- `assets/audio/adhan_mecca.mp3`
- `assets/audio/adhan_madina.mp3`
- `assets/audio/adhan_aqsa.mp3`
- `assets/audio/notification_short.mp3`

Update your `pubspec.yaml` to include these assets:

```yaml
flutter:
  assets:
    - assets/
    - assets/audio/
```

### 5. Troubleshooting

#### iOS Build Issues

If you encounter the error "Using bridging headers with module interfaces is unsupported":

1. Open your project in Xcode
2. Select the Runner target
3. Go to Build Settings
4. Set "Build Libraries for Distribution" to NO
5. Set "Only Safe API Extensions" to NO

For the Notification Extension target:
1. Set "Build Libraries for Distribution" to NO
2. Set "Only Safe API Extensions" to YES

#### Android Build Issues

If you encounter issues with Android builds:

1. Make sure your `minSdkVersion` is set to 23 or higher in `android/app/build.gradle`
2. Ensure all permissions are correctly added to the AndroidManifest.xml
3. Check that the notification icon follows Material Design guidelines (transparent background with white foreground)

### 6. Testing

To test the notifications:
1. Go to the Prayer Reminders settings in the app
2. Enable notifications for a prayer that's coming up soon
3. Set the reminder time to 1-2 minutes before the current time
4. Check if the notification appears with the correct adhan sound

## Features

- Prayer Times
- Qiblah Direction
- Quran
- Duas
- Zakat Calculator
- Events
- Announcements
- Prayer Reminders
- And more!

## Getting Started

1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Run `flutter run` to start the app

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
