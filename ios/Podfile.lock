PODS:
  - audioplayers_darwin (0.0.1):
    - Flutter
  - awesome_notifications (0.10.0):
    - Flutter
    - IosAwnCore (~> 0.10.0)
  - connectivity_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_compass_v2 (0.0.1):
    - Flutter
  - flutter_native_timezone (0.0.1):
    - Flutter
  - flutter_qiblah (0.0.1):
    - Flutter
  - flutter_ringtone_player (0.0.1):
    - Flutter
  - flutter_sensors (0.0.1):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - home_widget (0.0.1):
    - Flutter
  - IosAwnCore (0.10.0)
  - location (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sensors_plus (0.0.1):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - workmanager (0.0.1):
    - Flutter

DEPENDENCIES:
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - awesome_notifications (from `.symlinks/plugins/awesome_notifications/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_compass_v2 (from `.symlinks/plugins/flutter_compass_v2/ios`)
  - flutter_native_timezone (from `.symlinks/plugins/flutter_native_timezone/ios`)
  - flutter_qiblah (from `.symlinks/plugins/flutter_qiblah/ios`)
  - flutter_ringtone_player (from `.symlinks/plugins/flutter_ringtone_player/ios`)
  - flutter_sensors (from `.symlinks/plugins/flutter_sensors/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - home_widget (from `.symlinks/plugins/home_widget/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - workmanager (from `.symlinks/plugins/workmanager/ios`)

SPEC REPOS:
  trunk:
    - IosAwnCore

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  awesome_notifications:
    :path: ".symlinks/plugins/awesome_notifications/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  Flutter:
    :path: Flutter
  flutter_compass_v2:
    :path: ".symlinks/plugins/flutter_compass_v2/ios"
  flutter_native_timezone:
    :path: ".symlinks/plugins/flutter_native_timezone/ios"
  flutter_qiblah:
    :path: ".symlinks/plugins/flutter_qiblah/ios"
  flutter_ringtone_player:
    :path: ".symlinks/plugins/flutter_ringtone_player/ios"
  flutter_sensors:
    :path: ".symlinks/plugins/flutter_sensors/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  home_widget:
    :path: ".symlinks/plugins/home_widget/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  workmanager:
    :path: ".symlinks/plugins/workmanager/ios"

SPEC CHECKSUMS:
  audioplayers_darwin: 877d9a4d06331c5c374595e46e16453ac7eafa40
  awesome_notifications: dd5518ff1c80be03d4f1c40f04da9d9cc2a37af5
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_compass_v2: f7704b078814b705e76ae2bd48fe48ae6cd067df
  flutter_native_timezone: 5f05b2de06c9776b4cc70e1839f03de178394d22
  flutter_qiblah: f69eeb708d127a2634073ab1f27771c979928f50
  flutter_ringtone_player: 15eba85187230b87b2512f0e1b92225618bc03e7
  flutter_sensors: dc6eed3291e7724bf8f4138d7d14c4071b42bd29
  geolocator_apple: 9bcea1918ff7f0062d98345d238ae12718acfbc1
  home_widget: 0434835a4c9a75704264feff6be17ea40e0f0d57
  IosAwnCore: 653786a911089012092ce831f2945cd339855a89
  location: d5cf8598915965547c3f36761ae9cc4f4e87d22e
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  sensors_plus: 18a9b346c43e157da17d2c8e99def703f9efb9d8
  share_plus: 8b6f8b3447e494cca5317c8c3073de39b3600d1f
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  workmanager: 0afdcf5628bbde6924c21af7836fed07b42e30e6

PODFILE CHECKSUM: dab00c55f3a8b247e7b87f4d1c9d4288c2a82220

COCOAPODS: 1.15.2
