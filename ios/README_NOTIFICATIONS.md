# iOS Notification Setup Guide

## Fixing Bridging Headers Issue

If you encounter the error:
```
Using bridging headers with module interfaces is unsupported
```

Follow these steps to fix it:

### 1. Update Podfile

The Podfile has been updated with the necessary post-install hooks to set the correct build settings. The key settings are:

For the Runner target:
- `BUILD_LIBRARY_FOR_DISTRIBUTION` = `NO`
- `ONLY_SAFE_API_EXTENSIONS` = `NO` (implicit in our configuration)

For any Notification Extension targets:
- `BUILD_LIBRARY_FOR_DISTRIBUTION` = `NO`
- `ONLY_SAFE_API_EXTENSIONS` = `YES` (via `APPLICATION_EXTENSION_API_ONLY` = `YES`)

### 2. Manual Xcode Configuration (if needed)

If you still encounter issues, you can manually set these in Xcode:

1. Open your project in Xcode: `open ios/Runner.xcworkspace`
2. Select the Runner target
3. Go to Build Settings
4. Search for "Build Libraries for Distribution" and set it to "No"
5. Search for "Only Safe API Extensions" and set it to "No"
6. If you have a Notification Extension target:
   - Select that target
   - Set "Build Libraries for Distribution" to "No"
   - Set "Only Safe API Extensions" to "Yes"

### 3. Clean and Rebuild

After making these changes:

1. Clean the build folder:
   ```
   flutter clean
   ```

2. Update pods:
   ```
   cd ios
   pod deintegrate
   pod install
   cd ..
   ```

3. Rebuild the app:
   ```
   flutter run
   ```

## Troubleshooting

If you still encounter issues:

1. Make sure you're using Awesome Notifications 0.10.1 or later
2. Check that your minimum iOS deployment target is set to iOS 12.0 or higher
3. Ensure that all pods are properly installed and up to date
4. Try deleting the derived data folder in Xcode
5. Make sure your Flutter and CocoaPods versions are up to date 